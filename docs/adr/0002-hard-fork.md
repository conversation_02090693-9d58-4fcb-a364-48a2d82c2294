# 2. Hard Fork

Date: 2025-07-28

## Status

Accepted

## Context

This project was originally forked from version 0.5.3 of Open WebUI. We initially attempted to track upstream Open WebUI changes as closely as possible and contribute changes back to the upstream repository. To this end, we introduced [ADR 1](0001-ui-customization.md), which was intended to help us differentiate between our customizations and changes that we could contribute upstream, and allow us to more easily merge in upstream changes without conflicts. Since then, our needs and UI have deviated significantly from the upstream project, making it unrealistic to attempt to keep the two in sync.

## Decision

We are removing the upstream overrides and essentially taking a hard fork approach.

## Consequences

We will now be able to make changes to the app without having to take into consideration what should be considered an override versus what can be merged upstream. We will need to add new features and fix issues on our own as opposed to merging them in from upstream.

## Alternatives considered

We considered continuing the upstream overrides approach.
