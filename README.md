# USAi Chat

The primary goal of USAi is to enable federal agencies to experiment with artificial intelligence (AI) in a secure, FedRAMP-compliant environment. By providing access to base models from leading AI companies and offering advanced UI features, it empowers agencies to test and validate new AI use cases efficiently.

## Features

- **FedRAMP-Approved Access**: Secure access to AI base models from multiple leading providers.
- **Prompt Playground**: A user-friendly interface for tweaking and optimizing system prompts.
- **Document Upload & Management**: Upload documents, tag collections, and reference them in chat using the `#` character.
- **Custom Models**: Create custom AI models by combining different system prompts, base models, and knowledge bases (via document upload and collection tagging).
- **Efficient Testing**: Rapidly test AI product offerings against real-world government use cases.
- **Web search**: Search the internet for information in real time.

## Mission

The USAi Chat is designed to help federal agencies quickly and cost-effectively:

1. Continuously test the latest AI market offerings against real-world government use cases in a secure environment.
2. Enable smart, modular technology procurement based on value proven through real-world use.
3. Quickly discover and clarify agency AI needs, potential value, and risks.
4. Enable use case owners to check assumptions early and often during use case development.

## How to get access

The project team is working on publishing an easy-to-consume containerized version of the app to the AWS and AWS GovCloud marketplaces, see #contributing below.

## Local Installation

Developers: detailed installation instructions are available in the [LOCALDEV.md](./LOCALDEV.md) file.

## Acknowledgments

We would like to acknowledge the open-source project **[Open WebUI](https://github.com/openwebui)** for providing the underlying framework that powers this project. Their work has been instrumental in building a robust and flexible platform.

## Contributing

We are actively seeking contributions from government and civic tech developers to help build a comprehensive security and privacy compliance wrapper around the Open WebUI project. We aim to ensure that government and non-profits with stringent compliance requirements can continuously benefit from the exceptional work being done by the Open WebUI community.

If you are interested in contributing, please contact us using the details below.

## Contact

Government/NGO/Academic developers and program officials can reach out to **[<EMAIL>](mailto:<EMAIL>)** for more information.
