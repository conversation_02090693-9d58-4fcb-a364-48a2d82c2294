{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' ou '-1' para nenhuma expiração.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "", "(e.g. `sh webui.sh --api`)": "(por exemplo, `sh webui.sh --api`)", "(latest)": "(mais recente)", "{{ models }}": "{{ modelos }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "{{user}}'s Chats", "{{webUIName}} Backend Required": "{{webUIName}} Backend Necessário", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Um modelo de tarefa é usado ao executar tarefas como gerar títulos para bate-papos e consultas de pesquisa na Web", "a user": "um utilizador", "About": "Acerca de", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "Conta", "Account Activation Pending": "Ativação da Conta Pendente", "Actions": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "Utilizadores Ativos", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Add a model ID": "", "Add a short description about what this model does": "Adicione uma breve descrição sobre o que este modelo faz", "Add a tag": "Adici<PERSON>r uma tag", "Add Arena Model": "", "Add Connection": "", "Add Content": "", "Add content here": "", "Add custom prompt": "Adicionar um prompt curto", "Add Files": "<PERSON><PERSON><PERSON><PERSON>", "Add Group": "", "Add Memory": "Adicionar <PERSON>", "Add Model": "Adicionar <PERSON>o", "Add Reaction": "", "Add Tag": "", "Add Tags": "adicionar tags", "Add text content": "", "Add User": "<PERSON><PERSON><PERSON><PERSON>", "Add User Group": "", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "Ajustar essas configurações aplicará alterações universalmente a todos os utilizadores.", "admin": "administrador", "Admin": "Admin", "Admin Panel": "Painel do Administrador", "Admin Settings": "Configurações do Administrador", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "", "Advanced Parameters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Advanced Params": "Params Avançados", "All Documents": "Todos os Documentos", "All models deleted successfully": "", "Allow Chat Delete": "", "Allow Chat Deletion": "Permitir <PERSON> de Conversa", "Allow Chat Edit": "", "Allow File Upload": "", "Allow non-local voices": "<PERSON><PERSON><PERSON> vozes não locais", "Allow Temporary Chat": "", "Allow User Location": "", "Allow Voice Interruption in Call": "", "Allowed Endpoints": "", "Already have an account?": "Já tem uma conta?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "um assistente", "and": "e", "and {{COUNT}} more": "", "and create a new shared link.": "e criar um novo link partilhado.", "api": "", "API Base URL": "URL Base da API", "API Key": "<PERSON><PERSON> da <PERSON>", "API Key created.": "<PERSON>ve da <PERSON> criada.", "API Key Endpoint Restrictions": "", "API keys": "<PERSON><PERSON> da <PERSON>", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "Abril", "Archive": "Arquivo", "Archive All Chats": "<PERSON><PERSON><PERSON><PERSON> todos os chats", "Archived Chats": "Conversas arquivadas", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "Tem a certeza?", "Arena Models": "", "Artifacts": "", "Ask a question": "", "Assistant": "", "Attach file": "<PERSON><PERSON><PERSON>", "Attribute for Username": "", "Audio": "<PERSON><PERSON><PERSON>", "August": "Agosto", "Authenticate": "", "Auto-Copy Response to Clipboard": "Cópia Automática da Resposta para a Área de Transferência", "Auto-playback response": "Reprodução automática da resposta", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "", "AUTOMATIC1111 Base URL": "URL Base do AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "O URL Base do AUTOMATIC1111 é obrigatório.", "Available list": "", "available!": "disponível!", "Azure AI Speech": "", "Azure Region": "", "Back": "Voltar", "Bad": "", "Bad Response": "Resposta má", "Banners": "Estandartes", "Base Model (From)": "Modelo Base (De)", "Batch Size (num_batch)": "", "before": "antes", "Beta": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "<PERSON><PERSON> da <PERSON> de Pesquisa Brave", "By {{name}}": "", "Bypass SSL verification for Websites": "Ignorar verificação SSL para sites", "Call": "<PERSON><PERSON>", "Call feature is not supported when using Web STT engine": "A funcionalide de Chamar não é suportada quando usa um motor Web STT", "Camera": "Camera", "Cancel": "<PERSON><PERSON><PERSON>", "Capabilities": "Capacidades", "Capture": "", "Certificate Path": "", "Change Password": "<PERSON><PERSON><PERSON>", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "chat": "", "Chat": "Conversa", "Chat Background Image": "", "Chat Bubble UI": "Bolha UI da Conversa", "Chat Controls": "", "Chat direction": "Direção da Conversa", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "Conversas", "Check Again": "Verifique novamente", "Check for updates": "Verificar atualizações", "Checking for updates...": "Verificando atualizações...", "Choose a model before saving...": "Escolha um modelo antes de guardar...", "Chunk Overlap": "Sobreposição de Fragmento", "Chunk Params": "Parâmetros de Fragmento", "Chunk Size": "Tamanho do Fragmento", "Ciphers": "", "Citation": "Citação", "Clear memory": "<PERSON><PERSON>", "click here": "", "Click here for filter guides.": "", "Click here for help.": "Clique aqui para obter ajuda.", "Click here to": "Clique aqui para", "Click here to download user import template file.": "", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to select": "Clique aqui para selecionar", "Click here to select a csv file.": "Clique aqui para selecionar um ficheiro csv.", "Click here to select a py file.": "Clique aqui para selecionar um ficheiro py", "Click here to upload a workflow.json file.": "", "click here.": "clique aqui.", "Click on the user role button to change a user's role.": "Clique no botão de função do utilizador para alterar a função de um utilizador.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "", "Clone": "Clonar", "Close": "<PERSON><PERSON><PERSON>", "Code execution": "", "Code formatted successfully": "", "Collection": "Coleção", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "URL Base do ComfyUI", "ComfyUI Base URL is required.": "O URL Base do ComfyUI é obrigatório.", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "Comand<PERSON>", "Completions": "", "Concurrent Requests": "Solicitações simultâneas", "Configure": "", "Configure Models": "", "Confirm": "", "Confirm Password": "Confirmar <PERSON>", "Confirm your action": "", "Confirm your new password": "", "Connections": "Conexões", "console": "", "Contact Admin for WebUI Access": "Contatar Admin para acesso ao WebUI", "Content": "<PERSON><PERSON><PERSON><PERSON>", "Content Extraction": "", "Context Length": "Comprimento do Contexto", "Continue Response": "Contin<PERSON><PERSON> resposta", "Continue with {{provider}}": "", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Controls": "", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "", "Copied shared chat URL to clipboard!": "URL de Conversa partilhado copiada com sucesso!", "Copied to clipboard": "", "Copy": "Copiar", "Copy last code block": "Copiar último bloco de código", "Copy last response": "Copiar última resposta", "Copy Link": "Copiar link", "Copy to clipboard": "", "Copying to clipboard was successful!": "Cópia para a área de transferência bem-sucedida!", "Create": "", "Create a knowledge base": "", "Create a model": "<PERSON><PERSON>r um modelo", "Create Account": "<PERSON><PERSON><PERSON>", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "", "Create new key": "Criar nova chave", "Create new secret key": "Criar nova chave secreta", "Created at": "C<PERSON><PERSON> em", "Created At": "C<PERSON><PERSON> em", "Created by": "", "CSV Import": "", "Current Model": "<PERSON><PERSON>", "Current Password": "<PERSON><PERSON>", "Custom": "Personalizado", "Dark": "Escuro", "Database": "Base de dados", "December": "Dezembro", "Default": "Padrão", "Default (Open AI)": "", "Default (SentenceTransformers)": "Padrão (SentenceTransformers)", "Default Model": "<PERSON><PERSON>", "Default model updated": "<PERSON><PERSON> atualizado", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "Sugestões de Prompt Padrão", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "Função de Utilizador Padrão", "Delete": "<PERSON><PERSON><PERSON>", "Delete a model": "<PERSON><PERSON><PERSON> um modelo", "Delete All Chats": "<PERSON><PERSON><PERSON> to<PERSON> as conversas", "Delete All Models": "", "Delete chat": "Apagar conversa", "Delete Chat": "Apagar Conversa", "Delete chat?": "", "Delete folder?": "", "Delete function?": "", "Delete Message": "", "Delete prompt?": "", "delete this link": "apagar este link", "Delete tool?": "", "Delete User": "<PERSON><PERSON><PERSON> Util<PERSON>", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} a<PERSON><PERSON>", "Deleted {{name}}": "<PERSON><PERSON><PERSON> {{name}}", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "Descrição", "Disabled": "", "discover": "", "Discover a function": "", "Discover a model": "Descubra um modelo", "Discover a prompt": "Descobrir um prompt", "Discover a tool": "", "Discover wonders": "", "Discover, download, and explore custom functions": "", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON>, des<PERSON><PERSON><PERSON> e explore prompts personalizados", "Discover, download, and explore custom tools": "", "Discover, download, and explore model presets": "Des<PERSON><PERSON>, descarregue e explore predefinições de modelo", "Dismissible": "Dispensável", "Display": "", "Display Emoji in Call": "", "Display the username instead of You in the Chat": "Exibir o nome de utilizador em vez de Você na Conversa", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "", "Do not install tools from sources you do not fully trust.": "", "Document": "Documento", "Documentation": "Documentação", "Documents": "Documentos", "does not make any external connections, and your data stays securely on your locally hosted server.": "não faz conexões externas e os seus dados permanecem seguros no seu servidor alojado localmente.", "Don't have an account?": "Não tem uma conta?", "don't install random functions from sources you don't trust.": "", "don't install random tools from sources you don't trust.": "", "Done": "", "Download": "<PERSON><PERSON><PERSON><PERSON>", "Download canceled": "Download cancelado", "Download Database": "Descarregar Base de Dados", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to add to the conversation": "Largue os ficheiros aqui para adicionar à conversa", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "por exemplo, '30s', '10m'. Unidades de tempo válidas são 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "<PERSON><PERSON>", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "", "Edit User": "<PERSON><PERSON>", "Edit User Group": "", "ElevenLabs": "", "Email": "E-mail", "Embark on adventures": "", "Embedding Batch Size": "Tamanho do Lote do Embedding", "Embedding Model": "Modelo de Embedding", "Embedding Model Engine": "Motor de Modelo de Embedding", "Embedding model set to \"{{embedding_model}}\"": "Modelo de Embedding definido como \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "Active a Partilha da Comunidade", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "Ativar Novas Inscrições", "Enable Web Search": "Ativar pesquisa na Web", "Enabled": "", "Engine": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Confirme que o seu ficheiro CSV inclui 4 colunas nesta ordem: Nome, E-mail, Senha, Função.", "Enter {{role}} message here": "Escreva a mensagem de {{role}} aqui", "Enter a detail about yourself for your LLMs to recall": "Escreva um detalhe sobre você para que os seus LLMs possam lembrar-se", "Enter api auth string (e.g. username:password)": "", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "Escreva a chave da API do Brave Search", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "Escreva a Sobreposição de Fragmento", "Enter Chunk Size": "Escreva o Tamanho do Fragmento", "Enter description": "", "Enter Github Raw URL": "Escreva o URL cru do Github", "Enter Google PSE API Key": "Escreva a chave da API PSE do Google", "Enter Google PSE Engine Id": "Escreva o ID do mecanismo PSE do Google", "Enter Image Size (e.g. 512x512)": "Escreva o Tamanho da Imagem (por exemplo, 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "Escreva os códigos de idioma", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "Escreva a tag do modelo (por exemplo, {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "Escreva o Número de Etapas (por exemplo, 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "Escreva a Pontuação", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "Escreva o URL da Pesquisa Searxng", "Enter Seed": "", "Enter Serper API Key": "Escreva a chave da API Serper", "Enter Serply API Key": "", "Enter Serpstack API Key": "Escreva a chave da <PERSON>ps<PERSON>ck", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "Escreva a sequência de paragem", "Enter system prompt": "", "Enter Tavily API Key": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "", "Enter Top K": "Escreva o Top K", "Enter URL (e.g. http://127.0.0.1:7860/)": "Escreva o URL (por exemplo, http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Escreva o URL (por exemplo, http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "Escreva o seu E-mail", "Enter Your Full Name": "Escreva o seu Nome Completo", "Enter your message": "", "Enter your new password": "", "Enter Your Password": "Escreva a sua Senha", "Enter your prompt": "", "Enter Your Role": "Escreva a sua Função", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "Erro", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "", "Experimental": "Experimental", "Explore the cosmos": "", "Export": "Exportar", "Export All Archived Chats": "", "Export All Chats (All Users)": "Exporta<PERSON> as <PERSON><PERSON><PERSON> (Todos os Utilizadores)", "Export chat (.json)": "Exportar Conversa (.json)", "Export Chats": "Exportar Conversas", "Export Config to JSON File": "", "Export Functions": "", "Export Models": "Modelos de Exportação", "Export Presets": "", "Export Prompts": "Exportar Prompts", "Export to CSV": "", "Export Tools": "", "External Models": "Modelos Externos", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "", "Failed to create API Key.": "Falha ao criar a Chave da <PERSON>.", "Failed to read clipboard contents": "Falha ao ler o conteúdo da área de transferência", "Failed to save models configuration": "", "Failed to update settings": "Falha ao atualizar as definições", "February": "<PERSON><PERSON>", "Feedback History": "", "Feedbacks": "", "File": "", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "<PERSON><PERSON>", "File not found.": "Ficheiro não encontrado.", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File uploaded successfully": "", "Files": "", "Filter is now globally disabled": "", "Filter is now globally enabled": "", "Filters": "", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Detectada falsificação da impressão digital: Não é possível usar iniciais como avatar. A usar a imagem de perfil padrão.", "Fluidly stream large external response chunks": "Transmita com fluidez grandes blocos de resposta externa", "Focus chat input": "Focar na conversa", "Folder deleted successfully": "", "Folder name cannot be empty": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Forge new paths": "", "Form": "", "Format your variables using brackets like this:": "", "Frequency Penalty": "Penalidade de Frequência", "Function": "", "Function created successfully": "", "Function deleted successfully": "", "Function Description": "", "Function ID": "", "Function is now globally disabled": "", "Function is now globally enabled": "", "Function Name": "", "Function updated successfully": "", "Functions": "", "Functions allow arbitrary code execution": "", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "", "General": "G<PERSON>", "General Settings": "Configurações Gerais", "Generate Image": "<PERSON><PERSON><PERSON> imagem", "Generating search query": "A gerar a consulta da pesquisa", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "", "Good Response": "Boa Resposta", "Google Drive": "", "Google PSE API Key": "Chave da API PSE do Google", "Google PSE Engine Id": "ID do mecanismo PSE do Google", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "h:mm a": "h:mm a", "Haptic Feedback": "", "Harmful or offensive": "", "has no conversations.": "não possui conversas.", "Hello, {{name}}": "<PERSON><PERSON><PERSON>, {{name}}", "Help": "<PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "Ocultar", "Host": "", "How can I help you today?": "Como posso ajudá-lo hoje?", "How would you rate this response?": "", "Hybrid Search": "Pesquisa Híbrida", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "Geração de Imagens (Experimental)", "Image Generation Engine": "Mecanismo de Geração de Imagens", "Image Max Compression Size": "", "Image Settings": "Configuraç<PERSON><PERSON> da Imagem", "Images": "Imagens", "Import Chats": "Importar Conversas", "Import Config from JSON File": "", "Import Functions": "", "Import Models": "Importar Modelos", "Import Presets": "", "Import Prompts": "Importar Prompts", "Import Tools": "", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "Inclua a flag `--api` ao executar stable-diffusion-webui", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "Informação", "Input commands": "Comandos de entrada", "Install from Github URL": "Instalar a partir do URL do Github", "Instant Auto-Send After Voice Transcription": "Enviar automaticamente depois da transcrição da voz", "Interface": "Interface", "Invalid file format.": "", "Invalid Tag": "Etiqueta <PERSON>álid<PERSON>", "is typing...": "", "January": "Janeiro", "Jina API Key": "", "join our Discord for help.": "junte-se ao nosso Discord para obter ajuda.", "JSON": "JSON", "JSON Preview": "Pré-visualização JSON", "July": "<PERSON><PERSON>", "June": "<PERSON><PERSON>", "JWT Expiration": "Expiração JWT", "JWT Token": "Token JWT", "Kagi Search API Key": "", "Keep Alive": "Manter Vivo", "Key": "", "Keyboard shortcuts": "Atalhos de teclado", "Knowledge": "Conhecimento", "Knowledge Access": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Label": "", "Landing Page Mode": "", "Language": "Idioma", "Last Active": "Último Ativo", "Last Modified": "", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Light": "<PERSON><PERSON><PERSON>", "Listening...": "A escutar...", "Local": "", "Local Models": "Modelos Lo<PERSON>is", "Lost": "", "LTR": "LTR", "Made by OpenWebUI Community": "<PERSON>ito pela Comunidade OpenWebUI", "Make sure to enclose them with": "Certifique-se de colocá-los entre", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "<PERSON><PERSON><PERSON>", "Manage Arena Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "Gerir pipelines", "March": "Março", "Max Tokens (num_predict)": "<PERSON><PERSON><PERSON> (num_predict)", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "O máximo de 3 modelos podem ser descarregados simultaneamente. Tente novamente mais tarde.", "May": "<PERSON><PERSON>", "Memories accessible by LLMs will be shown here.": "Memórias acessíveis por LLMs serão mostradas aqui.", "Memory": "Memória", "Memory added successfully": "", "Memory cleared successfully": "", "Memory deleted successfully": "", "Memory updated successfully": "", "Merge Responses": "", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Mensagens que você enviar após criar o seu link não serão partilhadas. Os utilizadores com o URL poderão visualizar a conversa partilhada.", "Min P": "", "Minimum Score": "<PERSON><PERSON><PERSON>", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "DD/MM/YYYY", "MMMM DD, YYYY HH:mm": "DD/MM/YYYY HH:mm", "MMMM DD, YYYY hh:mm:ss A": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "O modelo '{{modelName}}' foi descarregado com sucesso.", "Model '{{modelTag}}' is already in queue for downloading.": "O modelo '{{modelTag}}' já está na fila para descarregar.", "Model {{modelId}} not found": "Modelo {{modelId}} não foi encontrado", "Model {{modelName}} is not vision capable": "O modelo {{modelName}} não é capaz de visão", "Model {{name}} is now {{status}}": "Modelo {{name}} agora é {{status}}", "Model accepts image inputs": "", "Model created successfully!": "", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Dtectado caminho do sistema de ficheiros do modelo. É necessário o nome curto do modelo para atualização, não é possível continuar.", "Model Filtering": "", "Model ID": "ID do modelo", "Model IDs": "", "Model Name": "", "Model not selected": "Modelo não selecionado", "Model Params": "Params Modelo", "Model Permissions": "", "Model updated successfully": "", "Modelfile Content": "Conteúdo do Ficheiro do Modelo", "Models": "Modelos", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "", "More": "<PERSON><PERSON>", "Name": "Nome", "Name your knowledge base": "", "New Chat": "Nova Conversa", "New folder": "", "New Password": "Nova Senha", "new-channel": "", "No content found": "", "No content to speak": "", "No distance available": "", "No feedbacks found": "", "No file selected": "", "No files found.": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No knowledge found": "", "No model IDs": "", "No models found": "", "No models selected": "", "No results found": "Não foram encontrados resultados", "No search query generated": "Não foi gerada nenhuma consulta de pesquisa", "No source available": "Nenhuma fonte disponível", "No users were found.": "", "No valves to update": "", "None": "<PERSON><PERSON><PERSON>", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Nota: Se você definir uma pontuação mínima, a pesquisa só retornará documentos com uma pontuação maior ou igual à pontuação mínima.", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Notificações da Área de Trabalho", "November": "Novembro", "num_gpu (Ollama)": "", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "", "October": "Out<PERSON>ro", "Off": "Des<PERSON><PERSON>", "Okay, Let's Go!": "Ok, Vamos Lá!", "OLED Dark": "OLED Escuro", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API disabled": "API do Ollama desativada", "Ollama API settings updated": "", "Ollama Version": "Versão do Ollama", "On": "Ligado", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "Apenas caracteres alfanuméricos e hífens são permitidos na string de comando.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Epá! Parece que o URL é inválido. Verifique novamente e tente outra vez.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Epá! Você está a usar um método não suportado (somente frontend). Por favor, sirva o WebUI a partir do backend.", "Open in full screen": "", "Open new chat": "Abrir nova conversa", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "OpenAI", "OpenAI API": "API OpenAI", "OpenAI API Config": "Configuração da API OpenAI", "OpenAI API Key is required.": "A Chave da API OpenAI é obrigatória.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "URL/Chave da API OpenAI é necessária.", "or": "ou", "Organize your users": "", "OUTPUT": "", "Output format": "", "Overview": "", "page": "", "Password": "<PERSON><PERSON>", "Paste Large Text as File": "", "PDF document (.pdf)": "Documento PDF (.pdf)", "PDF Extract Images (OCR)": "Extrair Imagens de PDF (OCR)", "pending": "pendente", "Permission denied when accessing media devices": "A permissão foi negada ao aceder aos dispositivos de media", "Permission denied when accessing microphone": "A permissão foi negada ao aceder ao microfone", "Permission denied when accessing microphone: {{error}}": "A permissão foi negada ao aceder o microfone: {{error}}", "Permissions": "", "Personalization": "Personalização", "Pin": "", "Pinned": "", "Pioneer insights": "", "Pipeline deleted successfully": "", "Pipeline downloaded successfully": "", "Pipelines": "<PERSON><PERSON><PERSON>", "Pipelines Not Detected": "", "Pipelines Valves": "Válvulas de Condutas", "Plain text (.txt)": "Texto sem formatação (.txt)", "Playground": "<PERSON><PERSON><PERSON>", "Please carefully review the following warnings:": "", "Please enter a prompt": "", "Please fill in all fields.": "", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "Últimos 30 dias", "Previous 7 days": "Últimos 7 dias", "Profile Image": "<PERSON><PERSON>", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (ex.: Dê-me um facto divertido sobre o Império <PERSON>)", "Prompt Content": "Conteúdo do Prompt", "Prompt created successfully": "", "Prompt suggestions": "Sugestões de Prompt", "Prompt updated successfully": "", "Prompts": "Prompts", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "Puxar \"{{searchValue}}\" do Ollama.com", "Pull a model from Ollama.com": "Puxar um modelo do Ollama.com", "Query Generation Prompt": "", "Query Params": "Parâmetros de Consulta", "RAG Template": "Modelo RAG", "Rating": "", "Re-rank models by topic similarity": "", "Read Aloud": "Ler <PERSON> Voz Alta", "Record voice": "<PERSON><PERSON><PERSON> voz", "Redirecting you to OpenWebUI Community": "Redirecionando-o para a Comunidade OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Refera-se a si próprio como \"User\" (por exemplo, \"User está a aprender Espanhol\")", "References from": "", "Refresh Token Expiration": "", "Regenerate": "<PERSON><PERSON><PERSON>", "Release Notes": "Notas de Lançamento", "Relevance": "", "Remove": "Remover", "Remove Model": "Remover Modelo", "Rename": "Renomear", "Reorder Models": "", "Repeat Last N": "Repetir <PERSON>ltimos N", "Reply in Thread": "", "Request Mode": "Modo de Pedido", "Reranking Model": "<PERSON><PERSON>", "Reranking model disabled": "<PERSON><PERSON> de Reranking desativado", "Reranking model set to \"{{reranking_model}}\"": "Modelo de Reranking definido como \"{{reranking_model}}\"", "Reset": "", "Reset All Models": "", "Reset Upload Directory": "Limpar Pasta de Carregamento", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "", "Response splitting": "", "Result": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "Função", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "", "Running": "A correr", "Save": "Guardar", "Save & Create": "Guardar e Criar", "Save & Update": "Guardar e Atualizar", "Save As Copy": "", "Save Tag": "", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Guardar o registo das conversas diretamente no armazenamento do seu navegador já não é suportado. Reserve um momento para descarregar e eliminar os seus registos de conversas clicando no botão abaixo. Não se preocupe, você pode facilmente reimportar os seus registos de conversas para o backend através de", "Scroll to bottom when switching between branches": "", "Search": "<PERSON><PERSON><PERSON><PERSON>", "Search a model": "Pesquisar um modelo", "Search Base": "", "Search Chats": "<PERSON><PERSON><PERSON><PERSON>", "Search Collection": "", "Search Filters": "", "search for tags": "", "Search Functions": "", "Search Knowledge": "", "Search Models": "Modelos de pesquisa", "Search options": "", "Search Prompts": "Pesquisar Prompts", "Search Result Count": "Contagem de resultados da pesquisa", "Search Tools": "", "Search users": "", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searxng Query URL": "URL de consulta Searxng", "See readme.md for instructions": "Consulte readme.md para obter instruções", "See what's new": "Veja o que há de novo", "Seed": "Semente", "Select a base model": "Selecione um modelo base", "Select a engine": "Selecione um motor", "Select a function": "", "Select a group": "", "Select a model": "Selecione um modelo", "Select a pipeline": "Selecione um pipeline", "Select a pipeline url": "Selecione um URL de pipeline", "Select a tool": "", "Select Engine": "", "Select Knowledge": "", "Select model": "Selecione o modelo", "Select only one model to call": "Selecione apenas um modelo para a chamada", "Selected model(s) do not support image inputs": "O(s) modelo(s) selecionado(s) não suporta(m) entradas de imagem", "Semantic distance to query": "", "Send": "Enviar", "Send a message": "", "Send message": "Enviar mensagem", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "Setembro", "Serper API Key": "Chave API Serper", "Serply API Key": "", "Serpstack API Key": "<PERSON><PERSON> da <PERSON>", "Server connection verified": "Conexão com o servidor verificada", "Set as default": "Definir como padrão", "Set CFG Scale": "", "Set Default Model": "Definir Modelo <PERSON>", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "Definir modelo de vetorização (ex.: {{model}})", "Set Image Size": "<PERSON><PERSON><PERSON>", "Set reranking model (e.g. {{model}})": "Definir modelo de reranking (ex.: {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "Definir Etapas", "Set Task Model": "Definir modelo de tarefa", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "<PERSON><PERSON><PERSON>", "Set whisper model": "", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "Configurações", "Settings saved successfully!": "Configurações guardadas com sucesso!", "Share": "Partilhar", "Share Chat": "<PERSON><PERSON><PERSON> Con<PERSON>", "Share to OpenWebUI Community": "Partilhar com a Comunidade OpenWebUI", "Show": "Mostrar", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "Mostrar Detalhes do Administrador na sobreposição de Conta Pendente", "Show shortcuts": "<PERSON><PERSON> atalhos", "Show your support!": "", "Sign in": "Entrar", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "<PERSON><PERSON>", "Sign up": "Inscrever-se", "Sign up to {{WEBUI_NAME}}": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Source": "Fonte", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "Erro de reconhecimento de fala: {{error}}", "Speech-to-Text Engine": "Motor de Fala para Texto", "Stop": "", "Stop Sequence": "Sequência de Paragem", "Stream Chat Response": "", "STT Model": "Modelo STT", "STT Settings": "Configurações STT", "Success": "Sucesso", "Successfully updated.": "Atualizado com sucesso.", "Support": "", "Support this plugin:": "", "Sync directory": "", "System": "Sistema", "System Instructions": "", "System Prompt": "Prompt do Sistema", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "", "Tavily API Key": "", "Temperature": "Temperatura", "Template": "<PERSON><PERSON>", "Temporary Chat": "", "Text Splitter": "", "Text-to-Speech Engine": "Motor de Texto para Fala", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "<PERSON><PERSON><PERSON> pelo seu feedback!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "A pontuação deve ser um valor entre 0.0 (0%) e 1.0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "<PERSON><PERSON>", "Thinking...": "A pensar...", "This action cannot be undone. Do you wish to continue?": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "<PERSON><PERSON> garante que suas conversas valiosas sejam guardadas com segurança na sua base de dados de backend. Obrigado!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Isto é um recurso experimental, pode não funcionar conforme o esperado e está sujeito a alterações a qualquer momento.", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Tika": "", "Tika Server URL required.": "", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Dica: Atualize vários slots de variáveis consecutivamente pressionando a tecla Tab na entrada da conversa após cada substituição.", "Title": "<PERSON><PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "<PERSON><PERSON><PERSON><PERSON> (ex.: Diz-me um facto divertido)", "Title Auto-Generation": "Geração Automática de Título", "Title cannot be an empty string.": "T<PERSON><PERSON>lo não pode ser uma string vazia.", "Title Generation Prompt": "Prompt de Geração de Título", "TLS": "", "To access the available model names for downloading,": "Para aceder aos nomes de modelo disponíveis para descarregar,", "To access the GGUF models available for downloading,": "Para aceder aos modelos GGUF disponíveis para descarregar,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Para aceder ao WebUI, entre em contato com o administrador. Os administradores podem gerir o status dos utilizadores no Painel de Administração.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "", "To select toolkits here, add them to the \"Tools\" workspace first.": "", "Toast notifications for new updates": "", "Today": "Hoje", "Toggle settings": "Alternar configurações", "Toggle sidebar": "Alternar barra lateral", "Toggle temporary chat": "", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "", "Tool created successfully": "", "Tool deleted successfully": "", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "", "Tool Name": "", "Tool updated successfully": "", "Tools": "", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Top K": "Top K", "Top P": "Top P", "Transformers": "", "Trouble accessing Ollama?": "Problemas a aceder ao Ollama?", "TTS Model": "Modelo TTS", "TTS Settings": "Configurações TTS", "TTS Voice": "Voz TTS", "Type": "Tipo", "Type Hugging Face Resolve (Download) URL": "Escreva o URL do Hugging Face Resolve (Descarregar)", "Uh-oh! There was an issue with the response.": "", "UI": "", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "", "Unravel secrets": "", "Untagged": "", "Update": "", "Update and Copy Link": "Atualizar e Copiar Link", "Update for the latest features and improvements.": "", "Update password": "<PERSON><PERSON><PERSON><PERSON>", "Updated": "", "Updated at": "", "Updated At": "", "Upload": "", "Upload a GGUF model": "Carregar um modelo GGUF", "Upload directory": "", "Upload files": "", "Upload Files": "<PERSON><PERSON><PERSON>", "Upload Pipeline": "<PERSON>egar <PERSON>", "Upload Progress": "Progresso do Carregamento", "URL": "", "URL Mode": "Modo de URL", "USAi Chat can make mistakes. Review all responses for accuracy. Your agency’s AI and privacy policies apply.": "", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "<PERSON><PERSON>", "Use groups to group your users and assign permissions.": "", "Use Initials": "Usar Iniciais", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "utilizador", "User": "", "User location successfully retrieved.": "", "Username": "", "Users": "Utilizadores", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "<PERSON><PERSON><PERSON><PERSON>", "Valid time units:": "Unidades de tempo válidas:", "Valves": "", "Valves updated": "", "Valves updated successfully": "", "variable": "<PERSON><PERSON><PERSON><PERSON>", "variable to have them replaced with clipboard content.": "variável para que sejam substituídos pelo conteúdo da área de transferência.", "Version": "Vers<PERSON>", "Version {{selectedVersion}} of {{totalVersions}}": "", "Very bad": "", "View Replies": "", "Visibility": "", "Voice": "", "Voice Input": "", "Warning": "Aviso", "Warning:": "", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Aviso: Se você atualizar ou alterar o seu modelo de vetorização, você tem de reimportar todos os documentos.", "Web": "Web", "Web API": "Web API", "Web Loader Settings": "Configurações do Carregador da Web", "Web Search": "Pesquisa na Web", "Web Search Engine": "Motor de Pesquisa Web", "Web Search Query Generation": "", "Webhook URL": "URL do Webhook", "WebUI Settings": "Configurações WebUI", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "Welcome, {{name}}!": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "O que há de novo em", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "<PERSON><PERSON><PERSON> (Local)", "Widescreen Mode": "Modo Widescreen", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "Espaço de Trabalho", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "Escreva uma sugestão de prompt (por exemplo, Quem és tu?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Escreva um resumo em 50 palavras que resuma [tópico ou palavra-chave].", "Write something...": "", "Write your model template content here": "", "Yesterday": "Ontem", "You": "Você", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Você pode personalizar as suas interações com LLMs adicionando memórias através do botão ‘Gerir’ abaixo, tornando-as mais <PERSON> e personalizadas para você.", "You cannot upload an empty file.": "", "You have no archived conversations.": "Você não tem conversas arquivadas.", "You have shared this chat": "Você partilhou esta conversa", "You're a helpful assistant.": "Você é um assistente útil.", "Your account status is currently pending activation.": "O status da sua conta está atualmente com a ativação pendente.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "Youtube", "Youtube Loader Settings": "Configurações do Carregador do Youtube"}