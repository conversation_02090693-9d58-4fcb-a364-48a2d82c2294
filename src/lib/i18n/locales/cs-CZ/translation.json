{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' nebo '-1' pro <PERSON><PERSON><PERSON><PERSON> v<PERSON>.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(např. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(např. `sh webui.sh --api`)", "(latest)": "Nejnovější", "{{ models }}": "{{ models }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "{{user}}'s konver<PERSON>e", "{{webUIName}} Backend Required": "Požadován {{webUIName}} Backend", "*Prompt node ID(s) are required for image generation": "*Jsou vyžadovány IDs pro prompt node pro generování obrázků", "A new version (v{{LATEST_VERSION}}) is now available.": "Nová verze (v{{LATEST_VERSION}}) je nyn<PERSON> k dispozici.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Model ú<PERSON><PERSON> se používá při prová<PERSON><PERSON><PERSON><PERSON>, jako je generování názvů pro chaty a vyhledávací dotazy na webu.", "a user": "uživatel", "About": "O programu", "Access": "Přístup", "Access Control": "", "Accessible to all users": "Přístupné pro všecny uživatele", "Account": "Účet", "Account Activation Pending": "Čeká na aktivaci účtu", "Actions": "Ak<PERSON>", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Aktivujte tenhle příkaz napsáním \"/{{COMMAND}}\" do chat inputu", "Active Users": "Aktivní uživatelé", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Add a model ID": "Přidat ID modelu", "Add a short description about what this model does": "<PERSON>řide<PERSON><PERSON> krátk<PERSON> pop<PERSON> toho, co tento model <PERSON><PERSON><PERSON><PERSON>.", "Add a tag": "Přidat štítek", "Add Arena Model": "Přidat Arena model", "Add Connection": "Přidat připojení", "Add Content": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "Add content here": "Přidat obsah zde", "Add custom prompt": "Přidání vlastního promptu", "Add Files": "<PERSON><PERSON><PERSON><PERSON>", "Add Group": "<PERSON><PERSON><PERSON><PERSON>", "Add Memory": "<PERSON><PERSON><PERSON><PERSON>", "Add Model": "Přidat model", "Add Reaction": "", "Add Tag": "Přidat štítek", "Add Tags": "<PERSON><PERSON><PERSON><PERSON>", "Add text content": "Přidejte textový obsah", "Add User": "Přidat <PERSON>", "Add User Group": "Přidatg skupinu uživatelů", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "Úprava těchto nastavení se projeví univerzálně u všech uživatelů.", "admin": "amin", "Admin": "Admin", "Admin Panel": "Adminis panel", "Admin Settings": "Nastavení admina", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Administrátoři mají přístup ke všem nástrojům kdykoliv; uživatelé potřebují mít nástroje přiřazené podle modelu ve workspace.", "Advanced Parameters": "Pokročilé parametry", "Advanced Params": "Pokročilé parametry", "All Documents": "Všechny dokumenty", "All models deleted successfully": "Všechny modely úspěšně odstráněny", "Allow Chat Delete": "Povolit odstranění chatu", "Allow Chat Deletion": "Povolit odstranění chatu", "Allow Chat Edit": "Povolit úpravu chatu", "Allow File Upload": "Povolit nahrávat soubory", "Allow non-local voices": "Povolit ne-místní h<PERSON>y", "Allow Temporary Chat": "Po<PERSON><PERSON>ný chat", "Allow User Location": "Povolit uživatelskou polohu", "Allow Voice Interruption in Call": "Povolit přerušení hlasu při hovoru", "Allowed Endpoints": "", "Already have an account?": "<PERSON><PERSON> máte účet?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "asistent", "and": "a", "and {{COUNT}} more": "a {{COUNT}} další/ch", "and create a new shared link.": "a vytvořit nový sdílený odkaz.", "api": "", "API Base URL": "Základní URL adresa API", "API Key": "Klíč API", "API Key created.": "API klíč byl vytvořen.", "API Key Endpoint Restrictions": "", "API keys": "API klíče", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "<PERSON><PERSON>", "Archive": "Archivovat", "Archive All Chats": "Archivovat všechny chaty", "Archived Chats": "Archivo<PERSON><PERSON>", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "Jste si jistý?", "Arena Models": "Arena modely", "Artifacts": "Artefakty", "Ask a question": "Zeptejte se na otázku", "Assistant": "<PERSON><PERSON>, jak vám mohu pomoci?", "Attach file": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Attribute for Username": "", "Audio": "Zvuk", "August": "<PERSON><PERSON>", "Authenticate": "Autentikace", "Auto-Copy Response to Clipboard": "Automatické kopírování odpovědi do schránky", "Auto-playback response": "Automatická odpověď při přehrávání", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 Api Auth String", "AUTOMATIC1111 Base URL": "Výchozí URL pro AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "Vyžaduje se základní URL pro AUTOMATIC1111.", "Available list": "Dostupný seznam", "available!": "k dispozici!", "Azure AI Speech": "Azure AI syntéza řeči", "Azure Region": "Azure oblast", "Back": "<PERSON><PERSON><PERSON><PERSON>", "Bad": "", "Bad Response": "Špatná odezva", "Banners": "<PERSON><PERSON>", "Base Model (From)": "Základní model (z)", "Batch Size (num_batch)": "Batch size (num_batch)", "before": "p<PERSON>ed", "Beta": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "Klíč API pro Brave Search", "By {{name}}": "", "Bypass SSL verification for Websites": "Obcházení ověření SSL pro webové stránky", "Call": "Volání", "Call feature is not supported when using Web STT engine": "Funkce pro volání není podporována při použití Web STT engine.", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "Zrušit", "Capabilities": "Sc<PERSON>nosti", "Capture": "", "Certificate Path": "", "Change Password": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Channel Name": "", "Channels": "", "Character": "Znak", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "chat": "", "Chat": "Cha<PERSON>", "Chat Background Image": "Obrázek pozadí chatu", "Chat Bubble UI": "Uživatelské rozhraní chatu (Chat Bubble UI)", "Chat Controls": "Ovl<PERSON><PERSON><PERSON><PERSON> chatu", "Chat direction": "<PERSON><PERSON><PERSON><PERSON>", "Chat Overview": "<PERSON><PERSON><PERSON><PERSON> chatu", "Chat Permissions": "", "Chat Tags Auto-Generation": "Automatic<PERSON>é gene<PERSON> značek chatu", "Chats": "<PERSON><PERSON>.", "Check Again": "Zkontroluj znovu", "Check for updates": "Zkontrolovat aktualizace", "Checking for updates...": "Kontrola aktualizací...", "Choose a model before saving...": "Vyberte model p<PERSON><PERSON>...", "Chunk Overlap": "", "Chunk Params": "", "Chunk Size": "", "Ciphers": "", "Citation": "Odkaz", "Clear memory": "Vymazat paměť", "click here": "", "Click here for filter guides.": "", "Click here for help.": "Klikněte zde pro nápovědu.", "Click here to": "Klikněte sem pro", "Click here to download user import template file.": "Klikněte zde pro stažení šablony souboru pro import <PERSON><PERSON><PERSON><PERSON><PERSON>.", "Click here to learn more about faster-whisper and see the available models.": "Klikněte sem a zjistěte více o faster-whisper a podívejte se na dostupné modely.", "Click here to select": "Klikněte sem pro výběr", "Click here to select a csv file.": "Klikněte zde pro výběr souboru typu csv.", "Click here to select a py file.": "Klikněte sem pro výběr {{py}} souboru.", "Click here to upload a workflow.json file.": "Klikněte sem pro nahrání souboru workflow.json.", "click here.": "klik<PERSON><PERSON>te sem.", "Click on the user role button to change a user's role.": "Klikněte na tlačítko role uživatele, abyste změnili roli uživatele.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Přístup k zápisu do schránky byl zamítnut. Prosím, zkontrolujte nastavení svého prohlížeče a udělte potřebný přístup.", "Clone": "<PERSON><PERSON><PERSON><PERSON>", "Close": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Code execution": "Provádě<PERSON><PERSON> k<PERSON>", "Code formatted successfully": "<PERSON><PERSON><PERSON> <PERSON>l úsp<PERSON>šně naformátov<PERSON>.", "Collection": "", "Color": "<PERSON><PERSON>", "ComfyUI": "ComfyUI.", "ComfyUI API Key": "", "ComfyUI Base URL": "Základní URL ComfyUI", "ComfyUI Base URL is required.": "Je vyžadována základní URL pro ComfyUI.", "ComfyUI Workflow": "Pracovní postup ComfyUI", "ComfyUI Workflow Nodes": "Pracovní uzly ComfyUI", "Command": "Příkaz", "Completions": "Doplnění", "Concurrent Requests": "Současné p<PERSON>", "Configure": "Konfigurovat", "Configure Models": "Konfigurovat modely", "Confirm": "Potvrdit", "Confirm Password": "Potvrzen<PERSON> hesla", "Confirm your action": "Potvrďte svoji akci", "Confirm your new password": "", "Connections": "Připojení", "console": "", "Contact Admin for WebUI Access": "Kontaktujte administrátora pro přístup k webovému rozhraní.", "Content": "<PERSON><PERSON><PERSON>", "Content Extraction": "Extrahování obsahu", "Context Length": "<PERSON><PERSON><PERSON><PERSON>", "Continue Response": "Pokračovat v odpovědi", "Continue with {{provider}}": "Pokračovat s {{provider}}", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Řízení, jak se text zpr<PERSON>vy rozděluje pro požadavky TTS. 'Punctuation' rozděluje text na věty, 'paragraphs' rozděluje text na odstavce a 'none' udržuje zprávu jako jeden celý řetězec.", "Controls": "Ovládací prvky", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "Zkopírováno", "Copied shared chat URL to clipboard!": "URL sdíleného chatu zkopírován do schránky!", "Copied to clipboard": "Zkopírováno do schránky", "Copy": "Kopírovat", "Copy last code block": "Zkopírujte poslední blok kódu", "Copy last response": "Zkopírujte poslední odpověď", "Copy Link": "Kopírovat odkaz", "Copy to clipboard": "Kopírovat do schránky", "Copying to clipboard was successful!": "Kopírování do schránky bylo úspěšné!", "Create": "Vytvořit", "Create a knowledge base": "Vytvořit knowledge base", "Create a model": "Vytvořte model", "Create Account": "Vytvořit účet", "Create Admin Account": "Vytvořit admin <PERSON>", "Create Channel": "", "Create Group": "Vytvořit skupinu", "Create Knowledge": "Vytvořit knowledge", "Create new key": "Vytvořit nový klíč", "Create new secret key": "Vytvořte nový tajný klíč", "Created at": "Vytvořeno dne", "Created At": "Vytvořeno dne", "Created by": "Vytvořeno uživatelem", "CSV Import": "CSV import", "Current Model": "Aktuální model", "Current Password": "Aktuální he<PERSON>lo", "Custom": "Na míru", "Dark": "Tmavý", "Database": "<PERSON><PERSON><PERSON><PERSON>", "December": "Prosinec", "Default": "Výchozí hodnoty nebo nastavení.", "Default (Open AI)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Open AI)", "Default (SentenceTransformers)": "Výchozí (SentenceTransformers)", "Default Model": "Výchozí model", "Default model updated": "Výchozí model a<PERSON><PERSON><PERSON><PERSON><PERSON>.", "Default Models": "Výchozí modely", "Default permissions": "Výchozí povolení", "Default permissions updated successfully": "Výchozí povolení úspěšne aktualizovány", "Default Prompt Suggestions": "Výchozí návrhy promptů", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "Výchozí uživatelská role", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "Delete a model": "Odstranit model.", "Delete All Chats": "Odstranit všechny konverzace", "Delete All Models": "", "Delete chat": "<PERSON><PERSON><PERSON><PERSON> chat", "Delete Chat": "<PERSON><PERSON><PERSON><PERSON> chat", "Delete chat?": "<PERSON><PERSON>zat konverzaci?", "Delete folder?": "<PERSON><PERSON>zat složku?", "Delete function?": "Funkce pro odstranění?", "Delete Message": "", "Delete prompt?": "<PERSON><PERSON><PERSON><PERSON> prompt?", "delete this link": "smazat tento odkaz", "Delete tool?": "Odstranit nástroj?", "Delete User": "<PERSON><PERSON><PERSON><PERSON>", "Deleted {{deleteModelTag}}": "Smazáno {{deleteModelTag}}", "Deleted {{name}}": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{name}}", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "<PERSON><PERSON>", "Disabled": "Zak<PERSON><PERSON><PERSON><PERSON>", "discover": "", "Discover a function": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Discover a model": "Objevte model", "Discover a prompt": "<PERSON><PERSON><PERSON><PERSON><PERSON> prompt", "Discover a tool": "<PERSON><PERSON><PERSON><PERSON><PERSON> nástroj", "Discover wonders": "", "Discover, download, and explore custom functions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, stahujte a zkoumejte vlastní funkce", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON><PERSON><PERSON>, st<PERSON><PERSON>ěte a prozkoumejte vlastní prompty.", "Discover, download, and explore custom tools": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, stahujte a prozkoumávejte vlastní nástroje", "Discover, download, and explore model presets": "<PERSON><PERSON><PERSON><PERSON><PERSON>, st<PERSON><PERSON>ěte a prozkoumejte přednastavení modelů", "Dismissible": "Odstranitelné", "Display": "", "Display Emoji in Call": "Zobrazení emoji b<PERSON><PERSON> hovoru", "Display the username instead of You in the Chat": "Zobrazit uživatelské jméno místo \"Vás\" v chatu", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "Neinstalujte funkce ze zdro<PERSON>ů, kterým plně nedůvěřujete.", "Do not install tools from sources you do not fully trust.": "Neinstalujte nástroje ze zdro<PERSON>ů, kter<PERSON>m plně nedůvěřujete.", "Document": "Dokument", "Documentation": "Dokumentace", "Documents": "Dokumenty", "does not make any external connections, and your data stays securely on your locally hosted server.": "nevytvá<PERSON><PERSON> externí připojení a vaše data zůstávají bezpečně na vašem lokálním serveru.", "Don't have an account?": "<PERSON>emáte <PERSON>?", "don't install random functions from sources you don't trust.": "Neinstalujte náhodné funkce ze zdrojů, k<PERSON><PERSON><PERSON> nedůvěřujete.", "don't install random tools from sources you don't trust.": "Neinstalujte náhodné nástroje ze zdrojů, kter<PERSON>m nedůvěřujete.", "Done": "Hotovo.", "Download": "<PERSON><PERSON><PERSON><PERSON>", "Download canceled": "Stahování zrušeno", "Download Database": "Stáhnout <PERSON>zi", "Drag and drop a file to upload or select a file to view": "", "Draw": "<PERSON><PERSON><PERSON>", "Drop any files here to add to the conversation": "Sem přetáhněte libovolné soubory, které chcete přidat do konverzace", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "např. '30s','10m'. <PERSON><PERSON><PERSON><PERSON> j<PERSON> jsou 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "<PERSON><PERSON><PERSON><PERSON>", "Edit Arena Model": "Upravit Arena Model", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "Up<PERSON>vi<PERSON>", "Edit User": "Upravi<PERSON>", "Edit User Group": "", "ElevenLabs": "ElevenLabs", "Email": "E-mail", "Embark on adventures": "", "Embedding Batch Size": "", "Embedding Model": "Vkládací model (Embedding Model)", "Embedding Model Engine": "", "Embedding model set to \"{{embedding_model}}\"": "Model vkládání nastaven na \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "Povolit sdílení komunity", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "Povolit hodnocení zpráv", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "Povolit nové registrace", "Enable Web Search": "Povolit webové vyhledávání", "Enabled": "Povoleno", "Engine": "Engine", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Ujistě<PERSON> se, že váš CSV soubor obsahuje 4 sloupce v tomto pořadí: Name, Email, Password, Role.", "Enter {{role}} message here": "<PERSON>adej<PERSON> zprávu {{role}} sem", "Enter a detail about yourself for your LLMs to recall": "Zadejte podrobnost o sobě, kterou si vaše LLM mají pamatovat.", "Enter api auth string (e.g. username:password)": "Zadejte autentizační řetězec API (např. uživatelské_jméno:heslo)", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "Zadejte API klíč pro Brave Search", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "Zadejte měřítko CFG (např. 7.0)", "Enter Chunk Overlap": "Zadejte překryv části", "Enter Chunk Size": "Zadejte velikost bloku", "Enter description": "<PERSON>adej<PERSON> popis", "Enter Github Raw URL": "Zadejte URL adresu <PERSON>", "Enter Google PSE API Key": "Zadejte klíč rozhraní API Google PSE", "Enter Google PSE Engine Id": "Zadejte ID vyhledávacího mechanismu Google PSE", "Enter Image Size (e.g. 512x512)": "Zadejte velikost obrázku (např. 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "Zadej<PERSON> kó<PERSON>", "Enter Model ID": "Zadejte ID modelu", "Enter model tag (e.g. {{modelTag}})": "Zadejte označení modelu (např. {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "Zadejte počet krok<PERSON> (např. 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "Zadej<PERSON> (např. Euler a)", "Enter Scheduler (e.g. Karras)": "<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "Enter Score": "Zadejte skóre", "Enter SearchApi API Key": "Zadejte API klíč pro SearchApi", "Enter SearchApi Engine": "Zadejte vyhledávací stroj SearchApi", "Enter Searxng Query URL": "Zadejte URL dotazu Searxng", "Enter Seed": "", "Enter Serper API Key": "Zadejte Serper API klíč", "Enter Serply API Key": "Zadejte API klíč pro Serply", "Enter Serpstack API Key": "Zadejte klíč API pro Serpstack", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "Zadejte ukončovací sekvenci", "Enter system prompt": "<PERSON><PERSON><PERSON><PERSON> systémový prompt", "Enter Tavily API Key": "Zadejte API klíč <PERSON>", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "Zadejte URL serveru Tika", "Enter Top K": "Zadejte horní K", "Enter URL (e.g. http://127.0.0.1:7860/)": "Zadejte URL (např. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Zadejte URL (např. http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "Zadejte svůj email", "Enter Your Full Name": "Zadejte své plné <PERSON>", "Enter your message": "Zadejte svou zprávu", "Enter your new password": "", "Enter Your Password": "Zadejte své he<PERSON>lo", "Enter your prompt": "", "Enter Your Role": "Zadejte svou roli", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "Chyba", "ERROR": "Chyba", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "Hodnocení", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "Vyloučit", "Experimental": "Experimentální", "Explore the cosmos": "", "Export": "Exportovat", "Export All Archived Chats": "", "Export All Chats (All Users)": "Exportovat všechny chaty (všichni uživatelé)", "Export chat (.json)": "Exportovat chat (.json)", "Export Chats": "Exportovat chaty", "Export Config to JSON File": "Exportujte konfiguraci do souboru JSON", "Export Functions": "Exportovat funkce", "Export Models": "Export modelů", "Export Presets": "", "Export Prompts": "Exportovat prompty", "Export to CSV": "", "Export Tools": "Exportní nás<PERSON>je", "External Models": "Externí modely", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "Nepodařilo se přidat soubor.", "Failed to create API Key.": "Nepodařilo se vytvořit API klíč.", "Failed to read clipboard contents": "Nepodařilo se přečíst obsah s<PERSON>ánky", "Failed to save models configuration": "", "Failed to update settings": "Nepodařilo se aktualizovat nastavení", "February": "Únor", "Feedback History": "Historie zpětné vazby", "Feedbacks": "", "File": "<PERSON><PERSON><PERSON>", "File added successfully.": "<PERSON><PERSON><PERSON>.", "File content updated successfully.": "<PERSON><PERSON><PERSON> byl úspěšně aktualizován.", "File Mode": "<PERSON><PERSON><PERSON>", "File not found.": "<PERSON><PERSON><PERSON>.", "File removed successfully.": "<PERSON><PERSON><PERSON> by<PERSON> ods<PERSON>něn.", "File size should not exceed {{maxSize}} MB.": "<PERSON>eli<PERSON><PERSON> souboru by <PERSON><PERSON><PERSON><PERSON> {{maxSize}} MB.", "File uploaded successfully": "", "Files": "<PERSON><PERSON><PERSON>", "Filter is now globally disabled": "Filtr je nyní globálně zakázán", "Filter is now globally enabled": "Filtr je nyní globálně povolen.", "Filters": "Filtry", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Detekováno padělání otisku prstu: <PERSON>ení mož<PERSON> p<PERSON>žít iniciály jako avatar. Používá se výchozí profilový obrázek.", "Fluidly stream large external response chunks": "Plynule streamujte velké externí č<PERSON> odpovědí", "Focus chat input": "Zaměřte se na vstup chatu", "Folder deleted successfully": "S<PERSON>žka byla úspěšně s<PERSON>ána", "Folder name cannot be empty": "Název složky nesmí být prázdný", "Folder name cannot be empty.": "Název složky nesmí být prázdný.", "Folder name updated successfully": "Název složky byl úspěšně aktualizován.", "Forge new paths": "", "Form": "Formulář", "Format your variables using brackets like this:": "Formátujte své proměnné pomocí závorek takto:", "Frequency Penalty": "Penalizace frekvence", "Function": "Funkce", "Function created successfully": "Funkce byla úspěšně vytvořena.", "Function deleted successfully": "<PERSON><PERSON> byla úsp<PERSON>šně odstraněna", "Function Description": "", "Function ID": "", "Function is now globally disabled": "Funkce je nyní globálně zakázána.", "Function is now globally enabled": "Funkce je nyní glo<PERSON>álně povolena.", "Function Name": "", "Function updated successfully": "Funkce byla úspěšně aktualizována.", "Functions": "Funkce", "Functions allow arbitrary code execution": "Funkce umožňují vykonávat libovolný kód.", "Functions allow arbitrary code execution.": "Funkce umožňují provádění libovolného kódu.", "Functions imported successfully": "<PERSON><PERSON> byly <PERSON>", "General": "Obecný", "General Settings": "Obecná nastavení", "Generate Image": "Vygenerovat obrázek", "Generating search query": "Generování vyhledávacího dotazu", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "Globální", "Good Response": "<PERSON><PERSON><PERSON><PERSON>", "Google Drive": "", "Google PSE API Key": "Klíč API pro Google PSE (Programmatically Search Engine)", "Google PSE Engine Id": "Google PSE Engine Id (Identifikátor vyhledávacího modulu Google PSE)", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "h:mm a": "hh:mm dop./odp.", "Haptic Feedback": "Haptická zpětná vazba", "Harmful or offensive": "", "has no conversations.": "ne<PERSON>á žádné konverzace.", "Hello, {{name}}": "<PERSON><PERSON><PERSON>, {{name}}", "Help": "Pomoc", "Help us create the best community leaderboard by sharing your feedback history!": "Pomozte nám vytvořit nejlepší komunitní žebříček sdílením historie vaší zpětné vazby!", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "<PERSON><PERSON><PERSON>", "Host": "", "How can I help you today?": "Jak vám mohu dnes pomoci?", "How would you rate this response?": "", "Hybrid Search": "Hybridní v<PERSON>hledávání", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Beru na vědomí, že jsem si přečetl a chápu důsledky svých činů. Jsem si vědom rizik spojených s vykonáváním libovolného kódu a ověřil jsem důvěryhodnost zdroje.", "ID": "ID", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "Generování o<PERSON> (experimentální)", "Image Generation Engine": "Engine pro generování obrázků", "Image Max Compression Size": "", "Image Settings": "Nastavení obrázku", "Images": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Import Chats": "Importovat konverzace", "Import Config from JSON File": "Importování konfigurace z JSON souboru", "Import Functions": "Načítán<PERSON>", "Import Models": "Importování modelů", "Import Presets": "", "Import Prompts": "Importovat Prompty", "Import Tools": "Importovat nástroje", "Include": "<PERSON><PERSON><PERSON><PERSON>", "Include `--api-auth` flag when running stable-diffusion-webui": "Zahrňte přepínač `--api-auth` při spuštění stable-diffusion-webui.", "Include `--api` flag when running stable-diffusion-webui": "<PERSON><PERSON><PERSON> s<PERSON>štění stable-diffusion-webui zahrňte příznak `--api`.", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "Info", "Input commands": "Vstupní příkazy", "Install from Github URL": "Instalace z URL adresy Githubu", "Instant Auto-Send After Voice Transcription": "Okamžité automatické odeslání po přepisu hlasu", "Interface": "Rozhraní", "Invalid file format.": "Neplatný form<PERSON>t so<PERSON>.", "Invalid Tag": "Neplatný tag", "is typing...": "", "January": "<PERSON><PERSON>", "Jina API Key": "", "join our Discord for help.": "připojte se k našemu Discordu pro pomoc.", "JSON": "JSON", "JSON Preview": "<PERSON><PERSON><PERSON><PERSON> JSO<PERSON>", "July": "Červenec", "June": "červen", "JWT Expiration": "Vypršení JWT", "JWT Token": "JWT <PERSON> (JSON Web Token)", "Kagi Search API Key": "", "Keep Alive": "Udržovat spojení", "Key": "", "Keyboard shortcuts": "Klávesové zkratky", "Knowledge": "Znalosti", "Knowledge Access": "", "Knowledge created successfully.": "Znalost úspěšně vytvořena.", "Knowledge deleted successfully.": "Znalosti byly úsp<PERSON>šně odstraněny.", "Knowledge reset successfully.": "Úspěšné obnovení znalostí.", "Knowledge updated successfully": "Znalosti úspěšně aktualizovány", "Label": "", "Landing Page Mode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Language": "Jazyk", "Last Active": "Naposledy aktivní", "Last Modified": "Poslední změna", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Leave empty for unlimited": "Nechte prázdné pro neomezeně", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "Nechte prázdné pro zahrnutí všech modelů nebo vyberte konkrétní modely.", "Leave empty to use the default prompt, or enter a custom prompt": "Nechte prázdné pro použití výchozího podně<PERSON>, nebo zadejte vlastní podně<PERSON>.", "Light": "Světlo", "Listening...": "Poslouchání...", "Local": "", "Local Models": "Lokální modely", "Lost": "Ztracený", "LTR": "LTR", "Made by OpenWebUI Community": "Vytvořeno komunitou OpenWebUI", "Make sure to enclose them with": "Uji<PERSON><PERSON><PERSON> se, že jsou uzavřeny pomocí", "Make sure to export a workflow.json file as API format from ComfyUI.": "Uji<PERSON><PERSON><PERSON> se, že exportujete soubor workflow.json ve formátu API z ComfyUI.", "Manage": "Spravovat", "Manage Arena Models": "Správa modelů v Arena", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "Správa pipelines", "March": "Březen", "Max Tokens (num_predict)": "Maximální počet token<PERSON> (num_predict)", "Max Upload Count": "Maximální počet nahrání", "Max Upload Size": "Maximální velikost nahrávání", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Maximálně 3 modely mohou být staženy současně. Prosím zkuste to znovu později.", "May": "k<PERSON><PERSON><PERSON>", "Memories accessible by LLMs will be shown here.": "Vzpomínky přístupné LLMs budou zobrazeny zde.", "Memory": "Paměť", "Memory added successfully": "Paměť byla <PERSON>š<PERSON> p<PERSON>.", "Memory cleared successfully": "Paměť byla úspěšně v<PERSON>azána.", "Memory deleted successfully": "Paměť byla <PERSON> s<PERSON>", "Memory updated successfully": "Paměť úspěšně aktualizována", "Merge Responses": "Sloučit odpovědi", "Message rating should be enabled to use this feature": "Hodnocení zpráv musí být povoleno, aby bylo možné tuto <PERSON> použí<PERSON>.", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> po vytvoření o<PERSON>, nebu<PERSON>u sdíleny. Uživatelé s URL budou moci zobrazit sdílený chat.", "Min P": "<PERSON>", "Minimum Score": "Minimální skóre", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "MMMM DD, RRRR", "MMMM DD, YYYY HH:mm": "MMMM DD, RRRR HH:mm", "MMMM DD, YYYY hh:mm:ss A": "MMMM DD, YYYY hh:mm:ss A", "Model": "Model", "Model '{{modelName}}' has been successfully downloaded.": "Model „{{modelName}}“ byl úspěšně s<PERSON>.", "Model '{{modelTag}}' is already in queue for downloading.": "Model '{{modelTag}}' je ji<PERSON> z<PERSON>ř<PERSON> do fronty pro stahování.", "Model {{modelId}} not found": "Model {{modelId}} nebyl nalezen", "Model {{modelName}} is not vision capable": "Model {{modelName}} není schopen zpracovávat vizuální data.", "Model {{name}} is now {{status}}": "Model {{name}} je nyní {{status}}.", "Model accepts image inputs": "Model přij<PERSON><PERSON><PERSON> vstupy ve formě obrázků", "Model created successfully!": "Model byl úspěšně vytvořen!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Detekována cesta v souborovém systému. Je vyžadován krátký název modelu pro aktualizaci, nelze pokračovat.", "Model Filtering": "", "Model ID": "ID modelu", "Model IDs": "", "Model Name": "Název modelu", "Model not selected": "Model nebyl vybr<PERSON>", "Model Params": "Parametry modelu", "Model Permissions": "", "Model updated successfully": "Model by<PERSON><PERSON><PERSON> aktualiz<PERSON>n", "Modelfile Content": "<PERSON><PERSON><PERSON> modelfile", "Models": "<PERSON><PERSON>", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "více", "More": "V<PERSON>ce", "Name": "Jméno", "Name your knowledge base": "", "New Chat": "<PERSON><PERSON> chat", "New folder": "", "New Password": "<PERSON><PERSON>", "new-channel": "", "No content found": "Nebyly nalezeny žádné obsahové informace.", "No content to speak": "Žádný obsah k diskusi.", "No distance available": "Není dostupná žádná vzdálenost", "No feedbacks found": "Žádná zpětná vazba nenalezena", "No file selected": "Nebyl vybrán žádný soubor", "No files found.": "Nebyly nalezeny žádné soubory.", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "Nebyl nalezen žádný obsah HTML, CSS ani JavaScriptu.", "No knowledge found": "Nebyly nalezeny žádné znalosti", "No model IDs": "", "No models found": "Nebyly nalezeny žádné modely", "No models selected": "", "No results found": "Nebyly nalezeny žádné výsledky", "No search query generated": "Nebyl vygenerován žádný vyhledávací dotaz.", "No source available": "Není k dispozici žádný zdroj.", "No users were found.": "", "No valves to update": "Žádné ventily k aktualizaci", "None": "<PERSON><PERSON><PERSON><PERSON>", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Poznámka: Pokud nastavíte minimální skóre, vyhledávání vrátí pouze dokumenty s hodnocením, kter<PERSON> je větší nebo rovno zadanému minimálnímu skóre.", "Notes": "Poznámky", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Oznámení", "November": "Listopad", "num_gpu (Ollama)": "Počet GPU (Ollama)", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "OAuth ID", "October": "Říjen", "Off": "Vypnuto", "Okay, Let's Go!": "<PERSON><PERSON><PERSON><PERSON>, poj<PERSON>me na to!", "OLED Dark": "OLED Dark", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API disabled": "API rozhraní Ollama je zakázáno.", "Ollama API settings updated": "", "Ollama Version": "<PERSON><PERSON><PERSON>", "On": "Na", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "Příkazový řetězec smí obsahovat pouze alfanumerické znaky a pomlčky.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "<PERSON><PERSON> kolekce mohou být <PERSON>, pro úpravu/přidání dokumentů vytvořte novou znalostní b<PERSON>zi.", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Jejda! Vypadá to, že URL adresa je neplatná. Prosím, zkontrolujte ji a zkuste to znovu.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Jejda! Některé soubory se stále nahrávají. Prosím, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, až bude nahrá<PERSON><PERSON> do<PERSON>.", "Oops! There was an error in the previous response.": "Jejda! V předchozí odpovědi došlo k chybě.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Jejda! Používáte nepodporovanou metodu (pouze frontend). Prosím, spusťte WebUI ze serverové č<PERSON>ti (backendu).", "Open in full screen": "Otevřít na celou obrazovku", "Open new chat": "Otevřít nový chat", "Open WebUI uses faster-whisper internally.": "Open WebUI intern<PERSON> p<PERSON>ív<PERSON> faster-whisper.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Verze Open WebUI (v{{OPEN_WEBUI_VERSION}}) je niž<PERSON>í než požadovaná verze (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI je výzkumná organizace zaměřená na umělou inteligenci, která je známá vývojem pokročilých jazykových modelů, jako je například GPT. Tyto modely se využívají v různých aplikacích, včetně konverzačních agentů a jazykových nástrojů.", "OpenAI API": "OpenAI API je rozhraní aplikačního programování, které umožňuje vývojářům integrovat pokročilé jazykové modely do svých aplikací.", "OpenAI API Config": "Konfigurace API OpenAI", "OpenAI API Key is required.": "Je vyžadován klíč OpenAI API.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "Je vyžadován odkaz/adresa URL nebo klíč OpenAI.", "or": "nebo", "Organize your users": "", "OUTPUT": "VÝSTUP", "Output format": "Formát výstupu", "Overview": "<PERSON><PERSON><PERSON><PERSON>", "page": "<PERSON><PERSON><PERSON><PERSON>", "Password": "He<PERSON><PERSON>", "Paste Large Text as File": "", "PDF document (.pdf)": "PDF dokument (.pdf)", "PDF Extract Images (OCR)": "Extrahování obrázků z PDF (OCR)", "pending": "čeká na vyřízení", "Permission denied when accessing media devices": "Odmítnutí povolení při přístupu k mediálním zařízením", "Permission denied when accessing microphone": "Přístup k mikrofonu byl odepřen", "Permission denied when accessing microphone: {{error}}": "Oprávnění zamítnuto při přístupu k mikrofonu: {{error}}", "Permissions": "", "Personalization": "Personalizace", "Pin": "", "Pinned": "", "Pioneer insights": "", "Pipeline deleted successfully": "Pipeline byla <PERSON>ě odstraněna", "Pipeline downloaded successfully": "<PERSON><PERSON><PERSON>l ú<PERSON>š<PERSON> sta<PERSON>", "Pipelines": "", "Pipelines Not Detected": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON><PERSON>", "Pipelines Valves": "", "Plain text (.txt)": "Čistý text (.txt)", "Playground": "", "Please carefully review the following warnings:": "Prosím, pečlivě si přečtěte následující upozornění:", "Please enter a prompt": "Prosím, zadejte zadání.", "Please fill in all fields.": "Prosím, vyplňte všechna pole.", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "Předchozích 30 dnů", "Previous 7 days": "Předchozích 7 dní", "Profile Image": "Profilový obrázek", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (např. Řekni mi zábavný fakt o Římské říši)", "Prompt Content": "<PERSON><PERSON><PERSON> promptu", "Prompt created successfully": "", "Prompt suggestions": "Návrhy výzev", "Prompt updated successfully": "", "Prompts": "Prompty", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "Stáhněte \"{{searchValue}}\" z Ollama.com", "Pull a model from Ollama.com": "Stáhněte model z Ollama.com", "Query Generation Prompt": "", "Query Params": "Parametry dotazu", "RAG Template": "Šablona RAG", "Rating": "Hodnocení", "Re-rank models by topic similarity": "<PERSON><PERSON><PERSON> seř<PERSON>te modely podle podobnosti témat.", "Read Aloud": "Číst nahlas", "Record voice": "<PERSON><PERSON><PERSON><PERSON>", "Redirecting you to OpenWebUI Community": "Přesměrování na komunitu OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Odkazujte na sebe jako na \"uživatele\" (např. \"Uživatel se učí španělsky\").", "References from": "Reference z", "Refresh Token Expiration": "", "Regenerate": "Regenerovat", "Release Notes": "Záznamy o vydání", "Relevance": "Relevance", "Remove": "<PERSON><PERSON><PERSON><PERSON>", "Remove Model": "Odebrat model", "Rename": "Př<PERSON>menovat", "Reorder Models": "", "Repeat Last N": "Opakovat posledních N", "Reply in Thread": "", "Request Mode": "<PERSON><PERSON><PERSON>", "Reranking Model": "Model pro přehodnocení pořadí", "Reranking model disabled": "P<PERSON><PERSON>řazovací model je <PERSON><PERSON><PERSON><PERSON>", "Reranking model set to \"{{reranking_model}}\"": "Model pro přeřazení nastaven na \"{{reranking_model}}\"", "Reset": "<PERSON><PERSON><PERSON>", "Reset All Models": "", "Reset Upload Directory": "Resetovat ad<PERSON>", "Reset Vector Storage/Knowledge": "Resetování <PERSON>iště vektorů/znalostí", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Oznámení o odpovědích nelze aktivovat, protože oprávnění webu byla zamítnuta. Navštivte nastavení svého prohlížeče a udělte potřebný přístup.", "Response splitting": "Rozdělení odpovědi", "Result": "<PERSON><PERSON><PERSON><PERSON>", "Retrieval Query Generation": "", "Rich Text Input for Chat": "Vstup pro chat ve formátu <PERSON> Text", "RK": "RK", "Role": "<PERSON><PERSON>", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "<PERSON><PERSON><PERSON><PERSON>", "Running": "Spuštění", "Save": "Uložit", "Save & Create": "Uložit a Vytvořit", "Save & Update": "Uložit a aktualizovat", "Save As Copy": "Uložit jako kopii", "Save Tag": "Uložit štítek", "Saved": "Uloženo", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Ukládání záznamů chatu přímo do úložiště vašeho prohlížeče již není podpor<PERSON>no. Věnujte prosím chvíli stažení a smazání svých záznamů chatu kliknutím na tlačítko níže. Nemějte obavy, můžete snadno znovu importovat své záznamy chatu na backend prostřednictvím", "Scroll to bottom when switching between branches": "Přejít na konec při přepínání mezi větvemi.", "Search": "Vyhledávání", "Search a model": "Vyhledat model", "Search Base": "", "Search Chats": "Vyhledávání v chatu", "Search Collection": "Hledat kolekci", "Search Filters": "", "search for tags": "<PERSON><PERSON><PERSON><PERSON>", "Search Functions": "Vyhledávací funkce", "Search Knowledge": "Vyhledávání znalostí", "Search Models": "Vyhledávací modely", "Search options": "", "Search Prompts": "Vyhledávací dotazy", "Search Result Count": "Počet výsledků hledání", "Search Tools": "Nástroje pro vyhledávání", "Search users": "", "SearchApi API Key": "Klíč API pro SearchApi", "SearchApi Engine": "Vyhledávací engine API", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON><PERSON> \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "Vyhledávání znalostí pro \"{{searchQuery}}\"", "Searxng Query URL": "Adresa URL dotazu Searxng", "See readme.md for instructions": "Podívejte se do {{readme.md}} pro pokyny.", "See what's new": "Podívejte se, co je nového", "Seed": "<PERSON><PERSON><PERSON><PERSON>", "Select a base model": "<PERSON><PERSON><PERSON><PERSON> model", "Select a engine": "Vyberte engine", "Select a function": "<PERSON><PERSON><PERSON><PERSON>", "Select a group": "", "Select a model": "Vyberte model", "Select a pipeline": "Vyberte pipeline", "Select a pipeline url": "Vyberte URL adresu kanálu", "Select a tool": "<PERSON><PERSON><PERSON><PERSON>", "Select Engine": "Vyberte engine", "Select Knowledge": "Vybrat znalosti", "Select model": "Vyberte model", "Select only one model to call": "<PERSON><PERSON><PERSON><PERSON> pouze jeden model, <PERSON><PERSON><PERSON>te p<PERSON>t", "Selected model(s) do not support image inputs": "<PERSON><PERSON><PERSON><PERSON><PERSON>(é) model(y) nepodporují vstupy v podobě obrázků.", "Semantic distance to query": "Semantická vzdálenost k dotazu", "Send": "<PERSON><PERSON><PERSON>", "Send a message": "", "Send message": "Odeslat zprávu", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Odešle `stream_options: { include_usage: true }` v žádosti. Podporovaní poskytovatelé vrátí informace o využití tokenů v odpovědi, k<PERSON><PERSON> je tato možnost nastavena.", "September": "Září", "Serper API Key": "Klíč API pro Serper", "Serply API Key": "Serply API klíč", "Serpstack API Key": "Klíč API pro Serpstack", "Server connection verified": "Připojení k serveru ověřeno", "Set as default": "Nastavit jako výchozí", "Set CFG Scale": "Nastavte hodnotu CFG Scale", "Set Default Model": "Nastavení výchozího modelu", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "Nastavte model vkládání (např. {{model}})", "Set Image Size": "Nastavení velikosti obrázku", "Set reranking model (e.g. {{model}})": "Nastavte model pro přehodnocení (např. {{model}})", "Set Sampler": "Nastavení vzorkovače", "Set Scheduler": "Nastavení <PERSON>", "Set Steps": "Nastavení krok<PERSON>", "Set Task Model": "Nastavit model <PERSON><PERSON><PERSON>", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "<PERSON><PERSON><PERSON><PERSON> hlas", "Set whisper model": "Nastavit model whisper", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "Nastavení", "Settings saved successfully!": "Nastavení byla úspěšně uložena!", "Share": "Sdílet", "Share Chat": "Sdílet chat", "Share to OpenWebUI Community": "Sdílet s komunitou OpenWebUI", "Show": "Zobrazit", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "Zobrazit podrobnosti administrátora v překryvném okně s čekajícím účtem", "Show shortcuts": "Zobrazit klávesové zkratky", "Show your support!": "Vyjadřete svou podporu!", "Sign in": "Přihlásit se", "Sign in to {{WEBUI_NAME}}": "Přihlásit se do {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "Odhlásit se", "Sign up": "Zaregistrovat se", "Sign up to {{WEBUI_NAME}}": "Zaregistrujte se na {{WEBUI_NAME}}", "Signing in to {{WEBUI_NAME}}": "Přihlašování do {{WEBUI_NAME}}", "sk-1234": "", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Speech Playback Speed": "Rychlost přehrávání <PERSON>", "Speech recognition error: {{error}}": "Chyba rozpozná<PERSON>í <PERSON>: {{error}}", "Speech-to-Text Engine": "Motor převodu řeči na text", "Stop": "<PERSON><PERSON><PERSON><PERSON>", "Stop Sequence": "<PERSON><PERSON><PERSON><PERSON> Zastavení", "Stream Chat Response": "Odezva chatu Stream", "STT Model": "Model rozpoznávání řeči na text (STT)", "STT Settings": "Nastavení STT (Rozpoznávání řeči)", "Success": "Úspěch", "Successfully updated.": "Úspěšně aktualizováno.", "Support": "Podpora", "Support this plugin:": "Podpořte tento plugin:", "Sync directory": "Synchronizovat adresář", "System": "System", "System Instructions": "", "System Prompt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> prompt", "Tags Generation": "", "Tags Generation Prompt": "Prompt pro generov<PERSON><PERSON>", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "Klepněte pro přerušení", "Tavily API Key": "Klíč API pro Tavily", "Temperature": "", "Template": "Šablona", "Temporary Chat": "<PERSON><PERSON><PERSON><PERSON><PERSON> chat", "Text Splitter": "Rozdělovač textu", "Text-to-Speech Engine": "Stroj pro převod textu na řeč", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "Děkujeme za vaši zpětnou vazbu!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Vývojáři stojící za tímto pluginem jsou zapálení dobrovolníci z komunity. Pokud považujete tento plugin za užitečný, zvažte příspěvek k jeho vývoji.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "Hodnotící žebříček je založen na systému hodnocení Elo a je aktualizován v reálném čase.", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "Žebříček je v současné době v beta verzi a můžeme upravit výpočty hodnocení, jak budeme zdokonalovat algoritmus.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Maximální velikost souboru v MB. Pokud velikost souboru překročí tento limit, soubor nebude nahrán.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Maximální počet souborů, k<PERSON><PERSON> mohou být použity najednou v chatu. Pokud počet souborů překročí tento limit, soubory nebudou nahrány.", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "<PERSON><PERSON><PERSON><PERSON> by m<PERSON><PERSON> b<PERSON><PERSON> ho<PERSON> mezi 0,0 (0%) a 1,0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "<PERSON><PERSON><PERSON>", "Thinking...": "Přemýšlím...", "This action cannot be undone. Do you wish to continue?": "Tuto akci nelze vrátit zpět. Přejete si pokračovat?", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "To zaj<PERSON><PERSON><PERSON><PERSON><PERSON>, že vaše cenné konverzace jsou bezpečně uloženy ve vaší backendové databázi. Děkujeme!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Jedná se o <PERSON><PERSON><PERSON><PERSON>, nemusí fungovat podle očekávání a může být kdykoliv změněna.", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Tato volba odstraní všechny existující soubory ve sbírce a nahradí je nově nahranými soubory.", "This response was generated by \"{{model}}\"": "<PERSON><PERSON><PERSON><PERSON> byla v<PERSON>generov<PERSON>a pomo<PERSON>í \"{{model}}\"", "This will delete": "Tohle odstraní", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "<PERSON><PERSON><PERSON><PERSON> dojde k odstranění <strong>{{NAME}}</strong> a <strong>v<PERSON><PERSON> jeho obsah<PERSON></strong>.", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Toto obnoví znalostní databázi a synchronizuje všechny soubory. Přejete si pokračovat?", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Je vyžadována URL adresa serveru Tika.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Tip: Aktualizujte postupně více proměnných slotů stisknutím klávesy Tab v chatu po každé náhradě.", "Title": "<PERSON><PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "Název (např. Řekni mi zajímavost)", "Title Auto-Generation": "Automatické gene<PERSON> n<PERSON>", "Title cannot be an empty string.": "Název nemůže být prázdným řetězcem.", "Title Generation Prompt": "Generování názvu promptu", "TLS": "", "To access the available model names for downloading,": "Pro získání dostupných názvů modelů ke stažení,", "To access the GGUF models available for downloading,": "Pro přístup k modelům GGUF dostupným pro stažení,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Pro přístup k WebUI se prosím obraťte na administrátora. Administrátoři mohou spravovat stavy uživatelů z Admin Panelu.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Chcete-li zde připojit znalostní <PERSON>, nejprve ji přidejte do workspace \"Knowledge\".", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Aby byla chrán<PERSON>na va<PERSON><PERSON> souk<PERSON>, z vaší zpětné vazby jsou sd<PERSON> pouze hodn<PERSON>í, ID modelů, z<PERSON><PERSON><PERSON> a metadata – vaše chatové záznamy zůstávají soukromé a nejsou zahrnuty.", "To select actions here, add them to the \"Functions\" workspace first.": "Chcete-li zde v<PERSON> a<PERSON>, nejprve je přidejte do pracovní plochy \"Functions\".", "To select filters here, add them to the \"Functions\" workspace first.": "Chcete-li zde v<PERSON> fi<PERSON>, nejprve je přidejte do workspace „Functions“.", "To select toolkits here, add them to the \"Tools\" workspace first.": "Chcete-li zde v<PERSON>bra<PERSON> n<PERSON>, přidejte je nejprve do workspace \"Tools\".", "Toast notifications for new updates": "Oznámení ve formě toastů pro nové aktualizace", "Today": "Dnes", "Toggle settings": "Přepnout nastavení", "Toggle sidebar": "Přepnout postranní panel", "Toggle temporary chat": "", "Token": "Token", "Tokens To Keep On Context Refresh (num_keep)": "<PERSON><PERSON><PERSON>, které si ponechat při obnovení kontextu (num_keep)", "Tool created successfully": "Nástroj byl úspěšně vytvořen.", "Tool deleted successfully": "Nás<PERSON>j byl úsp<PERSON>š<PERSON>ě s<PERSON>.", "Tool Description": "", "Tool ID": "ID nástroje", "Tool imported successfully": "<PERSON>ás<PERSON>j byl ú<PERSON>š<PERSON>n", "Tool Name": "", "Tool updated successfully": "Nástroj byl úspěšně aktualizován.", "Tools": "<PERSON>ás<PERSON><PERSON>", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "Nástroje jsou systémem pro volání funkcí s vykonáváním libovolného kódu.", "Tools have a function calling system that allows arbitrary code execution": "Nástroje mají systém vol<PERSON>, k<PERSON><PERSON> umožňuje libovolné spouštění kódu.", "Tools have a function calling system that allows arbitrary code execution.": "Nástroje mají sys<PERSON>m vol<PERSON>, k<PERSON><PERSON> umožňuje spuštění libovolného kódu.", "Top K": "Top K", "Top P": "Top P", "Transformers": "", "Trouble accessing Ollama?": "<PERSON><PERSON><PERSON> s přístupem k Ollama?", "TTS Model": "Model převodu textu na řeč (TTS)", "TTS Settings": "Nastavení TTS (Text-to-Speech)", "TTS Voice": "TTS hlas", "Type": "Napište", "Type Hugging Face Resolve (Download) URL": "Zadejte URL pro úspěšné stažení z Hugging Face.", "Uh-oh! There was an issue with the response.": "", "UI": "UI", "Unarchive All": "Odarchivovat všechny", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "Odepnout", "Unravel secrets": "", "Untagged": "<PERSON><PERSON><PERSON>", "Update": "Aktualizovat", "Update and Copy Link": "Aktualizovat a zkopírovat odkaz", "Update for the latest features and improvements.": "Aktualizace pro nejnovější funkce a vylepšení.", "Update password": "Aktualizovat he<PERSON>lo", "Updated": "Aktualizováno", "Updated at": "Aktualizováno dne", "Updated At": "Aktualizováno dne", "Upload": "<PERSON><PERSON><PERSON><PERSON>", "Upload a GGUF model": "Nahrát model ve formátu GGUF", "Upload directory": "<PERSON><PERSON><PERSON><PERSON>", "Upload files": "<PERSON><PERSON><PERSON><PERSON>", "Upload Files": "<PERSON><PERSON><PERSON><PERSON>", "Upload Pipeline": "Nahrát pipeline", "Upload Progress": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "URL": "", "URL Mode": "<PERSON><PERSON><PERSON>", "USAi Chat can make mistakes. Review all responses for accuracy. Your agency’s AI and privacy policies apply.": "", "Use '#' in the prompt input to load and include your knowledge.": "Použijte '#' ve vstupu příkazu k načtení a zahrnutí vašich znalostí.", "Use Gravatar": "Použ<PERSON><PERSON>ra<PERSON>", "Use groups to group your users and assign permissions.": "", "Use Initials": "Použijte iniciály", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "uživatel", "User": "<PERSON>živatel", "User location successfully retrieved.": "Umístění už<PERSON> bylo úspěšně <PERSON>.", "Username": "Uživatelské jméno", "Users": "Uživatelé", "Using the default arena model with all models. Click the plus button to add custom models.": "Použití výchozího modelu arény se všemi modely. Kliknutím na tlačítko plus přidejte vlastní modely.", "Utilize": "Vyu<PERSON><PERSON><PERSON>", "Valid time units:": "<PERSON><PERSON><PERSON><PERSON>:", "Valves": "Ventily", "Valves updated": "Ventily aktual<PERSON>", "Valves updated successfully": "Ventily byly <PERSON><PERSON>ě aktualizovány.", "variable": "proměnn<PERSON>", "variable to have them replaced with clipboard content.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, aby byl jejich obsah nahrazen obsah<PERSON>.", "Version": "Verze", "Version {{selectedVersion}} of {{totalVersions}}": "Verze {{selectedVersion}} z {{totalVersions}}", "Very bad": "", "View Replies": "", "Visibility": "Viditelnost", "Voice": "<PERSON><PERSON>", "Voice Input": "Hlasový vstup", "Warning": "Varování", "Warning:": "Upozornění:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Varování: Pokud aktualizujete nebo změníte svůj model vkládání, budete muset všechny dokumenty znovu importovat.", "Web": "Web", "Web API": "Webové API", "Web Loader Settings": "Nastavení Web Loaderu", "Web Search": "Vyhledávání na webu", "Web Search Engine": "Webový vyhledávač", "Web Search Query Generation": "", "Webhook URL": "Webhook URL", "WebUI Settings": "Nastavení WebUI", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "Welcome, {{name}}!": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "Co je nového v", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "kdekoliv jste", "Whisper (Local)": "<PERSON><PERSON><PERSON> (Lokálně)", "Widescreen Mode": "<PERSON><PERSON><PERSON>ého zobrazení", "Won": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dotaz (např. Kdo jsi?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Na<PERSON>š<PERSON> shrnutí na 50 slov, k<PERSON><PERSON> shr<PERSON> [téma nebo kl<PERSON><PERSON><PERSON><PERSON> slovo].", "Write something...": "Napište něco...", "Write your model template content here": "", "Yesterday": "Včera", "You": "Vy", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Můžete komunikovat pouze s maximálně {{maxCount}} soubor(y) najednou.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Můžete personalizovat své interakce s LLM pomocí přidávání vzpomínek prostřednictvím tlačítka 'Spravovat' ní<PERSON>e, což je učiní pro vás užitečnějšími a lépe přizpůsobenými.", "You cannot upload an empty file.": "Nemůžete nahrát prázdný soubor.", "You have no archived conversations.": "Nemáte žádné archivované konverzace.", "You have shared this chat": "Sd<PERSON><PERSON>li jste tento chat.", "You're a helpful assistant.": "Jste užitečný asistent.", "Your account status is currently pending activation.": "Stav vašeho účtu je nyní čekající na aktivaci.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "<PERSON><PERSON>š celý příspěvek půjde přímo vývojáři pluginu; Open WebUI si nebere žádné procento. Vybraná platforma pro financování však může mít vlastní poplatky.", "Youtube": "YouTube", "Youtube Loader Settings": "Nastavení YouTube loaderu"}