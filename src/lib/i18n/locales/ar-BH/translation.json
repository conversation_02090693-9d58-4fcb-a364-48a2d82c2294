{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' أو '-1' لا توجد انتهاء", "(e.g. `sh webui.sh --api --api-auth username_password`)": "", "(e.g. `sh webui.sh --api`)": "( `sh webui.sh --api`مثال)", "(latest)": "(الأخير)", "{{ models }}": "{{ نماذج }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "دردشات {{user}}", "{{webUIName}} Backend Required": "{{webUIName}} مطلوب", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "يتم استخدام نموذج المهمة عند تنفيذ مهام مثل إنشاء عناوين للدردشات واستعلامات بحث الويب", "a user": "مستخدم", "About": "عن", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "الحساب", "Account Activation Pending": "", "Actions": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "", "Add": "<PERSON>ض<PERSON>", "Add a model ID": "", "Add a short description about what this model does": "أضف وصفا موجزا حول ما يفعله هذا النموذج", "Add a tag": "أضافة تاق", "Add Arena Model": "", "Add Connection": "", "Add Content": "", "Add content here": "", "Add custom prompt": "أضافة مطالبة مخصصه", "Add Files": "إضافة ملفات", "Add Group": "", "Add Memory": "إضافة ذكرايات", "Add Model": "اضافة موديل", "Add Reaction": "", "Add Tag": "", "Add Tags": "اضافة تاق", "Add text content": "", "Add User": "اضافة مستخدم", "Add User Group": "", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "سيؤدي ضبط هذه الإعدادات إلى تطبيق التغييرات بشكل عام على كافة المستخدمين", "admin": "المشرف", "Admin": "", "Admin Panel": "لوحة التحكم", "Admin Settings": "اعدادات المشرف", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "", "Advanced Parameters": "التعليمات المتقدمة", "Advanced Params": "المعلمات المتقدمة", "All Documents": "جميع الملفات", "All models deleted successfully": "", "Allow Chat Delete": "", "Allow Chat Deletion": "يستطيع حذف المحادثات", "Allow Chat Edit": "", "Allow File Upload": "", "Allow non-local voices": "", "Allow Temporary Chat": "", "Allow User Location": "", "Allow Voice Interruption in Call": "", "Allowed Endpoints": "", "Already have an account?": "هل تملك حساب ؟", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "مسا<PERSON>د", "and": "و", "and {{COUNT}} more": "", "and create a new shared link.": "و أنشئ رابط مشترك جديد.", "api": "", "API Base URL": "API الرابط الرئيسي", "API Key": "API مفتاح", "API Key created.": "API تم أنشاء المفتاح", "API Key Endpoint Restrictions": "", "API keys": "مفاتيح واجهة برمجة التطبيقات", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "أب<PERSON>يل", "Archive": "الأرشيف", "Archive All Chats": "أرشفة جميع الدردشات", "Archived Chats": "الأرشيف المحادثات", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "هل أنت متأكد ؟", "Arena Models": "", "Artifacts": "", "Ask a question": "", "Assistant": "", "Attach file": "<PERSON><PERSON><PERSON><PERSON> ملف", "Attribute for Username": "", "Audio": "صوتي", "August": "أغسطس", "Authenticate": "", "Auto-Copy Response to Clipboard": "النسخ التلقائي للاستجابة إلى الحافظة", "Auto-playback response": "استجابة التشغيل التلقائي", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 الرابط الرئيسي", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 الرابط مطلوب", "Available list": "", "available!": "متاح", "Azure AI Speech": "", "Azure Region": "", "Back": "خلف", "Bad": "", "Bad Response": "استجابة خطاء", "Banners": "لافتات", "Base Model (From)": "النموذج الأساسي (من)", "Batch Size (num_batch)": "", "before": "قبل", "Beta": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "مفتاح واجهة برمجة تطبيقات البحث الشجاع", "By {{name}}": "", "Bypass SSL verification for Websites": "تجاوز التحقق من SSL للموقع", "Call": "", "Call feature is not supported when using Web STT engine": "", "Camera": "", "Cancel": "اللغاء", "Capabilities": "قدرات", "Capture": "", "Certificate Path": "", "Change Password": "تغير الباسورد", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "chat": "", "Chat": "المحادثة", "Chat Background Image": "", "Chat Bubble UI": "UI الدردشة", "Chat Controls": "", "Chat direction": "اتجاه المحادثة", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "المحادثات", "Check Again": "تحقق مرة اخرى", "Check for updates": "تحقق من التحديثات", "Checking for updates...": "البحث عن تحديثات", "Choose a model before saving...": "أخ<PERSON>ار موديل قبل الحفظ", "Chunk Overlap": "Chunk تداخل", "Chunk Params": "Chunk المتغيرات", "Chunk Size": "<PERSON><PERSON>", "Ciphers": "", "Citation": "اقتباس", "Clear memory": "", "click here": "", "Click here for filter guides.": "", "Click here for help.": "أضغط هنا للمساعدة", "Click here to": "أضغط هنا الانتقال", "Click here to download user import template file.": "", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to select": "أضغط هنا للاختيار", "Click here to select a csv file.": "أضغط هنا للاختيار ملف csv", "Click here to select a py file.": "", "Click here to upload a workflow.json file.": "", "click here.": "أضغط هنا", "Click on the user role button to change a user's role.": "أ<PERSON><PERSON><PERSON> على أسم الصلاحيات لتغيرها للمستخدم", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "", "Clone": "استنساخ", "Close": "أغلق", "Code execution": "", "Code formatted successfully": "", "Collection": "مجموعة", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "ComfyUI الرابط الافتراضي", "ComfyUI Base URL is required.": "ComfyUI الرابط مطلوب", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "الأوامر", "Completions": "", "Concurrent Requests": "الطلبات المتزامنة", "Configure": "", "Configure Models": "", "Confirm": "", "Confirm Password": "تأكيد كلمة المرور", "Confirm your action": "", "Confirm your new password": "", "Connections": "اتصالات", "console": "", "Contact Admin for WebUI Access": "", "Content": "الاتصال", "Content Extraction": "", "Context Length": "طول السياق", "Continue Response": "متابعة الرد", "Continue with {{provider}}": "", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Controls": "", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "", "Copied shared chat URL to clipboard!": "تم نسخ عنوان URL للدردشة المشتركة إلى الحافظة", "Copied to clipboard": "", "Copy": "نسخ", "Copy last code block": "انسخ كتلة التعليمات البرمجية الأخيرة", "Copy last response": "ان<PERSON><PERSON> الرد الأخير", "Copy Link": "أن<PERSON><PERSON> الرابط", "Copy to clipboard": "", "Copying to clipboard was successful!": "تم النسخ إلى الحافظة بنجاح", "Create": "", "Create a knowledge base": "", "Create a model": "إنشاء نموذج", "Create Account": "إنشاء حساب", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "", "Create new key": "ع<PERSON><PERSON> مفتاح جديد", "Create new secret key": "عمل سر جديد", "Created at": "أنشئت في", "Created At": "أنشئت من", "Created by": "", "CSV Import": "", "Current Model": "الموديل المختار", "Current Password": "كلمة السر الحالية", "Custom": "مخصص", "Dark": "مظلم", "Database": "قاعدة البيانات", "December": "ديسمبر", "Default": "الإفتراضي", "Default (Open AI)": "", "Default (SentenceTransformers)": "(SentenceTransformers) الإفتراضي", "Default Model": "النموذج الافتراضي", "Default model updated": "الإفتراضي تحديث الموديل", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "الإفتراضي Prompt الاقتراحات", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "الإفتراضي صلاحيات المستخدم", "Delete": "<PERSON><PERSON><PERSON>", "Delete a model": "<PERSON><PERSON><PERSON> الموديل", "Delete All Chats": "حذ<PERSON> جميع الدردشات", "Delete All Models": "", "Delete chat": "<PERSON><PERSON><PERSON> المح<PERSON><PERSON>ه", "Delete Chat": "حذ<PERSON> المحاد<PERSON>ه.", "Delete chat?": "", "Delete folder?": "", "Delete function?": "", "Delete Message": "", "Delete prompt?": "", "delete this link": "أحذ<PERSON> هذا الرابط", "Delete tool?": "", "Delete User": "<PERSON>ذ<PERSON> المستخدم", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} حذف", "Deleted {{name}}": "حذ<PERSON> {{name}}", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "وصف", "Disabled": "", "discover": "", "Discover a function": "", "Discover a model": "اكتشف نموذجا", "Discover a prompt": "اكتشاف موجه", "Discover a tool": "", "Discover wonders": "", "Discover, download, and explore custom functions": "", "Discover, download, and explore custom prompts": "اكتشاف وتنزيل واستكشاف المطالبات المخصصة", "Discover, download, and explore custom tools": "", "Discover, download, and explore model presets": "اكتشاف وتنزيل واستكشاف الإعدادات المسبقة للنموذج", "Dismissible": "", "Display": "", "Display Emoji in Call": "", "Display the username instead of You in the Chat": "اعرض اسم المستخدم بدلاً منك في الدردشة", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "", "Do not install tools from sources you do not fully trust.": "", "Document": "المستند", "Documentation": "", "Documents": "مستندات", "does not make any external connections, and your data stays securely on your locally hosted server.": "لا يجري أي اتصالات خارجية، وتظل بياناتك آمنة على الخادم المستضاف محليًا.", "Don't have an account?": "ليس لديك حساب؟", "don't install random functions from sources you don't trust.": "", "don't install random tools from sources you don't trust.": "", "Done": "", "Download": "تحميل", "Download canceled": "تم اللغاء التحميل", "Download Database": "تحميل قاعدة البيانات", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to add to the conversation": "أسقط أية ملفات هنا لإضافتها إلى المحادثة", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "e.g. '30s','10m'. الوحدات الزمنية الصالحة هي 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "تعديل", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "", "Edit User": "تعديل المستخدم", "Edit User Group": "", "ElevenLabs": "", "Email": "البريد", "Embark on adventures": "", "Embedding Batch Size": "", "Embedding Model": "نموذج التضمين", "Embedding Model Engine": "تضمين محرك النموذج", "Embedding model set to \"{{embedding_model}}\"": "تم تعيين نموذج التضمين على \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "تمكين مشاركة المجتمع", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "تفعيل عمليات التسجيل الجديدة", "Enable Web Search": "تمكين بحث الويب", "Enabled": "", "Engine": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "تأكد من أن ملف CSV الخاص بك يتضمن 4 أعمدة بهذا الترتيب: Name, Email, Password, Role.", "Enter {{role}} message here": "أدخل رسالة {{role}} هنا", "Enter a detail about yourself for your LLMs to recall": "ادخل معلومات عنك تريد أن يتذكرها الموديل", "Enter api auth string (e.g. username:password)": "", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "أدخل مفتاح واجهة برمجة تطبيقات البحث الشجاع", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "أ<PERSON><PERSON><PERSON> ال<PERSON><PERSON><PERSON> Overlap", "Enter Chunk Size": "أ<PERSON><PERSON><PERSON>k الحجم", "Enter description": "", "Enter Github Raw URL": "أدخل عنوان URL ل Github Raw", "Enter Google PSE API Key": "أدخل مفتاح واجهة برمجة تطبيقات PSE من Google", "Enter Google PSE Engine Id": "أدخل معرف محرك PSE من Google", "Enter Image Size (e.g. 512x512)": "(e.g. 512x512) أدخل حجم الصورة ", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "أ<PERSON><PERSON><PERSON> كود اللغة", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "(e.g. {{modelTag}}) أدخل الموديل تاق", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "(e.g. 50) أد<PERSON><PERSON> عدد الخطوات", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "أد<PERSON>ل النتيجة", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "أدخل عنوان URL لاستعلام Searxng", "Enter Seed": "", "Enter Serper API Key": "أدخل مفتاح واجهة برمجة تطبيقات <PERSON>per", "Enter Serply API Key": "", "Enter Serpstack API Key": "أدخل مفتاح واجهة برمجة تطبيقات Serpstack", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "أدخل تسلسل التوقف", "Enter system prompt": "", "Enter Tavily API Key": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "", "Enter Top K": "أدخل Top K", "Enter URL (e.g. http://127.0.0.1:7860/)": "الرابط (e.g. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "URL (e.g. http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "أد<PERSON><PERSON> البريد الاكتروني", "Enter Your Full Name": "أد<PERSON>ل الاسم كامل", "Enter your message": "", "Enter your new password": "", "Enter Your Password": "ادخل كلمة المرور", "Enter your prompt": "", "Enter Your Role": "أد<PERSON>ل الصلاحيات", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "خطأ", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "", "Experimental": "تجريبي", "Explore the cosmos": "", "Export": "تصدير", "Export All Archived Chats": "", "Export All Chats (All Users)": "تصدير جميع الدردشات (جميع المستخدمين)", "Export chat (.json)": "", "Export Chats": "تصدير جميع الدردشات", "Export Config to JSON File": "", "Export Functions": "", "Export Models": "نماذج التصدير", "Export Presets": "", "Export Prompts": "مطالبات التصدير", "Export to CSV": "", "Export Tools": "", "External Models": "", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "", "Failed to create API Key.": "فشل في إنشاء مفتاح API.", "Failed to read clipboard contents": "فشل في قراءة محتويات الحافظة", "Failed to save models configuration": "", "Failed to update settings": "", "February": "فبراير", "Feedback History": "", "Feedbacks": "", "File": "", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "وضع الملف", "File not found.": "لم يتم العثور على الملف.", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File uploaded successfully": "", "Files": "", "Filter is now globally disabled": "", "Filter is now globally enabled": "", "Filters": "", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "تم اكتشاف انتحال بصمة الإصبع: غير قادر على استخدام الأحرف الأولى كصورة رمزية. الافتراضي لصورة الملف الشخصي الافتراضية.", "Fluidly stream large external response chunks": "دفق قطع الاستجابة الخارجية الكبيرة بسلاسة", "Focus chat input": "التركيز على إدخال الدردشة", "Folder deleted successfully": "", "Folder name cannot be empty": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Forge new paths": "", "Form": "", "Format your variables using brackets like this:": "", "Frequency Penalty": "عقوبة التردد", "Function": "", "Function created successfully": "", "Function deleted successfully": "", "Function Description": "", "Function ID": "", "Function is now globally disabled": "", "Function is now globally enabled": "", "Function Name": "", "Function updated successfully": "", "Functions": "", "Functions allow arbitrary code execution": "", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "", "General": "عام", "General Settings": "الاعدادات العامة", "Generate Image": "", "Generating search query": "إنشاء استعلام بحث", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "", "Good Response": "استجابة جيدة", "Google Drive": "", "Google PSE API Key": "مفتاح واجهة برمجة تطبيقات PSE من Google", "Google PSE Engine Id": "معرف محرك PSE من Google", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "h:mm a": "الساعة:الدقائق صباحا/مساء", "Haptic Feedback": "", "Harmful or offensive": "", "has no conversations.": "ليس لديه محادثات.", "Hello, {{name}}": " {{name}} مرحبا", "Help": "مساعدة", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "أخفاء", "Host": "", "How can I help you today?": "كيف استطيع مساعدتك اليوم؟", "How would you rate this response?": "", "Hybrid Search": "البحث الهجين", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "توليد الصور (تجريبي)", "Image Generation Engine": "محرك توليد الصور", "Image Max Compression Size": "", "Image Settings": "إعدادات الصورة", "Images": "الصور", "Import Chats": "استيراد الدردشات", "Import Config from JSON File": "", "Import Functions": "", "Import Models": "استيراد النماذج", "Import Presets": "", "Import Prompts": "مطالبات الاستيراد", "Import Tools": "", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "قم بتضمين علامة `-api` عند تشغيل Stable-diffusion-webui", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "معلومات", "Input commands": "إدخال الأوامر", "Install from Github URL": "التثبيت من عنوان URL لجيثب", "Instant Auto-Send After Voice Transcription": "", "Interface": "واجهه المستخدم", "Invalid file format.": "", "Invalid Tag": "تاق غير صالحة", "is typing...": "", "January": "يناير", "Jina API Key": "", "join our Discord for help.": "انضم إلى Discord للحصول على المساعدة.", "JSON": "JSON", "JSON Preview": "معاينة JSON", "July": "يوليو", "June": "يونيو", "JWT Expiration": "JWT تجريبي", "JWT Token": "JWT Token", "Kagi Search API Key": "", "Keep Alive": "Keep Alive", "Key": "", "Keyboard shortcuts": "اختصارات لوحة المفاتيح", "Knowledge": "", "Knowledge Access": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Label": "", "Landing Page Mode": "", "Language": "اللغة", "Last Active": "آخر نشاط", "Last Modified": "", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Light": "فاتح", "Listening...": "", "Local": "", "Local Models": "", "Lost": "", "LTR": "من جهة اليسار إلى اليمين", "Made by OpenWebUI Community": "OpenWebUI تم إنشاؤه بواسطة مجتمع ", "Make sure to enclose them with": "تأكد من إرفاقها", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "", "Manage Arena Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "إدارة خطوط الأنابيب", "March": "مارس", "Max Tokens (num_predict)": "ماكس توكنز (num_predict)", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "يمكن تنزيل 3 نماذج كحد أقصى في وقت واحد. الرجاء معاودة المحاولة في وقت لاحق.", "May": "مايو", "Memories accessible by LLMs will be shown here.": "سيتم عرض الذكريات التي يمكن الوصول إليها بواسطة LLMs هنا.", "Memory": "الذاكرة", "Memory added successfully": "", "Memory cleared successfully": "", "Memory deleted successfully": "", "Memory updated successfully": "", "Merge Responses": "", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "لن تتم مشاركة الرسائل التي ترسلها بعد إنشاء الرابط الخاص بك. سيتمكن المستخدمون الذين لديهم عنوان URL من عرض الدردشة المشتركة", "Min P": "", "Minimum Score": "ال<PERSON><PERSON> الأدنى من النقاط", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "MMMM DD, YYYY", "MMMM DD, YYYY HH:mm": "MMMM DD, YYYY HH:mm", "MMMM DD, YYYY hh:mm:ss A": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "تم تحميل النموذج '{{modelName}}' بنجاح", "Model '{{modelTag}}' is already in queue for downloading.": "النموذج '{{modelTag}}' موجو<PERSON> بالفعل في قائمة الانتظار للتحميل", "Model {{modelId}} not found": "لم يتم العثور على النموذج {{modelId}}.", "Model {{modelName}} is not vision capable": "نموذج {{modelName}} غير قادر على الرؤية", "Model {{name}} is now {{status}}": "نموذج {{name}} هو الآن {{status}}", "Model accepts image inputs": "", "Model created successfully!": "", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "تم اكتشاف مسار نظام الملفات النموذجي. الاسم المختصر للنموذج مطلوب للتحديث، ولا يمكن الاستمرار.", "Model Filtering": "", "Model ID": "رقم الموديل", "Model IDs": "", "Model Name": "", "Model not selected": "لم تختار موديل", "Model Params": "معلمات النموذج", "Model Permissions": "", "Model updated successfully": "", "Modelfile Content": "محتوى الملف النموذجي", "Models": "الموديلات", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "", "More": "المزيد", "Name": "الأسم", "Name your knowledge base": "", "New Chat": "دردشة جديدة", "New folder": "", "New Password": "كلمة المرور الجديدة", "new-channel": "", "No content found": "", "No content to speak": "", "No distance available": "", "No feedbacks found": "", "No file selected": "", "No files found.": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No knowledge found": "", "No model IDs": "", "No models found": "", "No models selected": "", "No results found": "لا توجد نتايج", "No search query generated": "لم يتم إنشاء استعلام بحث", "No source available": "لا يوجد مصدر متاح", "No users were found.": "", "No valves to update": "", "None": "اي", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "ملاحظة: إذ<PERSON> قمت بتعيين الحد الأدنى من النقاط، فلن يؤدي البحث إلا إلى إرجاع المستندات التي لها نقاط أكبر من أو تساوي الحد الأدنى من النقاط.", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "إشعارات", "November": "نوفمبر", "num_gpu (Ollama)": "", "num_thread (Ollama)": "num_thread (أولاما)", "OAuth ID": "", "October": "اكتوبر", "Off": "أغلاق", "Okay, Let's Go!": "حسنا دعنا نذهب!", "OLED Dark": "OLED داكن", "Ollama": "Ollama", "Ollama API": "أولاما API", "Ollama API disabled": "أولاما API معطلة", "Ollama API settings updated": "", "Ollama Version": "<PERSON><PERSON><PERSON> الاصدار", "On": "تشغيل", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "يُسمح فقط بالأحرف الأبجدية الرقمية والواصلات في سلسلة الأمر.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "خطاء! يبدو أن عنوان URL غير صالح. يرجى التحقق مرة أخرى والمحاولة مرة أخرى.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "خطاء! أنت تستخدم طريقة غير مدعومة (الواجهة الأمامية فقط). يرجى تقديم واجهة WebUI من الواجهة الخلفية.", "Open in full screen": "", "Open new chat": "فتح محادثة جديده", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API إعدادات", "OpenAI API Key is required.": "OpenAI API.مطلوب مفتاح ", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "URL/مفتاح OpenAI.مطلوب عنوان ", "or": "أو", "Organize your users": "", "OUTPUT": "", "Output format": "", "Overview": "", "page": "", "Password": "الباسورد", "Paste Large Text as File": "", "PDF document (.pdf)": "PDF ملف (.pdf)", "PDF Extract Images (OCR)": "PDF أستخرج الصور (OCR)", "pending": "قيد الانتظار", "Permission denied when accessing media devices": "", "Permission denied when accessing microphone": "", "Permission denied when accessing microphone: {{error}}": "{{error}} تم رفض الإذن عند الوصول إلى الميكروفون ", "Permissions": "", "Personalization": "التخصيص", "Pin": "", "Pinned": "", "Pioneer insights": "", "Pipeline deleted successfully": "", "Pipeline downloaded successfully": "", "Pipelines": "خطوط الانابيب", "Pipelines Not Detected": "", "Pipelines Valves": "صمامات خطوط الأنابيب", "Plain text (.txt)": "نص عادي (.txt)", "Playground": "مكان التجربة", "Please carefully review the following warnings:": "", "Please enter a prompt": "", "Please fill in all fields.": "", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "أخر 30 يوم", "Previous 7 days": "أخر 7 أيام", "Profile Image": "صورة الملف الشخصي", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "موجه (على سبيل المثال: أخبرني بحقيقة ممتعة عن الإمبراطورية الرومانية)", "Prompt Content": "مح<PERSON><PERSON><PERSON> عاجل", "Prompt created successfully": "", "Prompt suggestions": "اقتراحات سريعة", "Prompt updated successfully": "", "Prompts": "مطالبات", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "Ollama.com \"{{searchValue}}\" أسحب من ", "Pull a model from Ollama.com": "Ollama.com سحب الموديل من ", "Query Generation Prompt": "", "Query Params": "Query Params", "RAG Template": "RAG تنمبلت", "Rating": "", "Re-rank models by topic similarity": "", "Read Aloud": "أقراء لي", "Record voice": "سجل صوت", "Redirecting you to OpenWebUI Community": "OpenWebUI إعادة توجيهك إلى مجتمع ", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "", "References from": "", "Refresh Token Expiration": "", "Regenerate": "تجديد", "Release Notes": "ملاحظات الإصدار", "Relevance": "", "Remove": "إزالة", "Remove Model": "<PERSON><PERSON><PERSON> الموديل", "Rename": "إعادة تسمية", "Reorder Models": "", "Repeat Last N": "N كرر آخر", "Reply in Thread": "", "Request Mode": "وضع الطلب", "Reranking Model": "إعادة تقييم النموذج", "Reranking model disabled": "تم تعطيل نموذج إعادة الترتيب", "Reranking model set to \"{{reranking_model}}\"": "تم ضبط نموذج إعادة الترتيب على \"{{reranking_model}}\"", "Reset": "", "Reset All Models": "", "Reset Upload Directory": "", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "", "Response splitting": "", "Result": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "من<PERSON><PERSON>", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "من اليمين إلى اليسار", "Run": "", "Running": "", "Save": "<PERSON><PERSON><PERSON>", "Save & Create": "حفظ وإنشاء", "Save & Update": "حفظ وتحديث", "Save As Copy": "", "Save Tag": "", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "لم يعد حفظ سجلات الدردشة مباشرة في مساحة تخزين متصفحك مدعومًا. يرجى تخصيص بعض الوقت لتنزيل وحذف سجلات الدردشة الخاصة بك عن طريق النقر على الزر أدناه. لا تقلق، يمكنك بسهولة إعادة استيراد سجلات الدردشة الخاصة بك إلى الواجهة الخلفية من خلاله", "Scroll to bottom when switching between branches": "", "Search": "البحث", "Search a model": "البحث عن موديل", "Search Base": "", "Search Chats": "البحث في الدردشات", "Search Collection": "", "Search Filters": "", "search for tags": "", "Search Functions": "", "Search Knowledge": "", "Search Models": "نماذج البحث", "Search options": "", "Search Prompts": "<PERSON><PERSON><PERSON><PERSON> حث", "Search Result Count": "عد<PERSON> نتائج البحث", "Search Tools": "", "Search users": "", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searxng Query URL": "عنوان URL لاستعلام Searxng", "See readme.md for instructions": "readme.md للحصول على التعليمات", "See what's new": "ما الجديد", "Seed": "Seed", "Select a base model": "حدد نموذجا أساسيا", "Select a engine": "", "Select a function": "", "Select a group": "", "Select a model": "أخ<PERSON><PERSON><PERSON> الموديل", "Select a pipeline": "<PERSON><PERSON><PERSON> مسارا", "Select a pipeline url": "حدد عنوان URL لخط الأنابيب", "Select a tool": "", "Select Engine": "", "Select Knowledge": "", "Select model": " أخ<PERSON><PERSON><PERSON> موديل", "Select only one model to call": "", "Selected model(s) do not support image inputs": "النموذج (النماذج) المحددة لا تدعم مدخلات الصور", "Semantic distance to query": "", "Send": "تم", "Send a message": "", "Send message": "يُرجى إدخال طلبك هنا.", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "سبتمبر", "Serper API Key": "مفتاح واجهة برمجة تطبيقات سيربر", "Serply API Key": "", "Serpstack API Key": "مفتاح واجهة برمجة تطبيقات Serpstack", "Server connection verified": "تم التحقق من اتصال الخادم", "Set as default": "الافتراضي", "Set CFG Scale": "", "Set Default Model": "تفعيد الموديل الافتراضي", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "ضبط نموذج المتجهات (على سبيل المثال: {{model}})", "Set Image Size": "حجم الصورة", "Set reranking model (e.g. {{model}})": "ضبط نموذج إعادة الترتيب (على سبيل المثال: {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "<PERSON>بط الخطوات", "Set Task Model": "تعيين نموذج المهمة", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "<PERSON>بط الصوت", "Set whisper model": "", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "الاعدادات", "Settings saved successfully!": "تم حفظ الاعدادات بنجاح", "Share": "كشاركة", "Share Chat": "مشاركة الدردشة", "Share to OpenWebUI Community": "OpenWebUI شارك في مجتمع", "Show": "<PERSON><PERSON><PERSON>", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "", "Show shortcuts": "إظهار الاختصارات", "Show your support!": "", "Sign in": "تسجيل الدخول", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "تسجيل الخروج", "Sign up": "تسجيل", "Sign up to {{WEBUI_NAME}}": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Source": "المصدر", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "{{error}} خطأ في التعرف على الكلام", "Speech-to-Text Engine": "محرك تحويل الكلام إلى نص", "Stop": "", "Stop Sequence": "وقف التسلسل", "Stream Chat Response": "", "STT Model": "", "STT Settings": "STT اعدادات", "Success": "نجاح", "Successfully updated.": "تم التحديث بنجاح", "Support": "", "Support this plugin:": "", "Sync directory": "", "System": "النظام", "System Instructions": "", "System Prompt": "محادثة النظام", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "", "Tavily API Key": "", "Temperature": "درجة حرارة", "Template": "نموذج", "Temporary Chat": "", "Text Splitter": "", "Text-to-Speech Engine": "محرك تحويل النص إلى كلام", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "شكرا لملاحظاتك!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "يجب أن تكون النتيجة قيمة تتراوح بين 0.0 (0%) و1.0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "الثيم", "Thinking...": "", "This action cannot be undone. Do you wish to continue?": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "وهذا يضمن حفظ محادثاتك القيمة بشكل آمن في قاعدة بياناتك الخلفية. شكرًا لك!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Tika": "", "Tika Server URL required.": "", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "ملاحضة: قم بتحديث عدة فتحات متغيرة على التوالي عن طريق الضغط على مفتاح tab في مدخلات الدردشة بعد كل استبدال.", "Title": "العنوان", "Title (e.g. Tell me a fun fact)": "(e.g. Tell me a fun fact) العناون", "Title Auto-Generation": "توليد تلقائي للعنوان", "Title cannot be an empty string.": "العنوان مطلوب", "Title Generation Prompt": "موجه إنشاء العنوان", "TLS": "", "To access the available model names for downloading,": "للوصول إلى أسماء الموديلات المتاحة للتنزيل،", "To access the GGUF models available for downloading,": "للوصول إلى الموديلات GGUF المتاحة للتنزيل،", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "", "To select toolkits here, add them to the \"Tools\" workspace first.": "", "Toast notifications for new updates": "", "Today": "اليوم", "Toggle settings": "فتح وأغلاق الاعدادات", "Toggle sidebar": "فتح وأغلاق الشريط الجانبي", "Toggle temporary chat": "", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "", "Tool created successfully": "", "Tool deleted successfully": "", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "", "Tool Name": "", "Tool updated successfully": "", "Tools": "", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Top K": "Top K", "Top P": "Top P", "Transformers": "", "Trouble accessing Ollama?": "هل تواجه مشكلة في الوصول", "TTS Model": "", "TTS Settings": "TTS اعدادات", "TTS Voice": "", "Type": "نوع", "Type Hugging Face Resolve (Download) URL": "اكتب عنوان URL لحل مشكلة الوجه (تنزيل).", "Uh-oh! There was an issue with the response.": "", "UI": "", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "", "Unravel secrets": "", "Untagged": "", "Update": "", "Update and Copy Link": "تحديث ونسخ الرابط", "Update for the latest features and improvements.": "", "Update password": "تحديث كلمة المرور", "Updated": "", "Updated at": "", "Updated At": "", "Upload": "", "Upload a GGUF model": "GGUF رفع موديل نوع", "Upload directory": "", "Upload files": "", "Upload Files": "تحميل الملفات", "Upload Pipeline": "", "Upload Progress": "جاري التحميل", "URL": "", "URL Mode": "رابط الموديل", "USAi Chat can make mistakes. Review all responses for accuracy. Your agency’s AI and privacy policies apply.": "", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "<PERSON>ra<PERSON><PERSON> أستخدم", "Use groups to group your users and assign permissions.": "", "Use Initials": "Initials أستخدم", "use_mlock (Ollama)": "use_mlock (أولاما)", "use_mmap (Ollama)": "use_mmap (أولاما)", "user": "مستخدم", "User": "", "User location successfully retrieved.": "", "Username": "", "Users": "المستخدمين", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "يستخدم", "Valid time units:": "وحدات زمنية صالحة:", "Valves": "", "Valves updated": "", "Valves updated successfully": "", "variable": "المتغير", "variable to have them replaced with clipboard content.": "متغير لاستبدالها بمحتوى الحافظة.", "Version": "إصدار", "Version {{selectedVersion}} of {{totalVersions}}": "", "Very bad": "", "View Replies": "", "Visibility": "", "Voice": "", "Voice Input": "", "Warning": "تحذير", "Warning:": "", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "تحذير: إذ<PERSON> قمت بتحديث أو تغيير نموذج التضمين الخاص بك، فستحتاج إلى إعادة استيراد كافة المستندات.", "Web": "Web", "Web API": "", "Web Loader Settings": "Web تحميل اعدادات", "Web Search": "<PERSON><PERSON><PERSON> الويب", "Web Search Engine": "محرك بحث الويب", "Web Search Query Generation": "", "Webhook URL": "Webhook الرابط", "WebUI Settings": "WebUI اعدادات", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "Welcome, {{name}}!": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "ما هو الجديد", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "", "Widescreen Mode": "", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "مساحة العمل", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "اكتب اقتراحًا سريعًا (على سبيل المثال، من أنت؟)", "Write a summary in 50 words that summarizes [topic or keyword].": "اكتب ملخصًا في 50 كلمة يلخص [الموضوع أو الكلمة الرئيسية]", "Write something...": "", "Write your model template content here": "", "Yesterday": "<PERSON><PERSON><PERSON>", "You": "انت", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "", "You cannot upload an empty file.": "", "You have no archived conversations.": "لا تملك محادثات محفوظه", "You have shared this chat": "تم مشاركة هذه المحادثة", "You're a helpful assistant.": "مساعدك المفيد هنا", "Your account status is currently pending activation.": "", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "Youtube", "Youtube Loader Settings": "Youtube تحميل اعدادات"}