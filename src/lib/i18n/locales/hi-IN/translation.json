{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' or '-1' बिना किसी समाप्ति के", "(e.g. `sh webui.sh --api --api-auth username_password`)": "", "(e.g. `sh webui.sh --api`)": "(e.g. `sh webui.sh --api`)", "(latest)": "(latest)", "{{ models }}": "{{ मॉडल }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "{{user}} की चैट", "{{webUIName}} Backend Required": "{{webUIName}} बैकएंड आवश्यक", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "चैट और वेब खोज क्वेरी के लिए शीर्षक उत्पन्न करने जैसे कार्य करते समय कार्य मॉडल का उपयोग किया जाता है", "a user": "एक उपयोगकर्ता", "About": "हमारे बारे में", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "खाता", "Account Activation Pending": "", "Actions": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "", "Add": "जोड़ें", "Add a model ID": "", "Add a short description about what this model does": "इस मॉडल के बारे में एक संक्षिप्त विवरण जोड़ें", "Add a tag": "एक टैग जोड़े", "Add Arena Model": "", "Add Connection": "", "Add Content": "", "Add content here": "", "Add custom prompt": "अनुकूल संकेत जोड़ें", "Add Files": "फाइलें जोड़ें", "Add Group": "", "Add Memory": "मेमोरी जोड़ें", "Add Model": "मॉडल जोड़ें", "Add Reaction": "", "Add Tag": "", "Add Tags": "टैगों को जोड़ें", "Add text content": "", "Add User": "उपयोगकर्ता जोड़ें", "Add User Group": "", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "इन सेटिंग्स को समायोजित करने से परिवर्तन सभी उपयोगकर्ताओं पर सार्वभौमिक रूप से लागू होंगे।", "admin": "व्यवस्थापक", "Admin": "", "Admin Panel": "व्यवस्थापक पैनल", "Admin Settings": "व्यवस्थापक सेटिंग्स", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "", "Advanced Parameters": "उन्नत पैरामीटर", "Advanced Params": "उन्नत परम", "All Documents": "सभी डॉक्यूमेंट्स", "All models deleted successfully": "", "Allow Chat Delete": "", "Allow Chat Deletion": "चैट हटाने की अनुमति दें", "Allow Chat Edit": "", "Allow File Upload": "", "Allow non-local voices": "", "Allow Temporary Chat": "", "Allow User Location": "", "Allow Voice Interruption in Call": "", "Allowed Endpoints": "", "Already have an account?": "क्या आपके पास पहले से एक खाता मौजूद है?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "एक सहायक", "and": "और", "and {{COUNT}} more": "", "and create a new shared link.": "और एक नई साझा लिंक बनाएं.", "api": "", "API Base URL": "एपीआई बेस यूआरएल", "API Key": "एपीआई कुंजी", "API Key created.": "एपीआई कुंजी बनाई गई", "API Key Endpoint Restrictions": "", "API keys": "एपीआई कुंजियाँ", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "अप्रैल", "Archive": "पुरालेख", "Archive All Chats": "सभी चैट संग्रहीत करें", "Archived Chats": "संग्रहीत चैट", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "क्या आपको यकीन है?", "Arena Models": "", "Artifacts": "", "Ask a question": "", "Assistant": "", "Attach file": "फ़ाइल atta", "Attribute for Username": "", "Audio": "ऑडियो", "August": "अगस्त", "Authenticate": "", "Auto-Copy Response to Clipboard": "क्लिपबोर्ड पर प्रतिक्रिया ऑटोकॉपी", "Auto-playback response": "ऑटो-प्लेबैक प्रतिक्रिया", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 बेस यूआरएल", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 का बेस यूआरएल आवश्यक है।", "Available list": "", "available!": "उपलब्ध!", "Azure AI Speech": "", "Azure Region": "", "Back": "पीछे", "Bad": "", "Bad Response": "ख़राब प्रतिक्रिया", "Banners": "बैनर", "Base Model (From)": "बेस मॉडल (से)", "Batch Size (num_batch)": "", "before": "पहले", "Beta": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "Brave सर्च एपीआई कुंजी", "By {{name}}": "", "Bypass SSL verification for Websites": "वेबसाइटों के लिए SSL सुनिश्चिती को छोड़ें", "Call": "", "Call feature is not supported when using Web STT engine": "", "Camera": "", "Cancel": "रद्<PERSON> करें", "Capabilities": "क्षमताओं", "Capture": "", "Certificate Path": "", "Change Password": "पासवर्ड बदलें", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "chat": "", "Chat": "चैट करें", "Chat Background Image": "", "Chat Bubble UI": "चैट बॉली", "Chat Controls": "", "Chat direction": "चैट दिशा", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "सभी चैट", "Check Again": "फिर से जाँचो", "Check for updates": "अपडेट के लिए जाँच", "Checking for updates...": "अपडेट के लिए जांच कर रहा है...", "Choose a model before saving...": "सहेजने से पहले एक मॉडल चुनें...", "Chunk Overlap": "चंक ओवरलैप", "Chunk Params": "चंक पैरामीटर्स", "Chunk Size": "चंक आकार", "Ciphers": "", "Citation": "उद्धरण", "Clear memory": "", "click here": "", "Click here for filter guides.": "", "Click here for help.": "सहायता के लिए यहां क्लिक करें।", "Click here to": "यहां क्लिक करें", "Click here to download user import template file.": "", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to select": "चयन करने के लिए यहां क्लिक करें।", "Click here to select a csv file.": "सीएसवी फ़ाइल का चयन करने के लिए यहां क्लिक करें।", "Click here to select a py file.": "", "Click here to upload a workflow.json file.": "", "click here.": "यहाँ क्लिक करें।", "Click on the user role button to change a user's role.": "उपयोगकर्ता की भूमिका बदलने के लिए उपयोगकर्ता भूमिका बटन पर क्लिक करें।", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "", "Clone": "क्लोन", "Close": "ब<PERSON><PERSON> करना", "Code execution": "", "Code formatted successfully": "", "Collection": "संग्रह", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "ComfyUI बेस यूआरएल", "ComfyUI Base URL is required.": "ComfyUI का बेस यूआरएल आवश्यक है", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "कमांड", "Completions": "", "Concurrent Requests": "समवर्ती अनुरोध", "Configure": "", "Configure Models": "", "Confirm": "", "Confirm Password": "पासवर्ड की पुष्टि कीजिये", "Confirm your action": "", "Confirm your new password": "", "Connections": "सम्बन्ध", "console": "", "Contact Admin for WebUI Access": "", "Content": "सामग्री", "Content Extraction": "", "Context Length": "प्रसंग की लंबाई", "Continue Response": "प्रतिक्रिया जारी रखें", "Continue with {{provider}}": "", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Controls": "", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "", "Copied shared chat URL to clipboard!": "साझा चैट URL को क्लिपबोर्ड पर कॉपी किया गया!", "Copied to clipboard": "", "Copy": "कॉपी", "Copy last code block": "अंतिम कोड ब्लॉक कॉपी करें", "Copy last response": "अंतिम प्रतिक्रिया कॉपी करें", "Copy Link": "लिंक को कॉपी करें", "Copy to clipboard": "", "Copying to clipboard was successful!": "क्लिपबोर्ड पर कॉपी बनाना सफल रहा!", "Create": "", "Create a knowledge base": "", "Create a model": "एक मॉडल बनाएं", "Create Account": "खाता बनाएं", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "", "Create new key": "नया क्रिप्टोग्राफिक क्षेत्र बनाएं", "Create new secret key": "नया क्रिप्टोग्राफिक क्षेत्र बनाएं", "Created at": "किस समय बनाया गया", "Created At": "किस समय बनाया गया", "Created by": "", "CSV Import": "", "Current Model": "वर्तमान मॉडल", "Current Password": "वर्तमान पासवर्ड", "Custom": "कस्टम संस्करण", "Dark": "डार्क", "Database": "डेटाबेस", "December": "डिसेंबर", "Default": "डिफ़ॉल्ट", "Default (Open AI)": "", "Default (SentenceTransformers)": "डिफ़ॉल्ट (SentenceTransformers)", "Default Model": "डिफ़ॉल्ट मॉडल", "Default model updated": "डिफ़ॉल्ट मॉडल अपडेट किया गया", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "डिफ़ॉल्ट प्रॉम्प्ट सुझाव", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "डिफ़ॉल्ट उपयोगकर्ता भूमिका", "Delete": "डिलीट", "Delete a model": "एक मॉडल हटाएँ", "Delete All Chats": "सभी चैट हटाएं", "Delete All Models": "", "Delete chat": "चैट हटाएं", "Delete Chat": "चैट हटाएं", "Delete chat?": "", "Delete folder?": "", "Delete function?": "", "Delete Message": "", "Delete prompt?": "", "delete this link": "इस लिंक को हटाएं", "Delete tool?": "", "Delete User": "उपभोक्ता मिटायें", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} हटा दिया गया", "Deleted {{name}}": "{{name}} हटा दिया गया", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "विवरण", "Disabled": "", "discover": "", "Discover a function": "", "Discover a model": "एक मॉडल की खोज करें", "Discover a prompt": "प्रॉम्प्ट खोजें", "Discover a tool": "", "Discover wonders": "", "Discover, download, and explore custom functions": "", "Discover, download, and explore custom prompts": "कस्टम प्रॉम्प्ट को खोजें, डाउनलोड करें और एक्सप्लोर करें", "Discover, download, and explore custom tools": "", "Discover, download, and explore model presets": "मॉडल प्रीसेट खोजें, डाउनलोड करें और एक्सप्लोर करें", "Dismissible": "", "Display": "", "Display Emoji in Call": "", "Display the username instead of You in the Chat": "चैट में 'आप' के स्थान पर उपयोगकर्ता नाम प्रदर्शित करें", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "", "Do not install tools from sources you do not fully trust.": "", "Document": "दस्तावेज़", "Documentation": "", "Documents": "दस्तावेज़", "does not make any external connections, and your data stays securely on your locally hosted server.": "कोई बाहरी कनेक्शन नहीं बनाता है, और आपका डेटा आपके स्थानीय रूप से होस्ट किए गए सर्वर पर सुरक्षित रूप से रहता है।", "Don't have an account?": "कोई खाता नहीं है?", "don't install random functions from sources you don't trust.": "", "don't install random tools from sources you don't trust.": "", "Done": "", "Download": "डाउनलोड", "Download canceled": "डाउनलोड रद्द किया गया", "Download Database": "डेटाबेस डाउनलोड करें", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to add to the conversation": "बातचीत में जोड़ने के लिए कोई भी फ़ाइल यहां छोड़ें", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "जैसे '30s', '10m', मान्य समय इकाइयाँ 's', 'm', 'h' हैं।", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "संपादित करें", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "", "Edit User": "यूजर को संपादित करो", "Edit User Group": "", "ElevenLabs": "", "Email": "ईमेल", "Embark on adventures": "", "Embedding Batch Size": "", "Embedding Model": "मॉडेल अनुकूलन", "Embedding Model Engine": "एंबेडिंग मॉडल इंजन", "Embedding model set to \"{{embedding_model}}\"": "एम्बेडिंग मॉडल को \"{{embedding_model}}\" पर सेट किया गया", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "समुदाय साझाकरण सक्षम करें", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "नए साइन अप सक्रिय करें", "Enable Web Search": "वेब खोज सक्षम करें", "Enabled": "", "Engine": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "सुनिश्चित करें कि आपकी CSV फ़ाइल में इस क्रम में 4 कॉलम शामिल हैं: नाम, ईमेल, पासवर्ड, भूमिका।", "Enter {{role}} message here": "यहां {{role}} संदेश दर्ज करें", "Enter a detail about yourself for your LLMs to recall": "अपने एलएलएम को याद करने के लिए अपने बारे में एक विवरण दर्ज करें", "Enter api auth string (e.g. username:password)": "", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "Brave सर्च एपीआई कुंजी डालें", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "चंक ओवरलैप दर्ज करें", "Enter Chunk Size": "खंड आकार दर्ज करें", "Enter description": "", "Enter Github Raw URL": "<PERSON><PERSON><PERSON> Raw URL दर्ज करें", "Enter Google PSE API Key": "Google PSE API कुंजी दर्ज करें", "Enter Google PSE Engine Id": "Google PSE इंजन आईडी दर्ज करें", "Enter Image Size (e.g. 512x512)": "छवि का आकार दर्ज करें (उदा. 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "भाषा कोड दर्ज करें", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "Model tag दर्ज करें (उदा. {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "चरणों की संख्या दर्ज करें (उदा. 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "स्कोर दर्ज करें", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "Searxng क्वेरी URL दर्ज करें", "Enter Seed": "", "Enter Serper API Key": "<PERSON><PERSON> API कुंजी दर्ज करें", "Enter Serply API Key": "", "Enter Serpstack API Key": "सर्पस्टैक एपीआई कुंजी दर्ज करें", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "स्टॉप अनुक्रम दर्ज करें", "Enter system prompt": "", "Enter Tavily API Key": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "", "Enter Top K": "शी<PERSON><PERSON><PERSON> K दर्ज करें", "Enter URL (e.g. http://127.0.0.1:7860/)": "यूआरएल दर्ज करें (उदा. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "यूआरएल दर्ज करें (उदा. http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "अपना ईमेल दर्ज करें", "Enter Your Full Name": "अपना पूरा नाम भरें", "Enter your message": "", "Enter your new password": "", "Enter Your Password": "अपना पासवर्ड भरें", "Enter your prompt": "", "Enter Your Role": "अपनी भूमिका दर्ज करें", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "चूक", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "", "Experimental": "प्रयोगात्मक", "Explore the cosmos": "", "Export": "निर्यातित माल", "Export All Archived Chats": "", "Export All Chats (All Users)": "सभी चैट निर्यात करें (सभी उपयोगकर्ताओं की)", "Export chat (.json)": "", "Export Chats": "चैट निर्यात करें", "Export Config to JSON File": "", "Export Functions": "", "Export Models": "निर्यात मॉडल", "Export Presets": "", "Export Prompts": "प्रॉम्प्ट निर्यात करें", "Export to CSV": "", "Export Tools": "", "External Models": "", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "", "Failed to create API Key.": "एपीआई कुंजी बनाने में विफल.", "Failed to read clipboard contents": "क्लिपबोर्ड सामग्री पढ़ने में विफल", "Failed to save models configuration": "", "Failed to update settings": "", "February": "फरवरी", "Feedback History": "", "Feedbacks": "", "File": "", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "फ़ाइल मोड", "File not found.": "फ़ाइल प्राप्त नहीं हुई।", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File uploaded successfully": "", "Files": "", "Filter is now globally disabled": "", "Filter is now globally enabled": "", "Filters": "", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "फ़िंगरप्रिंट स्पूफ़िंग का पता चला: प्रारंभिक अक्षरों को अवतार के रूप में उपयोग करने में असमर्थ। प्रोफ़ाइल छवि को डिफ़ॉल्ट पर डिफ़ॉल्ट किया जा रहा है.", "Fluidly stream large external response chunks": "बड़े बाह्य प्रतिक्रिया खंडों को तरल रूप से प्रवाहित करें", "Focus chat input": "चैट इनपुट पर फ़ोकस करें", "Folder deleted successfully": "", "Folder name cannot be empty": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Forge new paths": "", "Form": "", "Format your variables using brackets like this:": "", "Frequency Penalty": "फ्रीक्वेंसी पेनल्टी", "Function": "", "Function created successfully": "", "Function deleted successfully": "", "Function Description": "", "Function ID": "", "Function is now globally disabled": "", "Function is now globally enabled": "", "Function Name": "", "Function updated successfully": "", "Functions": "", "Functions allow arbitrary code execution": "", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "", "General": "सामान्य", "General Settings": "सामान्य सेटिंग्स", "Generate Image": "", "Generating search query": "खोज क्वेरी जनरेट करना", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "", "Good Response": "अच्छी प्रतिक्रिया", "Google Drive": "", "Google PSE API Key": "Google PSE API कुंजी", "Google PSE Engine Id": "Google PSE इंजन आईडी", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "h:mm a": "h:mm a", "Haptic Feedback": "", "Harmful or offensive": "", "has no conversations.": "कोई बातचीत नहीं है", "Hello, {{name}}": "नमस्ते, {{name}}", "Help": "मदद", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "छुपाएं", "Host": "", "How can I help you today?": "आज मैं आपकी कैसे मदद कर सकता हूँ?", "How would you rate this response?": "", "Hybrid Search": "हाइब्रिड खोज", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "छवि निर्माण (प्रायोगिक)", "Image Generation Engine": "छवि निर्माण इंजन", "Image Max Compression Size": "", "Image Settings": "छवि सेटिंग्स", "Images": "इमेजिस", "Import Chats": "चैट आयात करें", "Import Config from JSON File": "", "Import Functions": "", "Import Models": "आयात मॉडल", "Import Presets": "", "Import Prompts": "प्रॉम्प्ट आयात करें", "Import Tools": "", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "stable-diffusion-webui चलाते समय `--api` ध्वज शामिल करें", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "सूचना-विषयक", "Input commands": "इनपुट क命", "Install from Github URL": "<PERSON><PERSON><PERSON>RL से इंस्टॉल करें", "Instant Auto-Send After Voice Transcription": "", "Interface": "इंटरफेस", "Invalid file format.": "", "Invalid Tag": "अवैध टैग", "is typing...": "", "January": "<PERSON><PERSON><PERSON><PERSON>ी", "Jina API Key": "", "join our Discord for help.": "मदद के लिए हमारे डिस्कोर्ड में शामिल हों।", "JSON": "ज्ञान प्रकार", "JSON Preview": "JSON पूर्वावलोकन", "July": "जुलाई", "June": "जुन", "JWT Expiration": "JWT समाप्ति", "JWT Token": "जट टोकन", "Kagi Search API Key": "", "Keep Alive": "क्रिया<PERSON>ील रहो", "Key": "", "Keyboard shortcuts": "कीबोर्ड शॉर्टकट", "Knowledge": "", "Knowledge Access": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Label": "", "Landing Page Mode": "", "Language": "भाषा", "Last Active": "पिछली बार सक्रिय", "Last Modified": "", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Light": "सुन", "Listening...": "", "Local": "", "Local Models": "", "Lost": "", "LTR": "LTR", "Made by OpenWebUI Community": "OpenWebUI समुदाय द्वारा निर्मित", "Make sure to enclose them with": "उन्हें संलग्न करना सुनिश्चित करें", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "", "Manage Arena Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "पाइपलाइनों का प्रबंधन करें", "March": "मार<PERSON><PERSON>", "Max Tokens (num_predict)": "अधिकतम टोकन (num_predict)", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "अधिकतम 3 मॉडल एक साथ डाउनलोड किये जा सकते हैं। कृपया बाद में पुन: प्रयास करें।", "May": "मेई", "Memories accessible by LLMs will be shown here.": "एलएलएम द्वारा सुलभ यादें यहां दिखाई जाएंगी।", "Memory": "मेमोरी", "Memory added successfully": "", "Memory cleared successfully": "", "Memory deleted successfully": "", "Memory updated successfully": "", "Merge Responses": "", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "अपना लिंक बनाने के बाद आपके द्वारा भेजे गए संदेश साझा नहीं किए जाएंगे। यूआरएल वाले यूजर्स शेयर की गई चैट देख पाएंगे।", "Min P": "", "Minimum Score": "न्यूनतम स्कोर", "Mirostat": "मिरोस्टा", "Mirostat Eta": "मिरोस्टा ईटा", "Mirostat Tau": "मिरोस्तात ताऊ", "MMMM DD, YYYY": "MMMM DD, YYYY", "MMMM DD, YYYY HH:mm": "MMMM DD, YYYY HH:mm", "MMMM DD, YYYY hh:mm:ss A": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "मॉडल '{{modelName}}' सफलतापूर्वक डाउनलोड हो गया है।", "Model '{{modelTag}}' is already in queue for downloading.": "मॉडल '{{modelTag}}' पहले से ही डाउनलोड करने के लिए कतार में है।", "Model {{modelId}} not found": "मॉडल {{modelId}} नहीं मिला", "Model {{modelName}} is not vision capable": "मॉडल {{modelName}} दृष्टि सक्षम नहीं है", "Model {{name}} is now {{status}}": "मॉडल {{name}} अब {{status}} है", "Model accepts image inputs": "", "Model created successfully!": "", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "मॉडल फ़ाइल सिस्टम पथ का पता चला. अद्यतन के लिए मॉडल संक्षिप्त नाम आवश्यक है, जारी नहीं रखा जा सकता।", "Model Filtering": "", "Model ID": "मॉडल आईडी", "Model IDs": "", "Model Name": "", "Model not selected": "मॉडल चयनित नहीं है", "Model Params": "मॉडल Params", "Model Permissions": "", "Model updated successfully": "", "Modelfile Content": "मॉडल फ़ाइल सामग्री", "Models": "सभी मॉडल", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "", "More": "और..", "Name": "नाम", "Name your knowledge base": "", "New Chat": "नई चैट", "New folder": "", "New Password": "नया पासवर्ड", "new-channel": "", "No content found": "", "No content to speak": "", "No distance available": "", "No feedbacks found": "", "No file selected": "", "No files found.": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No knowledge found": "", "No model IDs": "", "No models found": "", "No models selected": "", "No results found": "कोई परिणाम नहीं मिला", "No search query generated": "कोई खोज क्वेरी जनरेट नहीं हुई", "No source available": "कोई स्रोत उपलब्ध नहीं है", "No users were found.": "", "No valves to update": "", "None": "कोई नहीं", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "ध्यान दें: यदि आप न्यूनतम स्कोर निर्धारित करते हैं, तो खोज केवल न्यूनतम स्कोर से अधिक या उसके बराबर स्कोर वाले दस्तावेज़ वापस लाएगी।", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "सूचनाएं", "November": "नवं<PERSON>र", "num_gpu (Ollama)": "", "num_thread (Ollama)": "num_thread (ओलामा)", "OAuth ID": "", "October": "अक्टूबर", "Off": "बंद", "Okay, Let's Go!": "ठीक है, चलिए चलते हैं!", "OLED Dark": "O<PERSON><PERSON><PERSON><PERSON>", "Ollama": "Ollama", "Ollama API": "ओलामा एपीआई", "Ollama API disabled": "ओलामा एपीआई अक्षम", "Ollama API settings updated": "", "Ollama Version": "Ollama Version", "On": "चालू", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "कमांड स्ट्रिंग में केवल अल्फ़ान्यूमेरिक वर्ण और हाइफ़न की अनुमति है।", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "उफ़! ऐसा लगता है कि यूआरएल अमान्य है. कृपया दोबारा जांचें और पुनः प्रयास करें।", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "उफ़! आप एक असमर्थित विधि (केवल फ्रंटएंड) का उपयोग कर रहे हैं। कृपया बैकएंड से WebUI सर्वे करें।", "Open in full screen": "", "Open new chat": "नई चैट खोलें", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API कॉन्फिग", "OpenAI API Key is required.": "OpenAI API कुंजी आवश्यक है", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "OpenAI URL/Key आवश्यक है।", "or": "या", "Organize your users": "", "OUTPUT": "", "Output format": "", "Overview": "", "page": "", "Password": "पासवर्ड", "Paste Large Text as File": "", "PDF document (.pdf)": "PDF दस्तावेज़ (.pdf)", "PDF Extract Images (OCR)": "PDF छवियाँ निकालें (OCR)", "pending": "लंबित", "Permission denied when accessing media devices": "", "Permission denied when accessing microphone": "", "Permission denied when accessing microphone: {{error}}": "माइक्रोफ़ोन तक पहुँचने पर अनुमति अस्वीकृत: {{error}}", "Permissions": "", "Personalization": "पेरसनलाइज़मेंट", "Pin": "", "Pinned": "", "Pioneer insights": "", "Pipeline deleted successfully": "", "Pipeline downloaded successfully": "", "Pipelines": "पाइपलाइनों", "Pipelines Not Detected": "", "Pipelines Valves": "पाइपलाइन वाल्व", "Plain text (.txt)": "सादा पाठ (.txt)", "Playground": "कार्यक्षेत्र", "Please carefully review the following warnings:": "", "Please enter a prompt": "", "Please fill in all fields.": "", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "पिछले 30 दिन", "Previous 7 days": "पिछले 7 दिन", "Profile Image": "प्रोफ़ाइल छवि", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "प्रॉम्प्ट (उदाहरण के लिए मुझे रोमन साम्राज्य के बारे में एक मजेदार तथ्य बताएं)", "Prompt Content": "प्रॉम्प्ट सामग्री", "Prompt created successfully": "", "Prompt suggestions": "प्रॉम्प्ट सुझाव", "Prompt updated successfully": "", "Prompts": "प्रॉम्प्ट", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "\"{{searchValue}}\" को Ollama.com से खींचें", "Pull a model from Ollama.com": "Ollama.com से एक मॉडल खींचें", "Query Generation Prompt": "", "Query Params": "क्वेरी पैरामीटर", "RAG Template": "RAG टेम्पलेट", "Rating": "", "Re-rank models by topic similarity": "", "Read Aloud": "जोर से पढ़ें", "Record voice": "आवाज रिकॉर्ड करना", "Redirecting you to OpenWebUI Community": "आपको OpenWebUI समुदाय पर पुनर्निर्देशित किया जा रहा है", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "", "References from": "", "Refresh Token Expiration": "", "Regenerate": "पुनः जेनरेट", "Release Notes": "रिलीज नोट्स", "Relevance": "", "Remove": "हटा दें", "Remove Model": "मोडेल हटाएँ", "Rename": "नाम बदलें", "Reorder Models": "", "Repeat Last N": "अंतिम N दोहराएँ", "Reply in Thread": "", "Request Mode": "अनुरोध मोड", "Reranking Model": "रीरैकिंग मोड", "Reranking model disabled": "पुनर्रैंकिंग मॉडल अक्षम किया गया", "Reranking model set to \"{{reranking_model}}\"": "रीरैंकिंग मॉडल को \"{{reranking_model}}\" पर ​​सेट किया गया", "Reset": "", "Reset All Models": "", "Reset Upload Directory": "", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "", "Response splitting": "", "Result": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "भूमिका", "Rosé Pine": "रोसे पिन", "Rosé Pine Dawn": "रोसे पिन डेन", "RTL": "RTL", "Run": "", "Running": "", "Save": "सहेजें", "Save & Create": "सहेजें और बनाएं", "Save & Update": "सहेजें और अपडेट करें", "Save As Copy": "", "Save Tag": "", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "चैट लॉग को सीधे आपके ब्राउज़र के स्टोरेज में सहेजना अब समर्थित नहीं है। कृपया नीचे दिए गए बटन पर क्लिक करके डाउनलोड करने और अपने चैट लॉग को हटाने के लिए कुछ समय दें। चिंता न करें, आप आसानी से अपने चैट लॉग को बैकएंड पर पुनः आयात कर सकते हैं", "Scroll to bottom when switching between branches": "", "Search": "खोजें", "Search a model": "एक मॉडल खोजें", "Search Base": "", "Search Chats": "चैट खोजें", "Search Collection": "", "Search Filters": "", "search for tags": "", "Search Functions": "", "Search Knowledge": "", "Search Models": "मॉडल खोजें", "Search options": "", "Search Prompts": "प्रॉम्प्ट खोजें", "Search Result Count": "खोज परिणामों की संख्या", "Search Tools": "", "Search users": "", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searxng Query URL": "Searxng क्वेरी URL", "See readme.md for instructions": "निर्देशों के लिए readme.md देखें", "See what's new": "देखें, क्या नया है", "Seed": "सीड्‌", "Select a base model": "एक आधार मॉडल का चयन करें", "Select a engine": "", "Select a function": "", "Select a group": "", "Select a model": "एक मॉडल चुनें", "Select a pipeline": "एक पाइपलाइन का चयन करें", "Select a pipeline url": "एक पाइपलाइन url चुनें", "Select a tool": "", "Select Engine": "", "Select Knowledge": "", "Select model": "मॉडल चुनें", "Select only one model to call": "", "Selected model(s) do not support image inputs": "चयनित मॉडल छवि इनपुट का समर्थन नहीं करते हैं", "Semantic distance to query": "", "Send": "भेज", "Send a message": "", "Send message": "मेसेज भेजें", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "सितं<PERSON>र", "Serper API Key": "<PERSON><PERSON> कुंजी", "Serply API Key": "", "Serpstack API Key": "सर्पस्टैक एपीआई कुंजी", "Server connection verified": "सर्वर कनेक्शन सत्यापित", "Set as default": "डिफाल्ट के रूप में सेट", "Set CFG Scale": "", "Set Default Model": "डिफ़ॉल्ट मॉडल सेट करें", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "ईम्बेडिंग मॉडल सेट करें (उदाहरण: {{model}})", "Set Image Size": "छवि का आकार सेट करें", "Set reranking model (e.g. {{model}})": "रीकरण मॉडल सेट करें (उदाहरण: {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "चरण निर्धारित करें", "Set Task Model": "कार्य मॉडल सेट करें", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "आवाज सेट करें", "Set whisper model": "", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "सेटिंग्स", "Settings saved successfully!": "सेटिंग्स सफलतापूर्वक सहेजी गईं!", "Share": "साझा करें", "Share Chat": "चैट साझा करें", "Share to OpenWebUI Community": "OpenWebUI समुदाय में साझा करें", "Show": "दिखाओ", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "", "Show shortcuts": "शॉर्टकट दिखाएँ", "Show your support!": "", "Sign in": "साइन इन", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "साइन आउट", "Sign up": "साइन अप", "Sign up to {{WEBUI_NAME}}": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Source": "स्रोत", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "वाक् पहचान त्रुटि: {{error}}", "Speech-to-Text Engine": "वाक्-से-पाठ इंजन", "Stop": "", "Stop Sequence": "अनुक्रम रोकें", "Stream Chat Response": "", "STT Model": "", "STT Settings": "STT सेटिंग्स ", "Success": "संपन्न", "Successfully updated.": "सफलतापूर्वक उत्परिवर्तित।", "Support": "", "Support this plugin:": "", "Sync directory": "", "System": "सिस्टम", "System Instructions": "", "System Prompt": "सिस्टम प्रॉम्प्ट", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "", "Tavily API Key": "", "Temperature": "टेंपेरेचर", "Template": "टेम्पलेट", "Temporary Chat": "", "Text Splitter": "", "Text-to-Speech Engine": "टेक्स्ट-टू-स्पीच इंजन", "Tfs Z": "टफ्स Z", "Thanks for your feedback!": "आपकी प्रतिक्रिया के लिए धन्यवाद!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "स्कोर का मान 0.0 (0%) और 1.0 (100%) के बीच होना चाहिए।", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "थीम", "Thinking...": "", "This action cannot be undone. Do you wish to continue?": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "यह सुनिश्चित करता है कि आपकी मूल्यवान बातचीत आपके बैकएंड डेटाबेस में सुरक्षित रूप से सहेजी गई है। धन्यवाद!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Tika": "", "Tika Server URL required.": "", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "टिप: प्रत्येक प्रतिस्थापन के बाद चैट इनपुट में टैब कुंजी दबाकर लगातार कई वैरिएबल स्लॉट अपडेट करें।", "Title": "शीर्षक", "Title (e.g. Tell me a fun fact)": "शीर्षक (उदा. मुझे एक मज़ेदार तथ्य बताएं)", "Title Auto-Generation": "शीर्षक ऑटो-जेनरेशन", "Title cannot be an empty string.": "शीर्षक नहीं खाली पाठ हो सकता है.", "Title Generation Prompt": "शीर्षक जनरेशन प्रॉम्प्ट", "TLS": "", "To access the available model names for downloading,": "डाउनलोड करने के लिए उपलब्ध मॉडल नामों तक पहुंचने के लिए,", "To access the GGUF models available for downloading,": "डाउनलोडिंग के लिए उपलब्ध GGUF मॉडल तक पहुँचने के लिए,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "", "To select toolkits here, add them to the \"Tools\" workspace first.": "", "Toast notifications for new updates": "", "Today": "आज", "Toggle settings": "सेटिंग्स टॉगल करें", "Toggle sidebar": "साइडबार टॉगल करें", "Toggle temporary chat": "", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "", "Tool created successfully": "", "Tool deleted successfully": "", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "", "Tool Name": "", "Tool updated successfully": "", "Tools": "", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Top K": "शी<PERSON><PERSON><PERSON>  <PERSON>", "Top P": "शीर<PERSON><PERSON>  P", "Transformers": "", "Trouble accessing Ollama?": "<PERSON><PERSON><PERSON> तक पहुँचने में परेशानी हो रही है?", "TTS Model": "", "TTS Settings": "TTS सेटिंग्स", "TTS Voice": "", "Type": "प्र<PERSON><PERSON>र", "Type Hugging Face Resolve (Download) URL": "हगिंग फेस रिज़ॉल्व (डाउनलोड) यूआरएल टाइप करें", "Uh-oh! There was an issue with the response.": "", "UI": "", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "", "Unravel secrets": "", "Untagged": "", "Update": "", "Update and Copy Link": "अपडेट करें और लिंक कॉपी करें", "Update for the latest features and improvements.": "", "Update password": "पासवर्ड अपडेट करें", "Updated": "", "Updated at": "", "Updated At": "", "Upload": "", "Upload a GGUF model": "GGUF मॉडल अपलोड करें", "Upload directory": "", "Upload files": "", "Upload Files": "फ़ाइलें अपलोड करें", "Upload Pipeline": "", "Upload Progress": "प्रगति अपलोड करें", "URL": "", "URL Mode": "URL मोड", "USAi Chat can make mistakes. Review all responses for accuracy. Your agency’s AI and privacy policies apply.": "", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "<PERSON>ra<PERSON>ar का प्रयोग करें", "Use groups to group your users and assign permissions.": "", "Use Initials": "प्रथमाक्षर का प्रयोग करें", "use_mlock (Ollama)": "use_mlock (ओलामा)", "use_mmap (Ollama)": "use_mmap (ओलामा)", "user": "उपयोगकर्ता", "User": "", "User location successfully retrieved.": "", "Username": "", "Users": "उपयोगकर्ताओं", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "उपयोग करें", "Valid time units:": "मान्य समय इकाइयाँ:", "Valves": "", "Valves updated": "", "Valves updated successfully": "", "variable": "वेरिएबल", "variable to have them replaced with clipboard content.": "उन्हें क्लिपबोर्ड सामग्री से बदलने के लिए वेरिएबल।", "Version": "संस्करण", "Version {{selectedVersion}} of {{totalVersions}}": "", "Very bad": "", "View Replies": "", "Visibility": "", "Voice": "", "Voice Input": "", "Warning": "चेतावनी", "Warning:": "", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "चेतावनी: यदि आप अपने एम्बेडिंग मॉडल को अपडेट या बदलते हैं, तो आपको सभी दस्तावेज़ों को फिर से आयात करने की आवश्यकता होगी।", "Web": "वेब", "Web API": "", "Web Loader Settings": "वेब लोडर सेटिंग्स", "Web Search": "वेब खोज", "Web Search Engine": "वेब खोज इंजन", "Web Search Query Generation": "", "Webhook URL": "वेबहुक URL", "WebUI Settings": "WebUI सेटिंग्स", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "Welcome, {{name}}!": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "इसमें नया क्या है", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "", "Widescreen Mode": "", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "वर्कस्पेस", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "एक त्वरित सुझाव लिखें (जैसे कि आप कौन हैं?)", "Write a summary in 50 words that summarizes [topic or keyword].": "50 शब्दों में एक सारांश लिखें जो [विषय या कीवर्ड] का सारांश प्रस्तुत करता हो।", "Write something...": "", "Write your model template content here": "", "Yesterday": "कल", "You": "आप", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "", "You cannot upload an empty file.": "", "You have no archived conversations.": "आपको कोई अंकित चैट नहीं है।", "You have shared this chat": "आपने इस चैट को शेयर किया है", "You're a helpful assistant.": "आप एक सहायक सहायक हैं", "Your account status is currently pending activation.": "", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "Youtube", "Youtube Loader Settings": "यूट्यूब लोडर सेटिंग्स"}