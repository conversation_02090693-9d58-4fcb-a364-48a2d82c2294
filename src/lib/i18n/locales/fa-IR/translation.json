{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' یا '-1' برای غیر فعال کردن انقضا.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "", "(e.g. `sh webui.sh --api`)": "(e.g. `sh webui.sh --api`)", "(latest)": "(آخ<PERSON><PERSON>ن)", "{{ models }}": "{{ models }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "{{user}} گفتگوهای", "{{webUIName}} Backend Required": "بکند {{webUIName}} نیاز است.", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "یک مدل وظیفه هنگام انجام وظایف مانند تولید عناوین برای چت ها و نمایش های جستجوی وب استفاده می شود.", "a user": "ی<PERSON> کاربر", "About": "درباره", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "حساب کاربری", "Account Activation Pending": "فعال‌سازی حساب در حال انتظار", "Actions": "کنش‌ها", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "کاربران فعال", "Add": "اضافه کردن", "Add a model ID": "", "Add a short description about what this model does": "افزودن توضیحات کوتاه در مورد انچه که این مدل انجام می دهد", "Add a tag": "افزودن یک برچسب", "Add Arena Model": "افزو<PERSON>ن مدل Arena", "Add Connection": "", "Add Content": "افزودن محتوا", "Add content here": "محتوا را اینجا اضافه کنید", "Add custom prompt": "افزودن یک درخواست سفارشی", "Add Files": "افزودن فایل‌ها", "Add Group": "", "Add Memory": "افزودن حافظه", "Add Model": "افزو<PERSON>ن مدل", "Add Reaction": "", "Add Tag": "افزودن برچسب", "Add Tags": "افزودن برچسب‌ها", "Add text content": "افزودن محتوای متنی", "Add User": "افزودن کاربر", "Add User Group": "", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "با تنظیم این تنظیمات، تغییرات به طور کلی برای همه کاربران اعمال می‌شود.", "admin": "مدیر", "Admin": "مدیر", "Admin Panel": "پنل مدیریت", "Admin Settings": "تنظیمات مدیریت", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "", "Advanced Parameters": "پارامترهای پیشرفته", "Advanced Params": "پارام‌های پیشرفته", "All Documents": "همهٔ سند‌ها", "All models deleted successfully": "", "Allow Chat Delete": "", "Allow Chat Deletion": "اجازهٔ حذف گفتگو", "Allow Chat Edit": "", "Allow File Upload": "", "Allow non-local voices": "", "Allow Temporary Chat": "اجازهٔ گفتگوی موقتی", "Allow User Location": "اجازهٔ موقعیت مکانی کاربر", "Allow Voice Interruption in Call": "", "Allowed Endpoints": "", "Already have an account?": "از قبل حساب کاربری دارید؟", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "یک دستیار", "and": "و", "and {{COUNT}} more": "و {{COUNT}} مورد دیگر", "and create a new shared link.": "و یک پیوند اشتراک‌گذاری جدید ایجاد کنید.", "api": "", "API Base URL": "نشانی پایهٔ API", "API Key": "کلید API", "API Key created.": "کلید API ساخته شد.", "API Key Endpoint Restrictions": "", "API keys": "کلیدهای API", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "آوریل", "Archive": "بایگانی", "Archive All Chats": "بایگانی همه گفتگوها", "Archived Chats": "گفتگوهای بایگانی‌شده", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "مطمئنید؟", "Arena Models": "", "Artifacts": "", "Ask a question": "سوالی بپرسید", "Assistant": "دستیار", "Attach file": "پیوست پرونده", "Attribute for Username": "", "Audio": "صدا", "August": "آگوست", "Authenticate": "", "Auto-Copy Response to Clipboard": "ک<PERSON>ی خودکار پاسخ به کلیپ بورد", "Auto-playback response": "پخش خودکار پاسخ", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "", "AUTOMATIC1111 Base URL": "پایه URL AUTOMATIC1111 ", "AUTOMATIC1111 Base URL is required.": "به URL پایه AUTOMATIC1111 مورد نیاز است.", "Available list": "فهرست دردسترس", "available!": "در دسترس!", "Azure AI Speech": "سخنگوی هوش‌مصنوعی Azure", "Azure Region": "منطقهٔ Azure", "Back": "بازگشت", "Bad": "", "Bad Response": "پاسخ خوب نیست", "Banners": "بنر", "Base Model (From)": "مدل پایه (از)", "Batch Size (num_batch)": "", "before": "قبل", "Beta": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "کلید API جستجوی شجاع", "By {{name}}": "", "Bypass SSL verification for Websites": "عبور از تأیید SSL برای وب سایت ها", "Call": "", "Call feature is not supported when using Web STT engine": "", "Camera": "دوربین", "Cancel": "لغو", "Capabilities": "قابلیت", "Capture": "", "Certificate Path": "", "Change Password": "تغییر رمز عبور", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "chat": "", "Chat": "گفتگو", "Chat Background Image": "تصویر پس‌زمینهٔ گفتگو", "Chat Bubble UI": "رابط کاربری حبابی گفتگو", "Chat Controls": "کنترل‌های گفتگو", "Chat direction": "جهت‌گفتگو", "Chat Overview": "نمای کلی گفتگو", "Chat Permissions": "", "Chat Tags Auto-Generation": "تو<PERSON><PERSON>د خودکار برچسب‌های گفتگو", "Chats": "گفتگو‌ها", "Check Again": "بررسی دوباره", "Check for updates": "بررسی به‌روزرسانی", "Checking for updates...": "در حال بررسی برای به‌روزرسانی..", "Choose a model before saving...": "قبل از ذخیره یک مدل را انتخاب کنید...", "Chunk Overlap": "همپوشانی تکه", "Chunk Params": "پارامترهای تکه", "Chunk Size": "اندازه تکه", "Ciphers": "", "Citation": "استناد", "Clear memory": "پاک کردن حافظه", "click here": "", "Click here for filter guides.": "", "Click here for help.": "برای کمک اینجا را کلیک کنید.", "Click here to": "برای کمک اینجا را کلیک کنید.", "Click here to download user import template file.": "", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to select": "برای انتخاب اینجا کلیک کنید", "Click here to select a csv file.": "برای انتخاب یک فایل csv اینجا را کلیک کنید.", "Click here to select a py file.": "", "Click here to upload a workflow.json file.": "", "click here.": "اینجا کلیک کنید.", "Click on the user role button to change a user's role.": "برای تغییر نقش کاربر، روی دکمه نقش کاربر کلیک کنید.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "", "Clone": "کلون", "Close": "بسته", "Code execution": "", "Code formatted successfully": "", "Collection": "مجموعه", "Color": "", "ComfyUI": "کومیوآی", "ComfyUI API Key": "", "ComfyUI Base URL": "URL پایه کومیوآی", "ComfyUI Base URL is required.": "URL پایه کومیوآی الزامی است.", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "دستور", "Completions": "", "Concurrent Requests": "درخواست های همزمان", "Configure": "", "Configure Models": "", "Confirm": "تا<PERSON><PERSON>د", "Confirm Password": "تا<PERSON>ید رمز عبور", "Confirm your action": "", "Confirm your new password": "", "Connections": "ارتباطات", "console": "", "Contact Admin for WebUI Access": "برای دسترسی به WebUI با مدیر تماس بگیرید", "Content": "محتوا", "Content Extraction": "استخراج محتوا", "Context Length": "طول زمینه", "Continue Response": "ادامه پاسخ", "Continue with {{provider}}": "با {{provider}} ادامه دهید", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Controls": "کنترل‌ها", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "کپی شد", "Copied shared chat URL to clipboard!": "URL چت به کلیپ بورد کپی شد!", "Copied to clipboard": "به بریده‌دان کپی‌شد", "Copy": "کپی", "Copy last code block": "کپی آخرین بلوک کد", "Copy last response": "کپی آخرین پاسخ", "Copy Link": "کپی لینک", "Copy to clipboard": "", "Copying to clipboard was successful!": "کپی کردن در کلیپ بورد با موفقیت انجام شد!", "Create": "", "Create a knowledge base": "", "Create a model": "ایج<PERSON> یک مدل", "Create Account": "ساخت حساب کاربری", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "", "Create new key": "ساخت کلید جدید", "Create new secret key": "ساخت کلید gehez جدید", "Created at": "ایجاد شده در", "Created At": "ایجاد شده در", "Created by": "ایجاد شده توسط", "CSV Import": "درون‌ریزی CSV", "Current Model": "مدل فعلی", "Current Password": "ر<PERSON>ز عبور فعلی", "Custom": "دلخواه", "Dark": "تیره", "Database": "پایگاه داده", "December": "دسامبر", "Default": "پیش<PERSON><PERSON>ض", "Default (Open AI)": "", "Default (SentenceTransformers)": "پیشفرض (SentenceTransformers)", "Default Model": "مدل پیشفرض", "Default model updated": "مدل پیشفرض به‌روزرسانی شد", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "پیشنهادات پرامپت پیش فرض", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "نقش کاربر پیش فرض", "Delete": "<PERSON><PERSON><PERSON>", "Delete a model": "حذ<PERSON> <PERSON><PERSON> مدل", "Delete All Chats": "حذف همه گفتگوها", "Delete All Models": "", "Delete chat": "حذ<PERSON> گپ", "Delete Chat": "حذ<PERSON> گپ", "Delete chat?": "", "Delete folder?": "", "Delete function?": "", "Delete Message": "", "Delete prompt?": "", "delete this link": "حذ<PERSON> این لینک", "Delete tool?": "", "Delete User": "<PERSON><PERSON><PERSON> کاربر", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} پاک شد", "Deleted {{name}}": "<PERSON><PERSON><PERSON> ش<PERSON> {{name}}", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "توضیحات", "Disabled": "", "discover": "", "Discover a function": "", "Discover a model": "کش<PERSON> یک مدل", "Discover a prompt": "یک اعلان را کشف کنید", "Discover a tool": "", "Discover wonders": "", "Discover, download, and explore custom functions": "", "Discover, download, and explore custom prompts": "پرامپت‌های سفارشی را کشف، دانلود و کاوش کنید", "Discover, download, and explore custom tools": "", "Discover, download, and explore model presets": "پیش تنظیمات مدل را کشف، دانلود و کاوش کنید", "Dismissible": "", "Display": "", "Display Emoji in Call": "", "Display the username instead of You in the Chat": "نمایش نام کاربری به جای «شما» در چت", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "", "Do not install tools from sources you do not fully trust.": "", "Document": "سند", "Documentation": "", "Documents": "اسناد", "does not make any external connections, and your data stays securely on your locally hosted server.": "هیچ اتصال خارجی ایجاد نمی کند و داده های شما به طور ایمن در سرور میزبان محلی شما باقی می ماند.", "Don't have an account?": "حساب کاربری ندارید؟", "don't install random functions from sources you don't trust.": "", "don't install random tools from sources you don't trust.": "", "Done": "", "Download": "د<PERSON><PERSON><PERSON><PERSON> کن", "Download canceled": "دانلود لغو شد", "Download Database": "دانلود پایگاه داده", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to add to the conversation": "هر فایلی را اینجا رها کنید تا به مکالمه اضافه شود", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "به طور مثال '30s','10m'. واحد‌های زمانی معتبر 's', 'm', 'h' هستند.", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "ویرایش", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "", "Edit User": "ویرایش کاربر", "Edit User Group": "", "ElevenLabs": "", "Email": "ایمیل", "Embark on adventures": "", "Embedding Batch Size": "", "Embedding Model": "مدل پیدائش", "Embedding Model Engine": "محرک مدل پیدائش", "Embedding model set to \"{{embedding_model}}\"": "مدل پیدائش را به \"{{embedding_model}}\" تنظیم کنید", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "فعالسازی اشتراک انجمن", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "فعال کردن ثبت نام‌های جدید", "Enable Web Search": "فعالسازی جستجوی وب", "Enabled": "", "Engine": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "اطمینان حاصل کنید که فایل CSV شما شامل چهار ستون در این ترتیب است: نام، ایمیل، رمز عبور، نقش.", "Enter {{role}} message here": "پیام {{role}} را اینجا وارد کنید", "Enter a detail about yourself for your LLMs to recall": "برای ذخیره سازی اطلاعات خود، یک توضیح کوتاه درباره خود را وارد کنید", "Enter api auth string (e.g. username:password)": "", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "کلید API جستجوی شجاع را وارد کنید", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "مق<PERSON><PERSON><PERSON>k <PERSON>lap را وارد کنید", "Enter Chunk Size": "مق<PERSON><PERSON><PERSON> Chun<PERSON> را وارد کنید", "Enter description": "", "Enter Github Raw URL": "ادر<PERSON> Github Raw را وارد کنید", "Enter Google PSE API Key": "کلید API گوگل PSE را وارد کنید", "Enter Google PSE Engine Id": "شناسه موتور PSE گوگل را وارد کنید", "Enter Image Size (e.g. 512x512)": "اندازه تصویر را وارد کنید (مثال: 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "کد زبان را وارد کنید", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "تگ مدل را وارد کنید (مثلا {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "تعداد گام ها را وارد کنید (مثال: 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "امتیاز را وارد کنید", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "نشانی وب پرسوجوی Searxng را وارد کنید", "Enter Seed": "", "Enter Serper API Key": "کلید API Serper را وارد کنید", "Enter Serply API Key": "", "Enter Serpstack API Key": "کلید API Serpstack را وارد کنید", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "توالی توقف را وارد کنید", "Enter system prompt": "", "Enter Tavily API Key": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "", "Enter Top K": "مقدار Top K را وارد کنید", "Enter URL (e.g. http://127.0.0.1:7860/)": "مقدار URL را وارد کنید (مثال http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "مقدار URL را وارد کنید (مثال http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "ایمیل خود را وارد کنید", "Enter Your Full Name": "نام کامل خود را وارد کنید", "Enter your message": "", "Enter your new password": "", "Enter Your Password": "رمز عبور خود را وارد کنید", "Enter your prompt": "", "Enter Your Role": "نقش خود را وارد کنید", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "خطا", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "", "Experimental": "آزمایشی", "Explore the cosmos": "", "Export": "برون‌ریزی", "Export All Archived Chats": "", "Export All Chats (All Users)": "برون‌ریزی همه گفتگو‌ها (همه کاربران)", "Export chat (.json)": "برون‌ریزی گفتگو (json)", "Export Chats": "برون‌ریزی گفتگوها", "Export Config to JSON File": "برون‌ریزی پیکربندی به پروندهٔ JSON", "Export Functions": "برون‌ریزی توابع", "Export Models": "برون‌ریزی مدل‌ها", "Export Presets": "", "Export Prompts": "برون‌ریزی پرامپت‌ها", "Export to CSV": "", "Export Tools": "برون‌ریزی ابزارها", "External Models": "مدل‌های بیرونی", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "خطا در افزودن پرونده", "Failed to create API Key.": "ایجاد کلید API با خطا مواجه شد.", "Failed to read clipboard contents": "خو<PERSON>دن محتوای کلیپ بورد ناموفق بود", "Failed to save models configuration": "", "Failed to update settings": "خطا در به‌روزرسانی تنظیمات", "February": "فوریه", "Feedback History": "تاریخچهٔ بازخورد", "Feedbacks": "", "File": "پرونده", "File added successfully.": "پرونده با موفقیت افزوده شد.", "File content updated successfully.": "محتوای پرونده با موفقیت به‌روز شد.", "File Mode": "حالت پرونده", "File not found.": "پرونده یافت نشد.", "File removed successfully.": "پرونده با موفقیت حذف شد.", "File size should not exceed {{maxSize}} MB.": "حجم پرونده نبایستی از {{maxSize}} MB بیشتر باشد.", "File uploaded successfully": "", "Files": "پرونده‌ها", "Filter is now globally disabled": "", "Filter is now globally enabled": "", "Filters": "", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "فانگ سرفیس شناسایی شد: نمی توان از نمایه شما به عنوان آواتار استفاده کرد. پیش فرض به عکس پروفایل پیش فرض برگشت داده شد.", "Fluidly stream large external response chunks": "تکه های پاسخ خارجی بزرگ را به صورت سیال پخش کنید", "Focus chat input": "فوکوس کردن ورودی گپ", "Folder deleted successfully": "", "Folder name cannot be empty": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Forge new paths": "", "Form": "", "Format your variables using brackets like this:": "", "Frequency Penalty": "مجازات فرکانس", "Function": "", "Function created successfully": "", "Function deleted successfully": "", "Function Description": "", "Function ID": "", "Function is now globally disabled": "", "Function is now globally enabled": "", "Function Name": "", "Function updated successfully": "", "Functions": "", "Functions allow arbitrary code execution": "", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "درون‌ریزی توابع با موفقیت انجام شد", "General": "عمومی", "General Settings": "تنظیمات عمومی", "Generate Image": "", "Generating search query": "در حال تولید پرسوجوی جستجو", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "", "Good Response": "پا<PERSON><PERSON> خوب", "Google Drive": "", "Google PSE API Key": "گوگل PSE API کلید", "Google PSE Engine Id": "شناسه موتور PSE گوگل", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "h:mm a": "h:mm a", "Haptic Feedback": "", "Harmful or offensive": "", "has no conversations.": "ندارد.", "Hello, {{name}}": "سلام، {{name}}", "Help": "کمک", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "پنهان‌سازی", "Host": "", "How can I help you today?": "امروز چطور می توانم کمک تان کنم؟", "How would you rate this response?": "", "Hybrid Search": "جستجوی همزمان", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "تولید تصویر (آزمایشی)", "Image Generation Engine": "موتور تولید تصویر", "Image Max Compression Size": "", "Image Settings": "تنظیمات تصویر", "Images": "تصاویر", "Import Chats": "درون‌ریزی گفتگوها", "Import Config from JSON File": "درون‌ریزی از پروندهٔ JSON", "Import Functions": "درون‌ریزی توابع", "Import Models": "درون‌ریزی مدل‌ها", "Import Presets": "", "Import Prompts": "درون‌ریزی پرامپت‌ها", "Import Tools": "درون‌ریزی ابزارها", "Include": "شامل", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "فلگ `--api` را هنکام اجرای stable-diffusion-webui استفاده کنید.", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "اطلاعات", "Input commands": "ورودی دستورات", "Install from Github URL": "نصب از ادر<PERSON>", "Instant Auto-Send After Voice Transcription": "", "Interface": "رابط", "Invalid file format.": "", "Invalid Tag": "تگ نامعتبر", "is typing...": "", "January": "ژانویه", "Jina API Key": "", "join our Discord for help.": "برای کمک به دیسکورد ما بپیوندید.", "JSON": "JSON", "JSON Preview": "پیش نمایش JSON", "July": "ژوئن", "June": "جو<PERSON><PERSON><PERSON>", "JWT Expiration": "JWT انقضای", "JWT Token": "JWT توکن", "Kagi Search API Key": "", "Keep Alive": "Keep Alive", "Key": "", "Keyboard shortcuts": "میانبرهای صفحه کلید", "Knowledge": "", "Knowledge Access": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Label": "", "Landing Page Mode": "", "Language": "زبان", "Last Active": "آخرین فعال", "Last Modified": "", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Light": "روشن", "Listening...": "", "Local": "", "Local Models": "", "Lost": "", "LTR": "LTR", "Made by OpenWebUI Community": "ساخته شده توسط OpenWebUI Community", "Make sure to enclose them with": "مطمئن شوید که آنها را با این محصور کنید:", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "", "Manage Arena Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "مدی<PERSON><PERSON><PERSON> خطوط لوله", "March": "ما<PERSON><PERSON>", "Max Tokens (num_predict)": "توکنهای بیشینه (num_predict)", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "حداکثر 3 مدل را می توان به طور همزمان دانلود کرد. لطفاً بعداً دوباره امتحان کنید.", "May": "ماهی", "Memories accessible by LLMs will be shown here.": "حافظه های دسترسی به LLMs در اینجا نمایش داده می شوند.", "Memory": "حافظه", "Memory added successfully": "", "Memory cleared successfully": "", "Memory deleted successfully": "", "Memory updated successfully": "", "Merge Responses": "", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "پیام های شما بعد از ایجاد لینک شما به اشتراک نمی گردد. کاربران با لینک URL می توانند چت اشتراک را مشاهده کنند.", "Min P": "", "Minimum Score": "نماد کمینه", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "MMMM DD, YYYY", "MMMM DD, YYYY HH:mm": "MMMM DD, YYYY HH:mm", "MMMM DD, YYYY hh:mm:ss A": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "مدل '{{modelName}}' با موفقیت دانلود شد.", "Model '{{modelTag}}' is already in queue for downloading.": "مدل '{{modelTag}}' در حال حاضر در صف برای دانلود است.", "Model {{modelId}} not found": "مدل {{modelId}} یافت نشد", "Model {{modelName}} is not vision capable": "مدل {{modelName}} قادر به بینایی نیست", "Model {{name}} is now {{status}}": "مدل {{name}} در حال حاضر {{status}}", "Model accepts image inputs": "", "Model created successfully!": "", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "مسیر فایل سیستم مدل یافت شد. برای بروزرسانی نیاز است نام کوتاه مدل وجود داشته باشد.", "Model Filtering": "", "Model ID": "شناسه مدل", "Model IDs": "", "Model Name": "", "Model not selected": "مدل انتخاب نشده", "Model Params": "مدل پارامز", "Model Permissions": "", "Model updated successfully": "", "Modelfile Content": "محتویات فایل مدل", "Models": "مدل‌ها", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "", "More": "بیشتر", "Name": "نام", "Name your knowledge base": "", "New Chat": "گپ جدید", "New folder": "", "New Password": "<PERSON><PERSON><PERSON> عبور جدید", "new-channel": "", "No content found": "", "No content to speak": "", "No distance available": "", "No feedbacks found": "", "No file selected": "", "No files found.": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No knowledge found": "", "No model IDs": "", "No models found": "", "No models selected": "", "No results found": "نتیجه‌ای یافت نشد", "No search query generated": "پرسوجوی جستجویی ایجاد نشده است", "No source available": "منبعی در دسترس نیست", "No users were found.": "", "No valves to update": "", "None": "ه<PERSON>چ کدام", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "توجه: اگر حداقل نمره را تعیین کنید، جستجو تنها اسنادی را با نمره بیشتر یا برابر با حداقل نمره باز می گرداند.", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "اعلان", "November": "نوامبر", "num_gpu (Ollama)": "", "num_thread (Ollama)": "num_thread (اولاما)", "OAuth ID": "", "October": "اکتبر", "Off": "خاموش", "Okay, Let's Go!": "باشه، بزن بریم!", "OLED Dark": "OLED تیره", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API disabled": "API Ollama غیرفعال شد", "Ollama API settings updated": "", "Ollama Version": "نسخه اولاما", "On": "روشن", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "فقط کاراکترهای الفبایی و خط فاصله در رشته فرمان مجاز هستند.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "اوه! به نظر می رسد URL نامعتبر است. لطفاً دوباره بررسی کنید و دوباره امتحان کنید.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "اوه! شما از یک روش پشتیبانی نشده (فقط frontend) استفاده می کنید. لطفاً WebUI را از بکند اجرا کنید.", "Open in full screen": "", "Open new chat": "باز کردن گپ جدید", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API Config", "OpenAI API Key is required.": "مقدار کلید OpenAI API مورد نیاز است.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "URL/Key OpenAI مورد نیاز است.", "or": "یا", "Organize your users": "", "OUTPUT": "خروجی", "Output format": "قالب خروجی", "Overview": "نمای کلی", "page": "صفحه", "Password": "<PERSON><PERSON><PERSON> عبور", "Paste Large Text as File": "", "PDF document (.pdf)": "PDF سند (.pdf)", "PDF Extract Images (OCR)": "استخراج تصاویر از PDF (OCR)", "pending": "در انتظار", "Permission denied when accessing media devices": "", "Permission denied when accessing microphone": "", "Permission denied when accessing microphone: {{error}}": "هنگام دسترسی به میکروفون، اجازه داده نشد: {{error}}", "Permissions": "", "Personalization": "شخصی سازی", "Pin": "", "Pinned": "", "Pioneer insights": "", "Pipeline deleted successfully": "", "Pipeline downloaded successfully": "", "Pipelines": "<PERSON>ط لوله", "Pipelines Not Detected": "", "Pipelines Valves": "شیرا<PERSON>ات خطوط لوله", "Plain text (.txt)": "متن ساده (.txt)", "Playground": "ز<PERSON>ین بازی", "Please carefully review the following warnings:": "", "Please enter a prompt": "", "Please fill in all fields.": "", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "30 روز قبل", "Previous 7 days": "7 روز قبل", "Profile Image": "تصویر پروفایل", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "پیشنهاد (برای مثال: به من بگوید چیزی که برای من یک کاربرد داره درباره ایران)", "Prompt Content": "محتویات پرامپت", "Prompt created successfully": "", "Prompt suggestions": "پیشنهادات پرامپت", "Prompt updated successfully": "", "Prompts": "پرامپت‌ها", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "بازگرداندن \"{{searchValue}}\" از Ollama.com", "Pull a model from Ollama.com": "دریافت یک مدل از Ollama.com", "Query Generation Prompt": "", "Query Params": "پارامترهای پرس و جو", "RAG Template": "RAG الگوی", "Rating": "", "Re-rank models by topic similarity": "", "Read Aloud": "خو<PERSON>دن به صورت صوتی", "Record voice": "<PERSON><PERSON><PERSON> صدا", "Redirecting you to OpenWebUI Community": "در حال هدایت به OpenWebUI Community", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "", "References from": "", "Refresh Token Expiration": "", "Regenerate": "ری‌<PERSON><PERSON><PERSON>ی", "Release Notes": "یادداشت‌های انتشار", "Relevance": "ارتباط", "Remove": "<PERSON><PERSON><PERSON>", "Remove Model": "<PERSON><PERSON><PERSON> مدل", "Rename": "تغییر نام", "Reorder Models": "", "Repeat Last N": "Repeat Last N", "Reply in Thread": "", "Request Mode": "حالت درخواست", "Reranking Model": "مدل ری‌شناسی مجدد غیرفعال است", "Reranking model disabled": "مدل ری‌شناسی مجدد غیرفعال است", "Reranking model set to \"{{reranking_model}}\"": "مدل ری‌شناسی مجدد به \"{{reranking_model}}\" تنظیم شده است", "Reset": "", "Reset All Models": "", "Reset Upload Directory": "", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "", "Response splitting": "", "Result": "نتیجه", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "نقش", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "اجرا", "Running": "", "Save": "ذخیره", "Save & Create": "ذخیره و ایجاد", "Save & Update": "ذخیره و به‌روزرسانی", "Save As Copy": "ذخیره به صویت رونوشت", "Save Tag": "ذخیرهٔ برچسب", "Saved": "ذخیره شد", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "ذخیره گزارش‌های چت مستقیماً در حافظه مرورگر شما دیگر پشتیبانی نمی‌شود. لطفاً با کلیک بر روی دکمه زیر، چند لحظه برای دانلود و حذف گزارش های چت خود وقت بگذارید. نگران نباشید، شما به راحتی می توانید گزارش های چت خود را از طریق بکند دوباره وارد کنید", "Scroll to bottom when switching between branches": "", "Search": "جستجو", "Search a model": "جستجوی یک مدل", "Search Base": "", "Search Chats": "جستجو گفتگوها", "Search Collection": "جستجوی مجموعه‌ها", "Search Filters": "", "search for tags": "جستجو برای برچسب‌ها", "Search Functions": "جستجوی توابع", "Search Knowledge": "جستجوی دانش", "Search Models": "جستجوی مدل‌ها", "Search options": "", "Search Prompts": "جستجوی پرامپت‌ها", "Search Result Count": "تعداد نتایج جستجو", "Search Tools": "ابزارهای جستجو", "Search users": "", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "جستجوی «{{searchQuery}}»", "Searching Knowledge for \"{{searchQuery}}\"": "جستجوی دانش برای «{{searchQuery}}»", "Searxng Query URL": "نشانی وب جستجوی Searxng", "See readme.md for instructions": "برای مشاهده دستورالعمل‌ها به readme.md مراجعه کنید", "See what's new": "ب<PERSON><PERSON><PERSON><PERSON>د موارد جدید چه بوده", "Seed": "", "Select a base model": "انتخاب یک مدل پایه", "Select a engine": "انتخاب یک موتور", "Select a function": "انتخاب یک تابع", "Select a group": "", "Select a model": "انتخاب یک مدل", "Select a pipeline": "انتخاب یک خط لوله", "Select a pipeline url": "یک ادرس خط لوله را انتخاب کنید", "Select a tool": "انتخاب یک ابقزار", "Select Engine": "انتخاب موتور", "Select Knowledge": "انتخاب دانش", "Select model": "انتخاب یک مدل", "Select only one model to call": "تنها یک مدل را برای صدا زدن انتخاب کنید", "Selected model(s) do not support image inputs": "مدل) های (انتخاب شده ورودیهای تصویر را پشتیبانی نمیکند", "Semantic distance to query": "", "Send": "ارسال", "Send a message": "", "Send message": "ارسال پیام", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "سپتامبر", "Serper API Key": "کلید API Serper", "Serply API Key": "", "Serpstack API Key": "کلید API Serpstack", "Server connection verified": "اتصال سرور تأیید شد", "Set as default": "تنظیم به عنوان پیشفرض", "Set CFG Scale": "", "Set Default Model": "تنظیم مدل پیش فرض", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "تنظیم مدل پیچشی (برای مثال {{model}})", "Set Image Size": "تنظیم اندازه تصویر", "Set reranking model (e.g. {{model}})": "تنظیم مدل ری‌راینگ (برای مثال {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "تنظیم گام‌ها", "Set Task Model": "تنظیم مدل تکلیف", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "تنظیم صدا", "Set whisper model": "", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "تنظیمات", "Settings saved successfully!": "تنظیمات با موفقیت ذخیره شد!", "Share": "اشتراک‌گذاری", "Share Chat": "اشتراک‌گذاری چت", "Share to OpenWebUI Community": "اشتراک گذاری با OpenWebUI Community", "Show": "نمایش", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "", "Show shortcuts": "نمایش میانبرها", "Show your support!": "", "Sign in": "ورود", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "خروج", "Sign up": "ثبت نام", "Sign up to {{WEBUI_NAME}}": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Source": "منبع", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "خطای تشخیص گفتار: {{error}}", "Speech-to-Text Engine": "موتور گفتار به متن", "Stop": "توقف", "Stop Sequence": "توقف توالی", "Stream Chat Response": "", "STT Model": "مدل تبدیل صدا به متن", "STT Settings": "تنظیمات تبدیل صدا به متن", "Success": "مو<PERSON><PERSON><PERSON>ت", "Successfully updated.": "با موفقیت به‌روز شد", "Support": "حما<PERSON>ت", "Support this plugin:": "حمایت از این افزونه", "Sync directory": "هم‌گام‌سازی پوشه", "System": "سیستم", "System Instructions": "", "System Prompt": "پرامپت سیستم", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "", "Tavily API Key": "", "Temperature": "دما", "Template": "الگو", "Temporary Chat": "", "Text Splitter": "", "Text-to-Speech Engine": "موتور تبدیل متن به گفتار", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "با تشکر از بازخورد شما!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "امتیاز باید یک مقدار بین 0.0 (0%) و 1.0 (100%) باشد.", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "پوسته", "Thinking...": "در حال فکر...", "This action cannot be undone. Do you wish to continue?": "این اقدام قابل بازگردانی نیست. برای ادامه اطمینان دارید؟", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "این تضمین می کند که مکالمات ارزشمند شما به طور ایمن در پایگاه داده بکند ذخیره می شود. تشکر!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Tika": "", "Tika Server URL required.": "", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "با فشردن کلید Tab در ورودی چت پس از هر بار تعویض، چندین متغیر را به صورت متوالی به روزرسانی کنید.", "Title": "عنوان", "Title (e.g. Tell me a fun fact)": "عنوان (برای مثال: به من بگوید چیزی که دوست دارید)", "Title Auto-Generation": "تو<PERSON><PERSON><PERSON> خودکار عنوان", "Title cannot be an empty string.": "عنوان نمی تواند یک رشته خالی باشد.", "Title Generation Prompt": "پرامپت تولید عنوان", "TLS": "", "To access the available model names for downloading,": "برای دسترسی به نام مدل های موجود برای دانلود،", "To access the GGUF models available for downloading,": "برای دسترسی به مدل‌های GGUF موجود برای دانلود،", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "", "To select toolkits here, add them to the \"Tools\" workspace first.": "", "Toast notifications for new updates": "", "Today": "امروز", "Toggle settings": "نمایش/عدم نمایش تنظیمات", "Toggle sidebar": "نمایش/عدم نمایش نوار کناری", "Toggle temporary chat": "", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "", "Tool created successfully": "", "Tool deleted successfully": "", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "", "Tool Name": "", "Tool updated successfully": "", "Tools": "", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Top K": "Top K", "Top P": "Top P", "Transformers": "", "Trouble accessing Ollama?": "در دسترسی به اولاما مشکل دارید؟", "TTS Model": "", "TTS Settings": "تنظیمات TTS", "TTS Voice": "", "Type": "نوع", "Type Hugging Face Resolve (Download) URL": "مقدار URL دانلود (Resolve) Hugging Face را وارد کنید", "Uh-oh! There was an issue with the response.": "", "UI": "", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "", "Unravel secrets": "", "Untagged": "", "Update": "به‌روزرسانی", "Update and Copy Link": "به روزرسانی و کپی لینک", "Update for the latest features and improvements.": "", "Update password": "به روزرسانی رمزعبور", "Updated": "بارگذاری شد", "Updated at": "بارگذاری در", "Updated At": "بارگذاری در", "Upload": "بارگذاری", "Upload a GGUF model": "آپلود یک مدل GGUF", "Upload directory": "", "Upload files": "", "Upload Files": "بارگذاری پروندهها", "Upload Pipeline": "", "Upload Progress": "پیشرفت آپلود", "URL": "", "URL Mode": "حالت URL", "USAi Chat can make mistakes. Review all responses for accuracy. Your agency’s AI and privacy policies apply.": "", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "استفاده از گراواتار", "Use groups to group your users and assign permissions.": "", "Use Initials": "استفاده از سرواژه", "use_mlock (Ollama)": "use_mlock (اولاما)", "use_mmap (Ollama)": "use_mmap (اولاما)", "user": "کاربر", "User": "کاربر", "User location successfully retrieved.": "موقعیت مکانی کاربر با موفقیت دریافت شد.", "Username": "", "Users": "کاربران", "Using the default arena model with all models. Click the plus button to add custom models.": "در حال استفاده از مدل آرنا با همهٔ مدل‌های دیگر به طور پیش‌فرض. برای افزودن مدل‌های سفارشی، روی دکمه به‌علاوه کلیک کنید.", "Utilize": "استفاده کنید", "Valid time units:": "واحدهای زمانی معتبر:", "Valves": "", "Valves updated": "", "Valves updated successfully": "", "variable": "متغ<PERSON>ر", "variable to have them replaced with clipboard content.": "متغیر برای جایگزینی آنها با محتوای بریده‌دان.", "Version": "نسخه", "Version {{selectedVersion}} of {{totalVersions}}": "نسخهٔ {{selectedVersion}} از {{totalVersions}}", "Very bad": "", "View Replies": "", "Visibility": "", "Voice": "صوت", "Voice Input": "ورودی صوتی", "Warning": "هشدار", "Warning:": "هشدار", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "هشدار: اگر شما به روز کنید یا تغییر دهید مدل شما، باید تمام سند ها را مجددا وارد کنید.", "Web": "وب", "Web API": "", "Web Loader Settings": "تنظیمات لودر وب", "Web Search": "جستجوی وب", "Web Search Engine": "موتور جستجوی وب", "Web Search Query Generation": "", "Webhook URL": "نشانی وب‌هوک", "WebUI Settings": "تنظیمات WebUI", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "Welcome, {{name}}!": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "موا<PERSON><PERSON> جدید در", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "", "Widescreen Mode": "حالت صفحهٔ عریض", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "<PERSON><PERSON><PERSON><PERSON> کار", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "یک پیشنهاد پرامپت بنویسید (مثلاً شما کی هستید؟)", "Write a summary in 50 words that summarizes [topic or keyword].": "خلاصه ای در 50 کلمه بنویسید که [موضوع یا کلمه کلیدی] را خلاصه کند.", "Write something...": "", "Write your model template content here": "", "Yesterday": "دی<PERSON><PERSON><PERSON>", "You": "شما", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "شما در هر زمان نهایتا می‌توانید با {{maxCount}} پرونده گفتگو کنید.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "", "You cannot upload an empty file.": "", "You have no archived conversations.": "شما هیچ گفتگوی ذخیره شده ندارید.", "You have shared this chat": "شما این گفتگو را به اشتراک گذاشته اید", "You're a helpful assistant.": "تو یک دستیار سودمند هستی.", "Your account status is currently pending activation.": "", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "یوتیوب", "Youtube Loader Settings": "تنظیمات لودر یوتیوب"}