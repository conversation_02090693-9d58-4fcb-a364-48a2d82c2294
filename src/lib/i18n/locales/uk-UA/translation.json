{"-1 for no limit, or a positive integer for a specific limit": "-1 для без обмежень або додатне ціле число для конкретного обмеження", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' or '-1' для відсутності терміну дії.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(e.g. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(e.g. `sh webui.sh --api`)", "(latest)": "(остання)", "{{ models }}": "{{ models }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "Чати {{user}}а", "{{webUIName}} Backend Required": "Необхідно підключення бекенду {{webUIName}}", "*Prompt node ID(s) are required for image generation": "*Для генерації зображення потрібно вказати ідентифікатор(и) вузла(ів)", "A new version (v{{LATEST_VERSION}}) is now available.": "Нова версія (v{{LATEST_VERSION}}) зараз доступна.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Модель задач використовується при виконанні таких завдань, як генерація заголовків для чатів та пошукових запитів в Інтернеті", "a user": "користувача", "About": "Про програму", "Access": "Доступ", "Access Control": "Контроль доступу", "Accessible to all users": "Доступно всім користувачам", "Account": "Обліковий запис", "Account Activation Pending": "Очікування активації облікового запису", "Actions": "Дії", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Активуйте цю команду, ввівши \"/{{COMMAND}}\" у введення чату.", "Active Users": "Активні користувачі", "Add": "Додати", "Add a model ID": "Додайти ID моделі", "Add a short description about what this model does": "Додайте короткий опис того, що робить ця модель", "Add a tag": "Додайти тег", "Add Arena Model": "Додати модель Arena", "Add Connection": "Додати з'єднання", "Add Content": "Додати вміст", "Add content here": "Додайте вміст сюди", "Add custom prompt": "Додати користувацьку підказку", "Add Files": "Додати файли", "Add Group": "Додати групу", "Add Memory": "Додати пам'ять", "Add Model": "Додати модель", "Add Reaction": "", "Add Tag": "Додати тег", "Add Tags": "Додати теги", "Add text content": "Додати текстовий вміст", "Add User": "Додати користувача", "Add User Group": "Додати групу користувачів", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "Зміни в цих налаштуваннях будуть застосовані для всіх користувачів.", "admin": "адмін", "Admin": "А<PERSON><PERSON><PERSON><PERSON>", "Admin Panel": "Адмін-панель", "Admin Settings": "Адмін-панель", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Адміністратори мають доступ до всіх інструментів у будь-який час; користувачам потрібні інструменти, призначені для кожної моделі в робочій області.", "Advanced Parameters": "Розширені параметри", "Advanced Params": "Розширені параметри", "All Documents": "Усі документи", "All models deleted successfully": "Всі моделі видалені успішно", "Allow Chat Delete": "Дозволити видалення чату", "Allow Chat Deletion": "Дозволити видалення чату", "Allow Chat Edit": "Дозволити редагування чату", "Allow File Upload": "Дозволити завантаження файлів", "Allow non-local voices": "Дозволити не локальні голоси", "Allow Temporary Chat": "Дозволити тимчасовий чат", "Allow User Location": "Доступ до місцезнаходження", "Allow Voice Interruption in Call": "Дозволити переривання голосу під час виклику", "Allowed Endpoints": "", "Already have an account?": "Вже є обліковий запис?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "Альтернатива параметру top_p, яка має на меті забезпечити баланс якості та різноманітності. Параметр p представляє мінімальну ймовірність для того, щоб токен був врахований, відносно ймовірності найбільш ймовірного токена. Наприклад, при p=0.05 і найбільш імовірному токені з ймовірністю 0.9, логіти зі значенням менше 0.045 будуть відфільтровані. (За замовчуванням: 0.0)", "an assistant": "асистента", "and": "та", "and {{COUNT}} more": "та ще {{COUNT}}", "and create a new shared link.": "і створіть нове спільне посилання.", "api": "", "API Base URL": "URL-адреса API", "API Key": "Ключ API", "API Key created.": "Ключ API створено.", "API Key Endpoint Restrictions": "", "API keys": "Ключі API", "Application DN": "DN застосунку", "Application DN Password": "Пароль DN застосунку", "applies to all users with the \"user\" role": "стосується всіх користувачів з роллю \"користувач\"", "April": "Квітень", "Archive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Archive All Chats": "Архівувати всі чати", "Archived Chats": "Архівовані чати", "archived-chat-export": "експорт-архівованих-чатів", "Are you sure you want to delete this channel?": "Ви впевнені, що хочете видалити цей канал?", "Are you sure you want to delete this message?": "Ви впевнені, що хочете видалити це повідомлення?", "Are you sure you want to unarchive all archived chats?": "Ви впевнені, що хочете розархівувати всі архівовані чати?", "Are you sure?": "Ви впевнені?", "Arena Models": "Моделі Arena", "Artifacts": "Артефакти", "Ask a question": "Задати питання", "Assistant": "Асис<PERSON><PERSON><PERSON>т", "Attach file": "Прикріпити файл", "Attribute for Username": "Атрибут для імені користувача", "Audio": "Ау<PERSON><PERSON><PERSON>", "August": "Серпень", "Authenticate": "Автентифікувати", "Auto-Copy Response to Clipboard": "Автокопіювання відповіді в буфер обміну", "Auto-playback response": "Автоматичне відтворення відповіді", "Autocomplete Generation": "Генерація автозаповнення", "Autocomplete Generation Input Max Length": "Максимальна довжина введення для генерації автозаповнення", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 Рядок авторизації API", "AUTOMATIC1111 Base URL": "URL-адреса AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "Необхідна URL-адреса AUTOMATIC1111.", "Available list": "Список доступності", "available!": "доступно!", "Azure AI Speech": "Мовлення Azure AI", "Azure Region": "Регіон Azure", "Back": "Назад", "Bad": "", "Bad Response": "Неправи<PERSON>ьна відповідь", "Banners": "Прапори", "Base Model (From)": "Базова модель (від)", "Batch Size (num_batch)": "Розмір партії (num_batch)", "before": "до того, як", "Beta": "Beta", "Bing Search V7 Endpoint": "Точка доступу Bing Search V7", "Bing Search V7 Subscription Key": "Ключ підписки Bing Search V7", "Brave Search API Key": "Ключ API пошуку Brave", "By {{name}}": "Від {{name}}", "Bypass SSL verification for Websites": "Обхід SSL-перевірки для веб-сайтів", "Call": "Виклик", "Call feature is not supported when using Web STT engine": "Функція виклику не підтримується при використанні Web STT (розпізнавання мовлення) рушія", "Camera": "Камера", "Cancel": "Скасувати", "Capabilities": "Можливості", "Capture": "Захоплення", "Certificate Path": "Шлях до сертифіката", "Change Password": "Змінити пароль", "Channel Name": "Назва каналу", "Channels": "Канали", "Character": "Перс<PERSON><PERSON><PERSON>", "Character limit for autocomplete generation input": "Ліміт символів для введення при генерації автозаповнення", "Chart new frontiers": "Відкривати нові горизонти", "chat": "", "Chat": "Чат", "Chat Background Image": "Фонове зображення чату", "Chat Bubble UI": "Чат у вигляді бульбашок", "Chat Controls": "Керування чатом", "Chat direction": "Напрям чату", "Chat Overview": "Огляд чату", "Chat Permissions": "Дозволи чату", "Chat Tags Auto-Generation": "Автоматична генерація тегів чату", "Chats": "Чати", "Check Again": "Перевірити ще раз", "Check for updates": "Перевірити оновлення", "Checking for updates...": "Перевірка оновлень...", "Choose a model before saving...": "Оберіть модель перед збереженням...", "Chunk Overlap": "Перекриття фрагментів", "Chunk Params": "Параметри фрагментів", "Chunk Size": "Розмір фрагменту", "Ciphers": "<PERSON>и<PERSON><PERSON>и", "Citation": "Цитування", "Clear memory": "Очистити пам'ять", "click here": "натисніть тут", "Click here for filter guides.": "Натисніть тут для інструкцій із фільтрації", "Click here for help.": "Натисніть тут, щоб отримати допомогу.", "Click here to": "Натисніть тут, щоб", "Click here to download user import template file.": "Натисніть тут, щоб завантажити файл шаблону імпорту користувача.", "Click here to learn more about faster-whisper and see the available models.": "Натисніть тут, щоб дізнатися більше про faster-whisper та переглянути доступні моделі.", "Click here to select": "Натисніть тут, щоб обрати", "Click here to select a csv file.": "Натисніть тут, щоб обрати csv-файл.", "Click here to select a py file.": "Натисніть тут, щоб обрати py-файл.", "Click here to upload a workflow.json file.": "Натисніть тут, щоб завантажити файл workflow.json.", "click here.": "натисніть тут.", "Click on the user role button to change a user's role.": "Натисніть кнопку ролі користувача, щоб змінити роль користувача.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Відмовлено в дозволі на запис до буфера обміну. Будь ласка, перевірте налаштування вашого браузера, щоб надати необхідний доступ.", "Clone": "Клонувати", "Close": "Закрити", "Code execution": "Виконання коду", "Code formatted successfully": "Код успішно відформатовано", "Collection": "Колекція", "Color": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ComfyUI": "ComfyUI", "ComfyUI API Key": "ComfyUI API ключ", "ComfyUI Base URL": "URL-адреса ComfyUI", "ComfyUI Base URL is required.": "Необхідно вказати URL-адресу ComfyUI.", "ComfyUI Workflow": "ComfyUI Workflow", "ComfyUI Workflow Nodes": "Вузли Workflow в ComfyUI", "Command": "Команда", "Completions": "Завершення", "Concurrent Requests": "Одночасні запити", "Configure": "Налаштувати", "Configure Models": "Налаштувати моделі", "Confirm": "Підтвердити", "Confirm Password": "Підтвердіть пароль", "Confirm your action": "Підтвердіть свою дію", "Confirm your new password": "Підтвердіть свій новий пароль", "Connections": "З'єднання", "console": "", "Contact Admin for WebUI Access": "Зверніться до адміна для отримання доступу до WebUI", "Content": "Зміст", "Content Extraction": "Вилучення вмісту", "Context Length": "<PERSON><PERSON><PERSON><PERSON><PERSON> контексту", "Continue Response": "Продовжити відповідь", "Continue with {{provider}}": "Продовжити з {{provider}}", "Continue with Email": "Продовжити з електронною поштою", "Continue with LDAP": "Продовжити з LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Керування розбиттям тексту повідомлення для TTS-запитів. 'Punctuation' розбиває на речення, 'paragraphs' розбиває на абзаци, а 'none' залишає повідомлення як один рядок.", "Controls": "Керування", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "Контролює баланс між зв'язністю та різноманітністю виходу. Нижче значення призведе до більш зосередженого та зв'язного тексту. (За замовчуванням: 5.0)", "Copied": "Скопійовано", "Copied shared chat URL to clipboard!": "Скопійовано URL-адресу спільного чату в буфер обміну!", "Copied to clipboard": "Скопійовано в буфер обміну", "Copy": "Копіювати", "Copy last code block": "Копіювати останній блок коду", "Copy last response": "Копіювати останню відповідь", "Copy Link": "Копіювати посилання", "Copy to clipboard": "Копіювати в буфер обміну", "Copying to clipboard was successful!": "Копіювання в буфер обміну виконано успішно!", "Create": "Створити", "Create a knowledge base": "Створити базу знань", "Create a model": "Створити модель", "Create Account": "Створити обліковий запис", "Create Admin Account": "Створити обліковий запис адміністратора", "Create Channel": "Створити канал", "Create Group": "Створити групу", "Create Knowledge": "Створити знання", "Create new key": "Створити новий ключ", "Create new secret key": "Створити новий секретний ключ", "Created at": "Створено у", "Created At": "Створено у", "Created by": "Створено", "CSV Import": "Імпорт CSV", "Current Model": "Поточна модель", "Current Password": "Поточний пароль", "Custom": "Налаштувати", "Dark": "Темна", "Database": "База даних", "December": "Гру<PERSON><PERSON><PERSON>ь", "Default": "За замовчуванням", "Default (Open AI)": "За замовчуванням (Open AI)", "Default (SentenceTransformers)": "За замовчуванням (SentenceTransformers)", "Default Model": "Модель за замовчуванням", "Default model updated": "Модель за замовчуванням оновлено", "Default Models": "Моделі за замовчуванням", "Default permissions": "Дозволи за замовчуванням", "Default permissions updated successfully": "Дозволи за замовчуванням успішно оновлено", "Default Prompt Suggestions": "Пропозиції промтів замовчуванням", "Default to 389 or 636 if TLS is enabled": "За замовчуванням використовується 389 або 636, якщо TLS увімкнено.", "Default to ALL": "За замовчуванням — ВСІ.", "Default User Role": "Роль користувача за замовчуванням", "Delete": "Видалити", "Delete a model": "Видалити модель", "Delete All Chats": "Видалити усі чати", "Delete All Models": "Видалити всі моделі", "Delete chat": "Видалити чат", "Delete Chat": "Видалити чат", "Delete chat?": "Видалити чат?", "Delete folder?": "Видалити папку?", "Delete function?": "Видалити функцію?", "Delete Message": "Видалити повідомлення", "Delete prompt?": "Видалити промт?", "delete this link": "видалити це посилання", "Delete tool?": "Видалити інструмент?", "Delete User": "Видалити користувача", "Deleted {{deleteModelTag}}": "Видалено {{deleteModelTag}}", "Deleted {{name}}": "Видалено {{name}}", "Deleted User": "Видалений користувач", "Describe your knowledge base and objectives": "Опишіть вашу базу знань та цілі", "Description": "<PERSON><PERSON><PERSON><PERSON>", "Disabled": "Вимкнено", "discover": "", "Discover a function": "Знайдіть функцію", "Discover a model": "Знайдіть модель", "Discover a prompt": "Знайдіть промт", "Discover a tool": "Знайдіть інструмент", "Discover wonders": "Відкривайте чудеса", "Discover, download, and explore custom functions": "Знайдіть, завантажте та досліджуйте налаштовані функції", "Discover, download, and explore custom prompts": "Знайдіть, завантажте та досліджуйте налаштовані промти", "Discover, download, and explore custom tools": "Знайдіть, завантажте та досліджуйте налаштовані інструменти", "Discover, download, and explore model presets": "Знайдіть, завантажте та досліджуйте налаштування моделей", "Dismissible": "Неприйнятно", "Display": "Відображення", "Display Emoji in Call": "Відображати емодзі у викликах", "Display the username instead of You in the Chat": "Показувати ім'я користувача замість 'Ви' в чаті", "Displays citations in the response": "Показує посилання у відповіді", "Dive into knowledge": "Зануртесь у знання", "Do not install functions from sources you do not fully trust.": "Не встановлюйте функції з джерел, яким ви не повністю довіряєте.", "Do not install tools from sources you do not fully trust.": "Не встановлюйте інструменти з джерел, яким ви не повністю довіряєте.", "Document": "Документ", "Documentation": "Документація", "Documents": "Документи", "does not make any external connections, and your data stays securely on your locally hosted server.": "не встановлює жодних зовнішніх з'єднань, і ваші дані залишаються в безпеці на вашому локальному сервері.", "Don't have an account?": "Немає облікового запису?", "don't install random functions from sources you don't trust.": "не встановлюйте випадкові функції з джерел, яким ви не довіряєте.", "don't install random tools from sources you don't trust.": "не встановлюйте випадкові інструменти з джерел, яким ви не довіряєте.", "Done": "Готово", "Download": "Завант<PERSON><PERSON><PERSON>ти", "Download canceled": "Завантаження скасовано", "Download Database": "Завантажити базу даних", "Drag and drop a file to upload or select a file to view": "Перетягніть файл для завантаження або виберіть файл для перегляду", "Draw": "Малювати", "Drop any files here to add to the conversation": "Перетягніть сюди файли, щоб додати до розмови", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "напр., '30s','10m'. Дійсні одиниці часу: 'с', 'хв', 'г'.", "e.g. A filter to remove profanity from text": "напр., фільтр для видалення нецензурної лексики з тексту", "e.g. My Filter": "напр., <PERSON><PERSON><PERSON> фільтр", "e.g. My Tools": "напр., Мої інструменти", "e.g. my_filter": "напр., my_filter", "e.g. my_tools": "напр., my_tools", "e.g. Tools for performing various operations": "напр., Інструменти для виконання різних операцій", "Edit": "Редагувати", "Edit Arena Model": "Редагувати модель Arena", "Edit Channel": "Редагувати канал", "Edit Connection": "Редагувати з'єднання", "Edit Default Permissions": "Редагувати дозволи за замовчуванням", "Edit Memory": "Редагувати пам'ять", "Edit User": "Редагувати користувача", "Edit User Group": "Редагувати групу користувачів", "ElevenLabs": "ElevenLabs", "Email": "Ел. пошта", "Embark on adventures": "Вирушайте в пригоди", "Embedding Batch Size": "Розмір пакету під час вбудовування", "Embedding Model": "Модель вбудовування", "Embedding Model Engine": "Ру<PERSON><PERSON>й моделі вбудовування ", "Embedding model set to \"{{embedding_model}}\"": "Встановлена модель вбудовування \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "Увімкнути генерацію автозаповнення для повідомлень чату", "Enable Community Sharing": "Увімкнути спільний доступ", "Enable Google Drive": "Увімкнути Google Drive", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Увімкнути блокування пам'яті (mlock), щоб запобігти виведенню даних моделі з оперативної пам'яті. Цей параметр блокує робочий набір сторінок моделі в оперативній пам'яті, гарантуючи, що вони не будуть виведені на диск. Це може допомогти підтримувати продуктивність, уникати помилок сторінок та забезпечувати швидкий доступ до даних.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Увімкнути відображення пам'яті (mmap) для завантаження даних моделі. Цей параметр дозволяє системі використовувати дискове сховище як розширення оперативної пам'яті, трактуючи файли на диску, як ніби вони знаходяться в RAM. Це може покращити продуктивність моделі, дозволяючи швидший доступ до даних. Однак, він може не працювати коректно на всіх системах і може споживати значну кількість дискового простору.", "Enable Message Rating": "Увімкнути оцінку повідомлень", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "Увімкнути вибірку Mirostat для контролю над непередбачуваністю. (За замовчуванням: 0, 0 = Вимкнено, 1 = Mirostat, 2 = Mirostat 2.0)", "Enable New Sign Ups": "Дозволити нові реєстрації", "Enable Web Search": "Увімкнути веб-пошук", "Enabled": "Увімкнено", "Engine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Переконайтеся, що ваш CSV-файл містить 4 колонки в такому порядку: Ім'я, <PERSON>ail, Пароль, Роль.", "Enter {{role}} message here": "Введіть повідомлення {{role}} тут", "Enter a detail about yourself for your LLMs to recall": "Введіть відомості про себе для запам'ятовування вашими LLM.", "Enter api auth string (e.g. username:password)": "Введіть рядок авторизації api (напр, ім'я користувача:пароль)", "Enter Application DN": "Введіть DN застосунку", "Enter Application DN Password": "Введіть пароль DN застосунку", "Enter Bing Search V7 Endpoint": "Введіть точку доступу Bing Search V7", "Enter Bing Search V7 Subscription Key": "Введіть ключ підписки Bing Search V7", "Enter Brave Search API Key": "Введіть ключ API для пошуку Brave", "Enter certificate path": "Введіть шлях до сертифіката", "Enter CFG Scale (e.g. 7.0)": "Введіть масштаб CFG (напр., 7.0)", "Enter Chunk Overlap": "Введіть перекриття фрагменту", "Enter Chunk Size": "Введіть розмір фрагменту", "Enter description": "Введіть опис", "Enter Github Raw URL": "Введіть Raw URL-адре<PERSON><PERSON> Github", "Enter Google PSE API Key": "Введіть ключ API Google PSE", "Enter Google PSE Engine Id": "Введіть Google PSE Engine Id", "Enter Image Size (e.g. 512x512)": "Введіть розмір зображення (напр., 512x512)", "Enter Jina API Key": "Введіть ключ API для Jina", "Enter Kagi Search API Key": "Введіть ключ API Kagi Search", "Enter language codes": "Введіть мовні коди", "Enter Model ID": "Введіть ID моделі", "Enter model tag (e.g. {{modelTag}})": "Введіть тег моделі (напр., {{modelTag}})", "Enter Mojeek Search API Key": "Введіть API ключ для пошуку Mojeek", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "Введіть кількість кроків (напр., 50)", "Enter proxy URL (e.g. **************************:port)": "Введіть URL проксі (напр., **************************:port)", "Enter Sampler (e.g. Euler a)": "Введіть семплер (напр., Euler a)", "Enter Scheduler (e.g. Karras)": "Введіть планувальник (напр., Karras)", "Enter Score": "Введіть бал", "Enter SearchApi API Key": "Введіть ключ API для SearchApi", "Enter SearchApi Engine": "Введіть SearchApi рушія", "Enter Searxng Query URL": "Введіть URL-адресу запиту Searxng", "Enter Seed": "Введіть насіння", "Enter Serper API Key": "Введіть ключ API Serper", "Enter Serply API Key": "Введіть ключ API Serply", "Enter Serpstack API Key": "Введіть ключ API Serpstack", "Enter server host": "Введіть хост сервера", "Enter server label": "Введіть мітку сервера", "Enter server port": "Введіть порт сервера", "Enter stop sequence": "Введіть символ зупинки", "Enter system prompt": "Введіть системний промт", "Enter Tavily API Key": "Введіть ключ <PERSON>ly", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "Введіть публічний URL вашого WebUI. Цей URL буде використовуватися для генерування посилань у сповіщеннях.", "Enter Tika Server URL": "Введіть URL-адресу сервера Tika ", "Enter Top K": "Введіть Top K", "Enter URL (e.g. http://127.0.0.1:7860/)": "Введіть URL-адресу (напр., http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Введіть URL-адресу (напр., http://localhost:11434)", "Enter your current password": "Введіть ваш поточний пароль", "Enter Your Email": "Введіть вашу ел. пошту", "Enter Your Full Name": "Введіть ваше ім'я", "Enter your message": "Введіть повідомлення ", "Enter your new password": "Введіть ваш новий пароль", "Enter Your Password": "Введіть ваш пароль", "Enter your prompt": "", "Enter Your Role": "Введіть вашу роль", "Enter Your Username": "Введіть своє ім'я користувача", "Enter your webhook URL": "Введіть URL вашого вебхука", "Error": "Помилка", "ERROR": "ПОМИЛКА", "Error accessing Google Drive: {{error}}": "Помилка доступу до Google Drive: {{error}}", "Error uploading file: {{error}}": "Помилка завантаження файлу: {{error}}", "Evaluations": "Оцінювання", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Приклад: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Приклад: В<PERSON>І", "Example: ou=users,dc=foo,dc=example": "Приклад: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Приклад: sAMAccountName або uid або userPrincipalName", "Exclude": "Виключити", "Experimental": "Експериментальне", "Explore the cosmos": "Досліджуйте космос", "Export": "Експорт", "Export All Archived Chats": "Експорт всіх архівованих чатів", "Export All Chats (All Users)": "Експорт всіх чатів (всіх користувачів)", "Export chat (.json)": "Експорт чату (.json)", "Export Chats": "Експорт чатів", "Export Config to JSON File": "Експорт конфігурації у файл JSON", "Export Functions": "Експорт функцій ", "Export Models": "Експорт моделей", "Export Presets": "Експорт пресетів", "Export Prompts": "Експорт промтів", "Export to CSV": "Експорт в CSV", "Export Tools": "Експорт інструментів", "External Models": "Зовнішні моделі", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "Не вдалося додати файл.", "Failed to create API Key.": "Не вдалося створити API ключ.", "Failed to read clipboard contents": "Не вдалося прочитати вміст буфера обміну", "Failed to save models configuration": "Не вдалося зберегти конфігурацію моделей", "Failed to update settings": "Не вдалося оновити налаштування", "February": "Лю<PERSON><PERSON>", "Feedback History": "Історія відгуків", "Feedbacks": "Відгуки", "File": "<PERSON>а<PERSON><PERSON>", "File added successfully.": "Файл успішно додано.", "File content updated successfully.": "Вміст файлу успішно оновлено.", "File Mode": "Файловий режим", "File not found.": "Файл не знайдено.", "File removed successfully.": "Файл успішно видалено.", "File size should not exceed {{maxSize}} MB.": "Розмір файлу не повинен перевищувати {{maxSize}} МБ.", "File uploaded successfully": "Файл успішно завантажено", "Files": "Файли", "Filter is now globally disabled": "Фільтр глобально вимкнено", "Filter is now globally enabled": "Фільтр увімкнено глобально", "Filters": "Фільтри", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Виявлено підробку відбитків: Неможливо використовувати ініціали як аватар. Повернення до зображення профілю за замовчуванням.", "Fluidly stream large external response chunks": "Плавно передавати великі фрагменти зовнішніх відповідей", "Focus chat input": "Фокус вводу чату", "Folder deleted successfully": "Папку успішно видалено", "Folder name cannot be empty": "Назва папки не може бути порожньою", "Folder name cannot be empty.": "Назва папки не може бути порожньою.", "Folder name updated successfully": "Назву папки успішно оновлено", "Forge new paths": "Прокладайте нові шляхи", "Form": "Форма", "Format your variables using brackets like this:": "Форматуйте свої змінні, використовуючи фігурні дужки таким чином:", "Frequency Penalty": "Штраф за частоту", "Function": "Функція", "Function created successfully": "Функцію успішно створено", "Function deleted successfully": "Функцію успішно видалено", "Function Description": "<PERSON><PERSON><PERSON><PERSON> функції", "Function ID": "ID функції", "Function is now globally disabled": "Функція зараз глобально вимкнена", "Function is now globally enabled": "Функція зараз глобально увімкнена ", "Function Name": "Назва функції", "Function updated successfully": "Функцію успішно оновлено", "Functions": "Функції", "Functions allow arbitrary code execution": "Функції дозволяють виконання довільного коду", "Functions allow arbitrary code execution.": "Функції дозволяють виконання довільного коду.", "Functions imported successfully": "Функції успішно імпортовано", "General": "Загальні", "General Settings": "Загальні налаштування", "Generate Image": "Створити зображення", "Generating search query": "Сформувати пошуковий запит", "Get started": "Почати", "Get started with {{WEBUI_NAME}}": "Почати з {{WEBUI_NAME}}", "Global": "Глоб.", "Good Response": "<PERSON>а<PERSON><PERSON> відповідь", "Google Drive": "Google Drive", "Google PSE API Key": "Ключ API Google PSE", "Google PSE Engine Id": "Id рушія Google PSE", "Group created successfully": "Групу успішно створено", "Group deleted successfully": "Групу успішно видалено", "Group Description": "О<PERSON><PERSON>с групи", "Group Name": "Назва групи", "Group updated successfully": "Групу успішно оновлено", "Groups": "Гру<PERSON>и", "h:mm a": "h:mm a", "Haptic Feedback": "Тактильний зворотній зв'язок", "Harmful or offensive": "", "has no conversations.": "не має розмов.", "Hello, {{name}}": "Привіт, {{name}}", "Help": "Допоможіть", "Help us create the best community leaderboard by sharing your feedback history!": "Допоможіть нам створити найкращу таблицю лідерів спільноти, поділившись історією своїх відгуків!", "Hex Color": "Шістнадцятковий колір", "Hex Color - Leave empty for default color": "Шістнадцятковий колір — залиште порожнім для кольору за замовчуванням", "Hide": "Приховати", "Host": "Хо<PERSON>т", "How can I help you today?": "Чим я можу допомогти вам сьогодні?", "How would you rate this response?": "Як би ви оцінили цю відповідь?", "Hybrid Search": "Гібридний пошук", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Я підтверджую, що прочитав і розумію наслідки своїх дій. Я усвідомлюю ризики, пов'язані з виконанням довільного коду, і перевірив надійність джерела.", "ID": "ID", "Ignite curiosity": "Запаліть цікавість", "Image Compression": "Стиснення зображень", "Image Generation (Experimental)": "Генерування зображень (експериментально)", "Image Generation Engine": "Механізм генерації зображень", "Image Max Compression Size": "Максимальний розмір стиснення зображення", "Image Settings": "Налаштування зображення", "Images": "Зображення", "Import Chats": "Імпорт чатів", "Import Config from JSON File": "Імпорт конфігурації з файлу JSON", "Import Functions": "Імпорт функцій ", "Import Models": "Імпорт моделей", "Import Presets": "Імпорт пресетів", "Import Prompts": "Імпорт промтів", "Import Tools": "Імпорт інструментів", "Include": "Включити", "Include `--api-auth` flag when running stable-diffusion-webui": "Включіть прапорець `--api-auth` під час запуску stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "Включіть прапор `--api` при запуску stable-diffusion-webui", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "Впливає на швидкість, з якою алгоритм реагує на зворотній зв'язок від згенерованого тексту. Нижча швидкість навчання призведе до повільнішої корекції, тоді як вища швидкість навчання зробить алгоритм більш реакційним. (За замовчуванням: 0.1)", "Info": "Інфо", "Input commands": "Команди вводу", "Install from Github URL": "Встановіть з URL-адре<PERSON><PERSON>ub", "Instant Auto-Send After Voice Transcription": "Миттєва автома<PERSON>ична відправка після транскрипції голосу", "Interface": "Інтерфейс", "Invalid file format.": "Неправильний формат файлу.", "Invalid Tag": "Недійсний тег", "is typing...": "", "January": "Січень", "Jina API Key": "Ключ API для Jina", "join our Discord for help.": "приєднуйтеся до нашого Discord для допомоги.", "JSON": "JSON", "JSON Preview": "Перегляд JSON", "July": "Липень", "June": "Червень", "JWT Expiration": "Термін дії JWT", "JWT Token": "Токен JWT", "Kagi Search API Key": "Kagi Search API ключ", "Keep Alive": "Зберегти активність", "Key": "<PERSON><PERSON><PERSON><PERSON>", "Keyboard shortcuts": "Клавіатурні скорочення", "Knowledge": "Знання", "Knowledge Access": "Доступ до знань", "Knowledge created successfully.": "Знання успішно створено.", "Knowledge deleted successfully.": "Знання успішно видалено.", "Knowledge reset successfully.": "Знання успішно скинуто.", "Knowledge updated successfully": "Знання успішно оновлено", "Label": "Мітка", "Landing Page Mode": "Режим головної сторінки", "Language": "Мова", "Last Active": "Остання активність", "Last Modified": "Востаннє змінено", "Last reply": "", "Latest users": "", "LDAP": "LDAP", "LDAP server updated": "Сервер LDAP оновлено", "Leaderboard": "Таблиця лідерів", "Leave empty for unlimited": "Залиште порожнім для необмеженого розміру", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "Залиште порожнім, щоб включити всі моделі з кінцевої точки \"{{URL}}/api/tags\"", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "Залиште порожнім, щоб включити всі моделі з кінцевої точки \"{{URL}}/models\"", "Leave empty to include all models or select specific models": "Залиште порожнім, щоб включити всі моделі, або виберіть конкретні моделі.", "Leave empty to use the default prompt, or enter a custom prompt": "Залиште порожнім для використання стандартного запиту, або введіть власний запит", "Light": "Світла", "Listening...": "Слухаю...", "Local": "Локальний", "Local Models": "Локальні моделі", "Lost": "Втрачене", "LTR": "LTR", "Made by OpenWebUI Community": "Зроблено спільнотою OpenWebUI", "Make sure to enclose them with": "Переконайтеся, що вони закриті", "Make sure to export a workflow.json file as API format from ComfyUI.": "Обов'язково експортуйте файл workflow.json у форматі API з ComfyUI.", "Manage": "Керувати", "Manage Arena Models": "Керувати моделями Arena", "Manage Ollama": "Керува<PERSON><PERSON>", "Manage Ollama API Connections": "Керувати з'єднаннями Ollama API", "Manage OpenAI API Connections": "Керувати з'єднаннями OpenAI API", "Manage Pipelines": "Керування конвеєрами", "March": "Березень", "Max Tokens (num_predict)": "Макс токенів (num_predict)", "Max Upload Count": "Макс. кількість завантажень", "Max Upload Size": "Макс. розмір завантаження", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Максимум 3 моделі можна завантажити одночасно. Будь ласка, спробуйте пізніше.", "May": "Травень", "Memories accessible by LLMs will be shown here.": "Пам'ять, яка доступна LLM, буде показана тут.", "Memory": "Пам'ять", "Memory added successfully": "Пам'ять додано успішно", "Memory cleared successfully": "Пам'ять успішно очищено", "Memory deleted successfully": "Пам'ять успішно видалено", "Memory updated successfully": "Пам'ять успішно оновлено", "Merge Responses": "Об'єднати відповіді", "Message rating should be enabled to use this feature": "Оцінювання повідомлень має бути увімкнено для використання цієї функції.", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Повідомлення, які ви надішлете після створення посилання, не будуть доступні для інших. Користувачі, які мають URL, зможуть переглядати спільний чат.", "Min P": "<PERSON>", "Minimum Score": "Міні<PERSON>альний бал", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "MMMM DD, YYYY", "MMMM DD, YYYY HH:mm": "MMMM DD, YYYY HH:mm", "MMMM DD, YYYY hh:mm:ss A": "MMMM DD, YYYY hh:mm:ss A", "Model": "Модель", "Model '{{modelName}}' has been successfully downloaded.": "Модель '{{modelName}}' успішно завантажено.", "Model '{{modelTag}}' is already in queue for downloading.": "Модель '{{modelTag}}' вже знаходиться в черзі на завантаження.", "Model {{modelId}} not found": "Модель {{modelId}} не знайдено", "Model {{modelName}} is not vision capable": "Модель {{modelName}} не здатна бачити", "Model {{name}} is now {{status}}": "Модель {{name}} тепер має {{status}}", "Model accepts image inputs": "Модель приймає зображеня", "Model created successfully!": "Модель створено успішно!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Виявлено шлях до файлової системи моделі. Для оновлення потрібно вказати коротке ім'я моделі, не вдасться продовжити.", "Model Filtering": "Фільтрація моделей", "Model ID": "ID моделі", "Model IDs": "ID моделей", "Model Name": "Назва моделі", "Model not selected": "Модель не вибрана", "Model Params": "Параметри моделі", "Model Permissions": "Дозволи моделей", "Model updated successfully": "Модель успішно оновлено", "Modelfile Content": "Вміст файлу моделі", "Models": "Моделі", "Models Access": "Доступ до моделей", "Models configuration saved successfully": "Конфігурацію моделей успішно збережено", "Mojeek Search API Key": "API ключ для пошуку Mojeek", "more": "більше", "More": "Більше", "Name": "Ім'я", "Name your knowledge base": "Назвіть вашу базу знань", "New Chat": "<PERSON>овий чат", "New folder": "Нова папка", "New Password": "Новий пароль", "new-channel": "новий-канал", "No content found": "Контент не знайдено.", "No content to speak": "Нема чого говорити", "No distance available": "Відстань недоступна", "No feedbacks found": "Відгуків не знайдено", "No file selected": "Файл не обрано", "No files found.": "Файли не знайдено.", "No groups with access, add a group to grant access": "Немає груп з доступом, додайте групу для надання доступу", "No HTML, CSS, or JavaScript content found.": "HTML, CSS або JavaScript контент не знайдено.", "No knowledge found": "Знання не знайдено.", "No model IDs": "Немає ID моделей", "No models found": "Моделей не знайдено", "No models selected": "Моделі не вибрано", "No results found": "Не знайдено жодного результату", "No search query generated": "Пошуковий запит не сформовано", "No source available": "Джерело не доступне", "No users were found.": "Користувачів не знайдено.", "No valves to update": "Немає клапанів для оновлення", "None": "Нема", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Примітка: Якщо ви встановите мінімальну кількість балів, пошук поверне лише документи з кількістю балів, більшою або рівною мінімальній кількості балів.", "Notes": "Примітки", "Notification Sound": "", "Notification Webhook": "Вебхук для сповіщень", "Notifications": "Сповіщення", "November": "Листопад", "num_gpu (Ollama)": "num_gpu (Ollama)", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "OAuth ID", "October": "Жовтень", "Off": "Вимк", "Okay, Let's Go!": "Гаразд, давайте почнемо!", "OLED Dark": "Темний OLED", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API disabled": "Ollama API вимкнено", "Ollama API settings updated": "Налаштування Ollama API оновлено", "Ollama Version": "Версія Ollama", "On": "Увімк", "Only alphanumeric characters and hyphens are allowed": "Дозволені тільки алфавітно-цифрові символи та дефіси", "Only alphanumeric characters and hyphens are allowed in the command string.": "У рядку команди дозволено використовувати лише алфавітно-цифрові символи та дефіси.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Редагувати можна лише колекції, створіть нову базу знань, щоб редагувати або додавати документи.", "Only select users and groups with permission can access": "Тільки вибрані користувачі та групи з дозволом можуть отримати доступ", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Упс! Схоже, що URL-адреса невірна. Будь ласка, перевірте ще раз та спробуйте ще раз.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Упс! Деякі файли все ще завантажуються. Будь ласка, зачекайте, поки завантаження завершиться.", "Oops! There was an error in the previous response.": "Упс! Сталася помилка в попередній відповіді.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Упс! Ви використовуєте непідтримуваний метод (тільки для фронтенду). Будь ласка, обслуговуйте WebUI з бекенду.", "Open in full screen": "Відкрити на весь екран", "Open new chat": "Відкрити новий чат", "Open WebUI uses faster-whisper internally.": "Open WebUI використовує faster-whisper внутрішньо.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI використовує вбудовування голосів SpeechT5 та CMU Arctic.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Open WebUI версія (v{{OPEN_WEBUI_VERSION}}) нижча за необхідну версію (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "Конфігурація OpenAI API", "OpenAI API Key is required.": "Потрібен ключ OpenAI API.", "OpenAI API settings updated": "Налаштування OpenAI API оновлено", "OpenAI URL/Key required.": "Потрібен OpenAI URL/ключ.", "or": "або", "Organize your users": "Організуйте своїх користувачів", "OUTPUT": "ВИХІД", "Output format": "Формат відповіді", "Overview": "Огляд", "page": "сторінка", "Password": "Пароль", "Paste Large Text as File": "Вставити великий текст як файл", "PDF document (.pdf)": "PDF документ (.pdf)", "PDF Extract Images (OCR)": "Розпізнавання зображень з PDF (OCR)", "pending": "на розгляді", "Permission denied when accessing media devices": "Відмовлено в доступі до медіапристроїв", "Permission denied when accessing microphone": "Відмовлено у доступі до мікрофона", "Permission denied when accessing microphone: {{error}}": "Доступ до мікрофона заборонено: {{error}}", "Permissions": "Дозволи", "Personalization": "Персоналізація", "Pin": "Зачепити", "Pinned": "Зачеплено", "Pioneer insights": "Прокладайте нові шляхи до знань", "Pipeline deleted successfully": "Конвеєр успішно видалено", "Pipeline downloaded successfully": "Конвеєр успішно завантажено", "Pipelines": "Конвеєри", "Pipelines Not Detected": "Конвеєрів не знайдено", "Pipelines Valves": "Клапани конвеєрів", "Plain text (.txt)": "Простий текст (.txt)", "Playground": "Май<PERSON><PERSON><PERSON><PERSON>ик", "Please carefully review the following warnings:": "Будь ласка, уважно ознайомтеся з наступними попередженнями:", "Please enter a prompt": "Будь ласка, введіть підказку", "Please fill in all fields.": "Будь ласка, заповніть всі поля.", "Please select a model first.": "Будь ласка, спочатку виберіть модель.", "Port": "Порт", "Prefix ID": "ID префікса", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "ID префікса використовується для уникнення конфліктів з іншими підключеннями шляхом додавання префікса до ID моделей — залиште порожнім, щоб вимкнути", "Previous 30 days": "Попередні 30 днів", "Previous 7 days": "Попередні 7 днів", "Profile Image": "Зображення профілю", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Підказка (напр., розкажіть мені цікавий факт про Римську імперію)", "Prompt Content": "Зміст промту", "Prompt created successfully": "Підказку успішно створено", "Prompt suggestions": "Швидкі промти", "Prompt updated successfully": "Підказку успішно оновлено", "Prompts": "Промти", "Prompts Access": "Доступ до підказок", "Provide any specific details": "", "Proxy URL": "URL проксі", "Pull \"{{searchValue}}\" from Ollama.com": "Завантажити \"{{searchValue}}\" з Ollama.com", "Pull a model from Ollama.com": "Завантажити модель з Ollama.com", "Query Generation Prompt": "Підказка для генерації запиту", "Query Params": "Параметри запиту", "RAG Template": "Шаблон RAG", "Rating": "Оцінка", "Re-rank models by topic similarity": "Перестановка моделей за схожістю тем", "Read Aloud": "Читати вголос", "Record voice": "Записати голос", "Redirecting you to OpenWebUI Community": "Перенаправляємо вас до спільноти OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "Знижує ймовірність генерації безглуздих відповідей. Вищі значення (напр., 100) призведуть до більш різноманітних відповідей, тоді як нижчі значення (напр., 10) будуть більш обережними. (За замовчуванням: 40)", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Називайте себе \"Користувач\" (напр., \"Користувач вивчає іспанську мову\")", "References from": "Посилання з", "Refresh Token Expiration": "", "Regenerate": "Регенерувати", "Release Notes": "Нотатки до випуску", "Relevance": "Актуальність", "Remove": "Видалити", "Remove Model": "Видалити модель", "Rename": "Перейменувати", "Reorder Models": "Переставити моделі", "Repeat Last N": "Повторити останні N", "Reply in Thread": "", "Request Mode": "Режим запиту", "Reranking Model": "Модель переранжування", "Reranking model disabled": "Модель переранжування вимкнена", "Reranking model set to \"{{reranking_model}}\"": "Модель переранжування встановлено на \"{{reranking_model}}\"", "Reset": "Скидання", "Reset All Models": "Скинути всі моделі", "Reset Upload Directory": "Скинути каталог завантажень", "Reset Vector Storage/Knowledge": "Скинути векторне сховище/Знання", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Сповіщення про відповіді не можуть бути активовані, оскільки вам було відмовлено в доступі до веб-сайту. Будь ласка, відвідайте налаштування вашого браузера, щоб надати необхідний доступ.", "Response splitting": "Розбиття відповіді", "Result": "Результат", "Retrieval Query Generation": "Генерація запиту для отримання даних", "Rich Text Input for Chat": "Ввід тексту з форматуванням для чату", "RK": "RK", "Role": "Роль", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "Запустити", "Running": "Виконується", "Save": "Зберегти", "Save & Create": "Зберегти та створити", "Save & Update": "Зберегти та оновити", "Save As Copy": "Зберегти як копію", "Save Tag": "Зберегти тег", "Saved": "Збережено", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Збереження журналів чату безпосередньо в сховище вашого браузера більше не підтримується. Будь ласка, завантажте та видаліть журнали чату, натиснувши кнопку нижче. Не хвилюйтеся, ви можете легко повторно імпортувати журнали чату до бекенду через", "Scroll to bottom when switching between branches": "Перемотувати до кінця при перемиканні між гілками", "Search": "По<PERSON><PERSON>к", "Search a model": "Шукати модель", "Search Base": "База пошуку", "Search Chats": "Пошук в чатах", "Search Collection": "Шукати колекцію", "Search Filters": "Фільтри пошуку", "search for tags": "шукати теги", "Search Functions": "Пошук функцій", "Search Knowledge": "Шукати знання", "Search Models": "Пошук моделей", "Search options": "Опц<PERSON>ї пошуку", "Search Prompts": "Пошук промтів", "Search Result Count": "Кількість результатів пошуку", "Search Tools": "Пошуку інструментів", "Search users": "", "SearchApi API Key": "Ключ API для SearchApi", "SearchApi Engine": "Руш<PERSON>й SearchApi", "Searched {{count}} sites": "Шукалося {{count}} сайтів", "Searching \"{{searchQuery}}\"": "<PERSON>у<PERSON><PERSON><PERSON> \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "Пошу<PERSON> знань для \"{{searchQuery}}\"", "Searxng Query URL": "URL-адреса запиту Searxng", "See readme.md for instructions": "Див. readme.md для інструкцій", "See what's new": "Подивіться, що нового", "Seed": "Сід", "Select a base model": "Обрати базову модель", "Select a engine": "Оберіть рушій", "Select a function": "Оберіть функцію", "Select a group": "Вибрати групу", "Select a model": "Оберіть модель", "Select a pipeline": "Оберіть конвеєр", "Select a pipeline url": "Оберіть адресу конвеєра", "Select a tool": "Оберіть інструмент", "Select Engine": "Виберіть двигун", "Select Knowledge": "Вибрати знання", "Select model": "Обрати модель", "Select only one model to call": "Оберіть лише одну модель для виклику", "Selected model(s) do not support image inputs": "Вибрані модель(і) не підтримують вхідні зображення", "Semantic distance to query": "Семан<PERSON><PERSON><PERSON>на відстань до запиту", "Send": "Надіслати", "Send a message": "", "Send message": "Надіслати повідомлення", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Відправляє `stream_options: { include_usage: true }` у запиті.\nПідтримувані постачальники повернуть інформацію про використання токену у відповіді, якщо вона встановлена.", "September": "Вересень", "Serper API Key": "Ключ API Serper", "Serply API Key": "Ключ API Serply", "Serpstack API Key": "Ключ API Serpstack", "Server connection verified": "З'єднання з сервером підтверджено", "Set as default": "Встановити за замовчуванням", "Set CFG Scale": "Встановити масштаб CFG", "Set Default Model": "Встановити модель за замовчуванням", "Set embedding model": "Встановити модель вбудовування", "Set embedding model (e.g. {{model}})": "Встановити модель вбудовування (напр, {{model}})", "Set Image Size": "Встановити розмір зображення", "Set reranking model (e.g. {{model}})": "Встановити модель переранжування (напр., {{model}})", "Set Sampler": "Встановити семплер", "Set Scheduler": "Встановити планувальник", "Set Steps": "Встановити кроки", "Set Task Model": "Встановити модель задач", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "Встановити кількість пристроїв GPU, що використовується для обробки інформації. Ця опція керує кількістю пристроїв GPU (якщо доступні), які використовуються для обробки надходження запитів. Збільшення цього значення може суттєво підвищити продуктивність моделей, оптимізованих за допомогою апаратного прискорення GPU, але також може споживати більше енергії та ресурсів GPU.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Встановити кількість робочих потоків, що використовуються для обробки інформації. Ця опція керує кількістю потоків, що використовуються для обробки надходження запитів одночасно. Збільшення цього значення може підвищити продуктивність при великій одночасності робіт, але також може споживати більше ресурсів CPU.", "Set Voice": "Встановити голос", "Set whisper model": "Встановити модель whisper", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "Встановлює, на скільки кроків назад модель повинна звертатися, щоб запобігти повторенням. (За замовчуванням: 64, 0 = вимкнено, -1 = num_ctx)", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "Встановлює ступінь покарання за повторення. Чим вище значення (напр., 1.5), тим суворіше буде покарання за повтори; низьке значення (напр., 0.9) передбачає більш пом'якшувальним. (За замовчуванням: 1.1)", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "Встановлює насіння випадкового числа для генерації. Вказавши конкретне число, модель буде генерувати той самий текст для одного й того ж запиту. (За замовчуванням: випадкове)", "Sets the size of the context window used to generate the next token. (Default: 2048)": "Встановлює розмір вікна контексту, яке використовується для генерації наступного токена. (За замовчуванням: 2048)", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Встановлює послідовності зупинки, які будуть використовуватися. Коли зустрічається така послідовність, LLM припиняє генерацію тексту і повертає результат. Можна встановити кілька послідовностей зупинки, вказавши кілька окремих параметрів зупинки у файлі моделі.", "Settings": "Налаштування", "Settings saved successfully!": "Налаштування успішно збережено!", "Share": "Поділити<PERSON>я", "Share Chat": "Поділитися чатом", "Share to OpenWebUI Community": "Поділитися зі спільнотою OpenWebUI", "Show": "Показати", "Show \"What's New\" modal on login": "Показати модальне вікно \"Що нового\" під час входу.", "Show Admin Details in Account Pending Overlay": "Відобразити дані адміна у вікні очікування облікового запису", "Show shortcuts": "Показати клавіатурні скорочення", "Show your support!": "Підтримайте нас!", "Sign in": "Увійти", "Sign in to {{WEBUI_NAME}}": "Увійти в {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "Увійти до {{WEBUI_NAME}} за допомогою LDAP", "Sign Out": "Вийти", "Sign up": "Зареєструватися", "Sign up to {{WEBUI_NAME}}": "Зареєструватися в {{WEBUI_NAME}}", "Signing in to {{WEBUI_NAME}}": "Увійти в {{WEBUI_NAME}}", "sk-1234": "sk-1234", "Source": "Д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Speech Playback Speed": "Швидкість відтворення мовлення", "Speech recognition error: {{error}}": "Помилка розпізнавання мови: {{error}}", "Speech-to-Text Engine": "Система розпізнавання мови", "Stop": "Зупинити", "Stop Sequence": "Символ зупинки", "Stream Chat Response": "Відповідь стрім-чату", "STT Model": "Модель STT ", "STT Settings": "Налаштування STT", "Success": "Успіх", "Successfully updated.": "Успішно оновлено.", "Support": "Підтримати", "Support this plugin:": "Підтримайте цей плагін:", "Sync directory": "Синхронізувати каталог", "System": "Система", "System Instructions": "Системні інструкції", "System Prompt": "Системний промт", "Tags Generation": "Генерація тегів", "Tags Generation Prompt": "Підказка для генерації тегів", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "Вибірка з відрізанням хвоста використовується для зменшення впливу малоймовірних токенів на результат. Вищі значення (напр., 2.0) зменшують цей вплив більше, в той час як значення 1.0 вимикає цю настройку. (За замовчуванням: 1)", "Tap to interrupt": "Нати<PERSON>ніть, щоб перервати", "Tavily API Key": "Ключ <PERSON> Tavily", "Temperature": "Температура", "Template": "Шабл<PERSON>н", "Temporary Chat": "Тимчасовий чат", "Text Splitter": "Роздільник тексту", "Text-to-Speech Engine": "Система синтезу мови", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "Дякуємо за ваш відгук!", "The Application Account DN you bind with for search": "DN облікового запису застосунку, з яким ви здійснюєте прив'язку для пошуку", "The base to search for users": "База для пошуку користувачів", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "Розмір пакету визначає, скільки текстових запитів обробляється одночасно. Більший розмір пакету може підвищити продуктивність та швидкість моделі, але також вимагає більше пам'яті. (За замовчуванням: 512)", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Розробники цього плагіна - пристрасні волонтери зі спільноти. Якщо ви вважаєте цей плагін корисним, будь ласка, зробіть свій внесок у його розвиток.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "Таблиця лідерів оцінки базується на системі рейтингу Ело і оновлюється в реальному часі.", "The LDAP attribute that maps to the username that users use to sign in.": "LDAP-атр<PERSON><PERSON><PERSON><PERSON>, який відповідає за ім'я користувача, яке використовують користувачі для входу.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "Таблиця лідерів наразі в бета-версії, і ми можемо коригувати розрахунки рейтингів у міру вдосконалення алгоритму.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Максимальний розмір файлу в МБ. Якщо розмір файлу перевищує цей ліміт, файл не буде завантажено.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Максима<PERSON>ьна кількість файлів, які можна використати одночасно в чаті. Якщо кількість файлів перевищує цей ліміт, файли не будуть завантажені.", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Оцінка повинна бути в діапазоні від 0.0 (0%) до 1.0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "Температура моделі. Збільшення температури зробить відповіді моделі більш креативними. (За замовчуванням: 0.8)", "Theme": "Тема", "Thinking...": "Думаю...", "This action cannot be undone. Do you wish to continue?": "Цю дію не можна скасувати. Ви бажаєте продовжити?", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Це забезпечує збереження ваших цінних розмов у безпечному бекенд-сховищі. Дякуємо!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Це експериментальна функція, вона може працювати не так, як очікувалося, і може бути змінена в будь-який час.", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "Ця опція керує кількістю токенів, яка зберігається під час оновлення контексту. Наприклад, якщо встановити як 2, останні 2 токени контексту розмови будуть збережені. Збереження контексту може допомогти підтримувати безперервність розмови, але воно також може зменшити здатність відповідати на нові теми. (За замовчуванням: 24)", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "Ця опція встановлює максимальну кількість токенів, які модель може генерувати в своєму відповіді. Збільшення цього обмеження дозволяє моделі надавати довші відповіді, але також може збільшити вірогідність генерації недопоможного чи невідповідного вмісту. (За замовчуванням: 128)", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Цей варіант видалить усі існуючі файли в колекції та замінить їх новими завантаженими файлами.", "This response was generated by \"{{model}}\"": "Цю відповідь згенеровано за допомогою \"{{model}}\"", "This will delete": "Це призведе до видалення", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "Це видалить <strong>{{NAME}}</strong> та <strong>всі його вмісти</strong>.", "This will delete all models including custom models": "Це видалить усі моделі, включаючи користувацькі моделі", "This will delete all models including custom models and cannot be undone.": "Це видалить усі моделі, включаючи користувацькі моделі, і не може бути скасовано.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Це скине базу знань і синхронізує всі файли. Ви бажаєте продовжити?", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Потрібна URL-адреса сервера Tika.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Порада: Оновіть кілька слотів змінних послідовно, натискаючи клавішу табуляції у вікні чату після кожної заміни.", "Title": "Заголовок", "Title (e.g. Tell me a fun fact)": "Заголовок (напр., Розкажіть мені цікавий факт)", "Title Auto-Generation": "Автогенерація заголовків", "Title cannot be an empty string.": "Заголовок не може бути порожнім рядком.", "Title Generation Prompt": "Промт для генерування заголовків", "TLS": "TLS", "To access the available model names for downloading,": "Щоб отримати доступ до назв доступних для завантаження моделей,", "To access the GGUF models available for downloading,": "Щоб отримати доступ до моделей GGUF, які можна завантажити,,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Щоб отримати доступ до веб-інтерфейсу, зверніться до адміністратора. Адміністратори можуть керувати статусами користувачів з Панелі адміністратора.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Щоб прикріпити базу знань тут, спочатку додайте їх до робочого простору \"Знання\".", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Для захисту вашої конфіденційності з вашими відгуками діляться лише оцінками, ID моделей, тегами та метаданими — ваші журнали чату залишаються приватними і не включаються.", "To select actions here, add them to the \"Functions\" workspace first.": "Щоб вибрати дії тут, спочатку додайте їх до робочої області \"Функції\".", "To select filters here, add them to the \"Functions\" workspace first.": "Щоб обрати фільтри тут, спочатку додайте їх до робочої області \"Функції\".", "To select toolkits here, add them to the \"Tools\" workspace first.": "Щоб обрати тут набори інструментів, спочатку додайте їх до робочої області \"Інструменти\".", "Toast notifications for new updates": "Сповіщення Toast про нові оновлення", "Today": "Сьогодні", "Toggle settings": "Переключити налаштування", "Toggle sidebar": "Переключити бокову панель", "Toggle temporary chat": "", "Token": "Токен", "Tokens To Keep On Context Refresh (num_keep)": "Токени для збереження при оновленні контексту (num_keep)", "Tool created successfully": "Інструмент успішно створено", "Tool deleted successfully": "Інструмент успішно видалено", "Tool Description": "Опис інструменту", "Tool ID": "ID інструменту", "Tool imported successfully": "Інструмент успішно імпортовано", "Tool Name": "Назва інструменту", "Tool updated successfully": "Інструмент успішно оновлено", "Tools": "Інструменти", "Tools Access": "Доступ до інструментів", "Tools are a function calling system with arbitrary code execution": "Інструменти - це система виклику функцій з довільним виконанням коду", "Tools have a function calling system that allows arbitrary code execution": "Інструменти мають систему виклику функцій, яка дозволяє виконання довільного коду", "Tools have a function calling system that allows arbitrary code execution.": "Інструменти мають систему виклику функцій, яка дозволяє виконання довільного коду.", "Top K": "Top K", "Top P": "Top P", "Transformers": "Трансформери", "Trouble accessing Ollama?": "Проблеми з доступом до Ollama?", "TTS Model": "Модель TTS", "TTS Settings": "Налаштування TTS", "TTS Voice": "Голос TTS", "Type": "Тип", "Type Hugging Face Resolve (Download) URL": "Введіть URL ресурсу Hugging Face Resolve (завантаження)", "Uh-oh! There was an issue with the response.": "Ой-ой! Сталася проблема з відповіддю.", "UI": "Користувацький інтерфейс", "Unarchive All": "Розархівувати все", "Unarchive All Archived Chats": "Розархівувати всі архівовані чати", "Unarchive Chat": "Розархівувати чат", "Unlock mysteries": "Розкрийте таємниці", "Unpin": "Відчепити", "Unravel secrets": "Розплутуйте секрети", "Untagged": "Без тегів", "Update": "Оновлення", "Update and Copy Link": "Оновлення та копіювання посилання", "Update for the latest features and improvements.": "Оновіть програми для нових функцій та покращень.", "Update password": "Оновити пароль", "Updated": "Оновлено", "Updated at": "Оновлено на", "Updated At": "Оновлено на", "Upload": "Завант<PERSON><PERSON><PERSON>ти", "Upload a GGUF model": "Завантажити GGUF модель", "Upload directory": "Завантажити каталог", "Upload files": "Завантажити файли", "Upload Files": "Завантажити файли", "Upload Pipeline": "Завантажити конвеєр", "Upload Progress": "Прогрес завантаження", "URL": "URL", "URL Mode": "Режим URL-адреси", "USAi Chat can make mistakes. Review all responses for accuracy. Your agency’s AI and privacy policies apply.": "", "Use '#' in the prompt input to load and include your knowledge.": "Використовуйте '#' у полі введення підказки, щоб завантажити та включити свої знання.", "Use Gravatar": "Змінити аватар", "Use groups to group your users and assign permissions.": "Використовуйте групи, щоб об’єднувати користувачів і призначати дозволи.", "Use Initials": "Використовувати ініціали", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "користувач", "User": "Користув<PERSON><PERSON>", "User location successfully retrieved.": "Місцезнаходження користувача успішно знайдено.", "Username": "Ім'я користувача", "Users": "Користувачі", "Using the default arena model with all models. Click the plus button to add custom models.": "Використовуючи модель арени за замовчуванням з усіма моделями. Натисніть кнопку плюс, щоб додати користувацькі моделі.", "Utilize": "Використовувати", "Valid time units:": "Дійсні одиниці часу:", "Valves": "Кла<PERSON>ани", "Valves updated": "Клапани оновлено", "Valves updated successfully": "Клапани успішно оновлено", "variable": "змінна", "variable to have them replaced with clipboard content.": "зм<PERSON>нна, щоб замінити їх вмістом буфера обміну.", "Version": "Версія", "Version {{selectedVersion}} of {{totalVersions}}": "Версія {{selectedVersion}} з {{totalVersions}}", "Very bad": "", "View Replies": "", "Visibility": "Видимість", "Voice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Voice Input": "Голосове введення", "Warning": "Увага!", "Warning:": "Увага:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Попередження: Увімкнення цього дозволить користувачам завантажувати довільний код на сервер.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Попередження: Як<PERSON>о ви оновлюєте або змінюєте модель вбудовування, вам потрібно буде повторно імпортувати всі документи.", "Web": "<PERSON>е<PERSON>", "Web API": "Веб-API", "Web Loader Settings": "Налаштування веб-завантажувача", "Web Search": "Веб-пошук", "Web Search Engine": "Веб-пошукова система", "Web Search Query Generation": "Генерація запиту для пошуку в мережі", "Webhook URL": "URL веб-запиту", "WebUI Settings": "Налаштування WebUI", "WebUI URL": "WebUI URL", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI надсилатиме запити до \"{{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI надсилатиме запити до \"{{url}}/chat/completions\"", "Welcome, {{name}}!": "", "What are you trying to achieve?": "Чого ви прагнете досягти?", "What are you working on?": "Над чим ти працюєш?", "What didn't you like about this response?": "", "What’s New in": "Що нового в", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "Коли активовано, модель буде відповідати на кожне повідомлення чату в режимі реального часу, генеруючи відповідь, як тільки користувач надішле повідомлення. Цей режим корисний для застосувань життєвих вітань чатів, але може позначитися на продуктивності на повільнішому апаратному забезпеченні.", "wherever you are": "де б ви не були", "Whisper (Local)": "<PERSON>hisper (Локально)", "Widescreen Mode": "Широкоекранний режим", "Won": "Переможець", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "Працює разом з top-k. Більше значення (напр., 0.95) приведе до більш різноманітного тексту, тоді як менше значення (напр., 0.5) згенерує більш зосереджений і консервативний текст. (За замовчуванням: 0.9)", "Workspace": "Робочий простір", "Workspace Permissions": "Дозволи робочого простору.", "Write a prompt suggestion (e.g. Who are you?)": "Напишіть промт (напр., Хто ти?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Напишіть стислий зміст у 50 слів, який узагальнює [тема або ключове слово].", "Write something...": "Напишіть щось...", "Write your model template content here": "Напишіть вміст шаблону моделі тут", "Yesterday": "Вчора", "You": "Ви", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Ви можете спілкуватися лише з максимальною кількістю {{maxCount}} файлів одночасно.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Ви можете налаштувати ваші взаємодії з мовними моделями, додавши спогади через кнопку 'Керувати' внизу, що зробить їх більш корисними та персоналізованими для вас.", "You cannot upload an empty file.": "Ви не можете завантажити порожній файл.", "You have no archived conversations.": "У вас немає архівованих розмов.", "You have shared this chat": "Ви поділилися цим чатом", "You're a helpful assistant.": "Ви корисний асистент.", "Your account status is currently pending activation.": "Статус вашого облікового запису наразі очікує на активацію.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Весь ваш внесок піде безпосередньо розробнику плагіна; Open WebUI не бере жодних відсотків. Од<PERSON><PERSON>, обрана платформа фінансування може мати свої власні збори.", "Youtube": "Youtube", "Youtube Loader Settings": "Налаштування завантажувача Youtube"}