<script lang="ts">
	import Bolt from '$lib/components/icons/Bolt.svelte';
	import { onMount, getContext, createEventDispatcher } from 'svelte';

	const i18n = getContext('i18n');
	const dispatch = createEventDispatcher();

	export let suggestionPrompts = [];
	export let className = '';

	let prompts = [];

	$: prompts = suggestionPrompts ?? [];
</script>

<div class="flex flex-wrap justify-center gap-2">
	{#each prompts as prompt, promptIdx}
		<button
			class="chat-suggested-prompts w-full sm:w-auto sm:max-w-xs rounded-full bg-transparent"
			tabindex="0"
			on:click={() => {
				dispatch('select', prompt.content);
			}}
		>
			<div class="truncate">
				{#if prompt.title && prompt.title[0] !== ''}
					{prompt.title[0]}
				{:else}
					{prompt.content}
				{/if}
			</div>
		</button>
	{/each}
</div>
