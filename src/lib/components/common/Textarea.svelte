<script lang="ts">
	import { onMount, tick } from 'svelte';

	export let id = '';
	export let value = '';
	export let placeholder = '';
	export let className =
		'w-full rounded-lg px-3 py-2 text-sm bg-gray-50 dark:text-gray-300 dark:bg-gray-850 outline-none resize-none h-full';
	export let autoResize = false;
	export let maxHeight = 320;
	export let minHeight = 40;

	export let onKeydown: Function = () => {};

	let textareaElement;

	// Auto-resize function for Safari and other browsers
	const adjustHeight = () => {
		if (textareaElement && autoResize) {
			textareaElement.style.height = 'auto';
			const newHeight = Math.max(
				minHeight,
				Math.min(textareaElement.scrollHeight, maxHeight)
			);
			textareaElement.style.height = `${newHeight}px`;
		}
	};

	// Adjust height when value changes
	$: if (textareaElement && value !== undefined) {
		textareaElement.value = value ?? '';
		if (autoResize) {
			tick().then(() => adjustHeight());
		}
	}

	// Adjust height on mount and after setting the element.
	onMount(async () => {
		await tick();
		if (autoResize) {
			adjustHeight();
		}
	});

	// Handle paste event to ensure only plaintext is pasted
	function handlePaste(event: ClipboardEvent) {
		event.preventDefault(); // Prevent the default paste action
		const clipboardData = event.clipboardData?.getData('text/plain'); // Get plaintext from clipboard

		// Insert plaintext into the textarea
		document.execCommand('insertText', false, clipboardData);

		// Adjust height after paste if auto-resize is enabled
		if (autoResize) {
			tick().then(() => adjustHeight());
		}
	}
</script>

<textarea
	{id}
	bind:this={textareaElement}
	class="{className} whitespace-pre-wrap relative {value
		? !value.trim()
			? 'placeholder'
			: ''
		: 'placeholder'}"
	style="{autoResize ? '' : 'field-sizing: content;'} -moz-user-select: text !important; {autoResize ? `min-height: ${minHeight}px; max-height: ${maxHeight}px;` : ''}"
	on:input={(e) => {
		const text = e.target.value;
		if (text === '\n') {
			e.target.value = '';
			value = '';
			return;
		}

		value = text;

		// Auto-resize if enabled
		if (autoResize) {
			adjustHeight();
		}
	}}
	on:focus={() => {
		if (autoResize) {
			adjustHeight();
		}
	}}
	on:paste={handlePaste}
	on:keydown={onKeydown}
	data-placeholder={placeholder}
/>

<style>
	.placeholder::before {
		/* abolute */
		position: absolute;
		content: attr(data-placeholder);
		color: #adb5bd;
		overflow: hidden;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
		pointer-events: none;
		touch-action: none;
	}
</style>
