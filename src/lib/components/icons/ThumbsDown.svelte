<script lang="ts">
	export let className = 'w-4 h-4';
	export let strokeWidth = '1.3';
</script>

<svg
	class={className + ($$restProps.class ? ' ' + $$restProps.class : '')}
	viewBox="0 0 16 16"
	fill="none"
	stroke-width={strokeWidth}
	stroke="currentColor"
	xmlns="http://www.w3.org/2000/svg"
>
	<path
		d="M6.09098 10.2255C6.09098 10.2255 5.43279 11.221 5.09777 12.2407C4.43563 14.256 6.75312 14.9278 7.41526 13.2484C7.78412 12.3128 8.40847 11.5689 9.0706 11.2331C10.0871 10.7175 10.3477 10.3114 10.8763 9.49538C10.9886 9.32196 11.1815 9.21782 11.3881 9.21782V9.21782M6.75312 9.88958H4.60116H3.10607C2.12959 9.88958 1.43853 8.93512 1.74332 8.00743L1.75056 7.9854C1.77458 7.9123 1.81518 7.84574 1.8692 7.79094V7.79094C2.01825 7.63973 2.05523 7.41073 1.96137 7.22028L1.95474 7.20682C1.84645 6.9871 1.81921 6.7362 1.87782 6.49835L2.06893 5.72282C2.10114 5.59209 2.16803 5.47248 2.26255 5.37659L2.58794 5.04646C2.69462 4.93824 2.69462 4.76442 2.58794 4.6562V4.6562C2.50488 4.57193 2.48427 4.44431 2.53658 4.33818L2.72323 3.95946C3.16357 3.06599 4.07319 2.5002 5.06928 2.5002H8.0774L10.726 2.83608H11.3881"
		stroke-linecap="round"
	/>
	<path
		d="M14.1249 8.65039C14.1249 9.20267 13.6772 9.65039 13.1249 9.65039L12.3881 9.65039C11.8358 9.65039 11.3881 9.20267 11.3881 8.65039V3.50039C11.3881 2.94811 11.8358 2.50039 12.3881 2.50039L13.1249 2.50039C13.6772 2.50039 14.1249 2.94811 14.1249 3.50039V8.65039Z"
	/>
</svg>
