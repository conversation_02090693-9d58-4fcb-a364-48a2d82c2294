<script lang="ts">
	export let className = '';
	export let strokeWidth = '2';
	// text-gray-400 is rbg(180,180,180)/#b4b4b4 which matches hamburger stroke color in dark mode
	let defaultClasses = 'size-6 dark:text-gray-400';
	let classes =
		defaultClasses + ' ' + className + ($$restProps.class ? ' ' + $$restProps.class : '');
</script>

<svg
	width="26"
	height="26"
	viewBox="0 0 26 26"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	class={classes}
>
	<path
		d="M13.8 8.5V16.5"
		stroke={$$restProps.class === 'only-icon-button-stroke-svg' ? '' : 'currentColor'}
		stroke-width={strokeWidth}
		stroke-linecap="round"
		stroke-linejoin="round"
	/>
	<path
		d="M9.80005 12.5H17.8"
		stroke={$$restProps.class === 'only-icon-button-stroke-svg' ? '' : 'currentColor'}
		stroke-width={strokeWidth}
		stroke-linecap="round"
		stroke-linejoin="round"
	/>
	<path
		d="M25 12.3334C25.0046 14.0932 24.5934 15.8292 23.8 17.4C22.8592 19.2823 21.413 20.8656 19.6232 21.9724C17.8335 23.0792 15.771 23.6659 13.6666 23.6667C11.9068 23.6713 10.1708 23.2601 8.59999 22.4667L1 25L3.53333 17.4C2.73991 15.8292 2.32874 14.0932 2.33333 12.3334C2.33415 10.229 2.92081 8.16649 4.02762 6.37676C5.13443 4.58703 6.71766 3.14079 8.59999 2.20004C10.1708 1.40661 11.9068 0.99545 13.6666 1.00004H14.3333C17.1124 1.15336 19.7374 2.32638 21.7055 4.29451C23.6736 6.26264 24.8466 8.88756 25 11.6667V12.3334Z"
		stroke={$$restProps.class === 'only-icon-button-stroke-svg' ? '' : 'currentColor'}
		stroke-width={strokeWidth}
		stroke-linecap="round"
		stroke-linejoin="round"
	/>
</svg>
