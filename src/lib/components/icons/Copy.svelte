<script lang="ts">
	import { blur } from 'svelte/transition';

	export let className = 'w-4 h-4';
	export let strokeWidth = '1.3';
</script>

<svg
	fill="none"
	stroke="currentColor"
	stroke-width={strokeWidth}
	viewBox="0 0 16 16"
	class={className + ($$restProps.class ? ' ' + $$restProps.class : '')}
	xmlns="http://www.w3.org/2000/svg"
	transition:blur={$$restProps.transition === 'blur' ? { duration: 300 } : undefined}
>
	<path
		stroke-linecap="round"
		stroke-linejoin="round"
		d="M11.2727 5.27273V3.63636C11.2727 2.73262 10.5401 2 9.63636 2H3.63636C2.73262 2 2 2.73263 2 3.63636V9.63636C2 10.5401 2.73262 11.2727 3.63636 11.2727H5.27273M11.2727 5.27273H12.3636C13.2674 5.27273 14 6.00535 14 6.90909V12.3636C14 13.2674 13.2674 14 12.3636 14H6.90909C6.00535 14 5.27273 13.2674 5.27273 12.3636V11.2727M11.2727 5.27273H6.90909C6.00535 5.27273 5.27273 6.00535 5.27273 6.90909V11.2727"
	/>
</svg>
