<script lang="ts">
	export let className = 'w-4 h-4';
	export let strokeWidth = '1.3';
</script>

<svg
	class={className + ($$restProps.class ? ' ' + $$restProps.class : '')}
	viewBox="0 0 16 16"
	fill="none"
	stroke-width={strokeWidth}
	stroke="currentColor"
	xmlns="http://www.w3.org/2000/svg"
>
	<path
		d="M11.6087 2.87035L12.6122 1.86677C13.1013 1.37773 13.8941 1.37773 14.3832 1.86677C14.8722 2.3558 14.8722 3.14869 14.3832 3.63773L7.81392 10.207C7.46083 10.5601 7.02533 10.8197 6.54677 10.9622L4.75361 11.4964L5.28776 9.70324C5.43032 9.22468 5.68987 8.78918 6.04295 8.43609L11.6087 2.87035ZM11.6087 2.87035L13.3708 4.6324M12.7681 8.8249V11.9973C12.7681 12.8272 12.0953 13.5 11.2654 13.5H4.25271C3.42279 13.5 2.75 12.8272 2.75 11.9973L2.75 4.98464C2.75 4.15472 3.42279 3.48193 4.25271 3.48193H7.4251"
		stroke-linecap="round"
		stroke-linejoin="round"
	/>
</svg>
