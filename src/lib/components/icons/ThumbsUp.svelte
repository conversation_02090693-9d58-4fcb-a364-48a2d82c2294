<script lang="ts">
	export let className = 'w-4 h-4';
	export let strokeWidth = '1.3';
</script>

<svg
	class={className + ($$restProps.class ? ' ' + $$restProps.class : '')}
	viewBox="0 0 16 16"
	fill="none"
	stroke-width={strokeWidth}
	stroke="currentColor"
	xmlns="http://www.w3.org/2000/svg"
>
	<path
		d="M10.034 5.97473C10.034 5.97473 10.6921 4.97915 11.0272 3.95948C11.6893 1.94421 9.37182 1.27242 8.70968 2.95184C8.34082 3.88742 7.71647 4.63125 7.05433 4.96709C6.03781 5.48274 5.77727 5.88882 5.24866 6.70481C5.13632 6.87824 4.94348 6.98238 4.73685 6.98238V6.98238M9.37182 6.31061H11.5238H13.0189C13.9953 6.31061 14.6864 7.26508 14.3816 8.19277L14.3744 8.21479C14.3504 8.2879 14.3098 8.35445 14.2557 8.40925V8.40925C14.1067 8.56047 14.0697 8.78946 14.1636 8.97992L14.1702 8.99338C14.2785 9.2131 14.3057 9.464 14.2471 9.70184L14.056 10.4774C14.0238 10.6081 13.9569 10.7277 13.8624 10.8236L13.537 11.1537C13.4303 11.262 13.4303 11.4358 13.537 11.544V11.544C13.6201 11.6283 13.6407 11.7559 13.5884 11.862L13.4017 12.2407C12.9614 13.1342 12.0517 13.7 11.0557 13.7H8.04754L5.39899 13.3641H4.73685"
		stroke-linecap="round"
	/>
	<path
		d="M2 7.54981C2 6.99752 2.44772 6.5498 3 6.54981L3.73684 6.54981C4.28913 6.54981 4.73684 6.99752 4.73684 7.54981V12.6998C4.73684 13.2521 4.28913 13.6998 3.73684 13.6998L3 13.6998C2.44772 13.6998 2 13.2521 2 12.6998V7.54981Z"
	/>
</svg>
