<script lang="ts">
	import { toast } from 'svelte-sonner';
	import { v4 as uuidv4 } from 'uuid';

	import { goto } from '$app/navigation';
	import {
		user,
		chats,
		settings,
		showSettings,
		chatId,
		tags,
		showSidebar,
		mobile,
		showArchivedChats,
		pinnedChats,
		scrollPaginationEnabled,
		currentChatPage,
		temporaryChatEnabled,
		channels,
		socket,
		config,
		WEBUI_NAME
	} from '$lib/stores';
	import { onMount, getContext, tick, onDestroy } from 'svelte';

	const i18n = getContext('i18n');

	import {
		deleteChatById,
		getChatList,
		getAllTags,
		getChatListBySearchText,
		createNewChat,
		getPinnedChatList,
		toggleChatPinnedStatusById,
		getChatPinnedStatusById,
		getChatById,
		updateChatFolderIdById,
		importChat
	} from '$lib/apis/chats';
	import { createNewFolder, getFolders, updateFolderParentIdById } from '$lib/apis/folders';
	import { WEBUI_BASE_URL } from '$lib/constants';

	import ArchivedChatsModal from './Sidebar/ArchivedChatsModal.svelte';
	import UserMenu from './Sidebar/UserMenu.svelte';
	import ChatItem from './Sidebar/ChatItem.svelte';
	import Spinner from '../common/Spinner.svelte';
	import Loader from '../common/Loader.svelte';
	import AddFilesPlaceholder from '../AddFilesPlaceholder.svelte';
	import SearchInput from './Sidebar/SearchInput.svelte';
	import Folder from '../common/Folder.svelte';
	import Plus from '../icons/Plus.svelte';
	import Tooltip from '../common/Tooltip.svelte';
	import Folders from './Sidebar/Folders.svelte';
	import { getChannels, createNewChannel } from '$lib/apis/channels';
	import ChannelModal from './Sidebar/ChannelModal.svelte';
	import ChannelItem from './Sidebar/ChannelItem.svelte';
	import NewChatIcon from '../icons/NewChatIcon.svelte';
	import MenuDrawerOpen from '../icons/MenuDrawerOpen.svelte';
	import PlusCircle from '../icons/PlusCircle.svelte';

	const BREAKPOINT = 768;

	let navElement;
	let search = '';

	let shiftKey = false;

	let selectedChatId = null;
	let showDropdown = false;
	let showPinnedChat = true;

	let showCreateChannel = false;

	// Pagination variables
	let chatListLoading = false;
	let allChatsLoaded = false;

	let folders = {};

	const initFolders = async () => {
		const folderList = await getFolders().catch((error) => {
			toast.error(error);
			return [];
		});

		folders = {};

		// First pass: Initialize all folder entries
		for (const folder of folderList) {
			// Ensure folder is added to folders with its data
			folders[folder.id] = { ...(folders[folder.id] || {}), ...folder };
		}

		// Second pass: Tie child folders to their parents
		for (const folder of folderList) {
			if (folder.parent_id) {
				// Ensure the parent folder is initialized if it doesn't exist
				if (!folders[folder.parent_id]) {
					folders[folder.parent_id] = {}; // Create a placeholder if not already present
				}

				// Initialize childrenIds array if it doesn't exist and add the current folder id
				folders[folder.parent_id].childrenIds = folders[folder.parent_id].childrenIds
					? [...folders[folder.parent_id].childrenIds, folder.id]
					: [folder.id];

				// Sort the children by updated_at field
				folders[folder.parent_id].childrenIds.sort((a, b) => {
					return folders[b].updated_at - folders[a].updated_at;
				});
			}
		}
	};

	const createFolder = async (name = 'Untitled') => {
		if (name === '') {
			toast.error($i18n.t('Folder name cannot be empty.'));
			return;
		}

		const rootFolders = Object.values(folders).filter((folder) => folder.parent_id === null);
		if (rootFolders.find((folder) => folder.name.toLowerCase() === name.toLowerCase())) {
			// If a folder with the same name already exists, append a number to the name
			let i = 1;
			while (
				rootFolders.find((folder) => folder.name.toLowerCase() === `${name} ${i}`.toLowerCase())
			) {
				i++;
			}

			name = `${name} ${i}`;
		}

		// Add a dummy folder to the list to show the user that the folder is being created
		const tempId = uuidv4();
		folders = {
			...folders,
			tempId: {
				id: tempId,
				name: name,
				created_at: Date.now(),
				updated_at: Date.now()
			}
		};

		const res = await createNewFolder(name).catch((error) => {
			toast.error(error);
			return null;
		});

		if (res) {
			await initFolders();
		}
	};

	const initChannels = async () => {
		await channels.set(await getChannels());
	};

	const initChatList = async () => {
		// Reset pagination variables
		tags.set(await getAllTags());
		pinnedChats.set(await getPinnedChatList());
		initFolders();

		currentChatPage.set(1);
		allChatsLoaded = false;

		if (search) {
			await chats.set(await getChatListBySearchText(search, $currentChatPage));
		} else {
			await chats.set(await getChatList($currentChatPage));
		}

		// Enable pagination
		scrollPaginationEnabled.set(true);
	};

	const loadMoreChats = async () => {
		chatListLoading = true;

		currentChatPage.set($currentChatPage + 1);

		let newChatList = [];

		if (search) {
			newChatList = await getChatListBySearchText(search, $currentChatPage);
		} else {
			newChatList = await getChatList($currentChatPage);
		}

		// once the bottom of the list has been reached (no results) there is no need to continue querying
		allChatsLoaded = newChatList.length === 0;
		await chats.set([...($chats ? $chats : []), ...newChatList]);

		chatListLoading = false;
	};

	let searchDebounceTimeout;

	const searchDebounceHandler = async () => {
		console.log('search', search);
		chats.set(null);

		if (searchDebounceTimeout) {
			clearTimeout(searchDebounceTimeout);
		}

		if (search === '') {
			await initChatList();
			return;
		} else {
			searchDebounceTimeout = setTimeout(async () => {
				allChatsLoaded = false;
				currentChatPage.set(1);
				await chats.set(await getChatListBySearchText(search));

				if ($chats.length === 0) {
					tags.set(await getAllTags());
				}
			}, 1000);
		}
	};

	const importChatHandler = async (items, pinned = false, folderId = null) => {
		console.log('importChatHandler', items, pinned, folderId);
		for (const item of items) {
			console.log(item);
			if (item.chat) {
				await importChat(item.chat, item?.meta ?? {}, pinned, folderId);
			}
		}

		initChatList();
	};

	const inputFilesHandler = async (files) => {
		console.log(files);

		for (const file of files) {
			const reader = new FileReader();
			reader.onload = async (e) => {
				const content = e.target.result;

				try {
					const chatItems = JSON.parse(content);
					importChatHandler(chatItems);
				} catch {
					toast.error($i18n.t(`Invalid file format.`));
				}
			};

			reader.readAsText(file);
		}
	};

	const tagEventHandler = async (type, tagName, chatId) => {
		console.log(type, tagName, chatId);
		if (type === 'delete') {
			initChatList();
		} else if (type === 'add') {
			initChatList();
		}
	};

	let draggedOver = false;

	const onDragOver = (e) => {
		e.preventDefault();

		// Check if a file is being draggedOver.
		if (e.dataTransfer?.types?.includes('Files')) {
			draggedOver = true;
		} else {
			draggedOver = false;
		}
	};

	const onDragLeave = () => {
		draggedOver = false;
	};

	const onDrop = async (e) => {
		e.preventDefault();
		console.log(e); // Log the drop event

		// Perform file drop check and handle it accordingly
		if (e.dataTransfer?.files) {
			const inputFiles = Array.from(e.dataTransfer?.files);

			if (inputFiles && inputFiles.length > 0) {
				console.log(inputFiles); // Log the dropped files
				inputFilesHandler(inputFiles); // Handle the dropped files
			}
		}

		draggedOver = false; // Reset draggedOver status after drop
	};

	let touchstart;
	let touchend;

	function checkDirection() {
		const screenWidth = window.innerWidth;
		const swipeDistance = Math.abs(touchend.screenX - touchstart.screenX);
		if (touchstart.clientX < 40 && swipeDistance >= screenWidth / 8) {
			if (touchend.screenX < touchstart.screenX) {
				showSidebar.set(false);
			}
			if (touchend.screenX > touchstart.screenX) {
				showSidebar.set(true);
			}
		}
	}

	const onTouchStart = (e) => {
		touchstart = e.changedTouches[0];
		console.log(touchstart.clientX);
	};

	const onTouchEnd = (e) => {
		touchend = e.changedTouches[0];
		checkDirection();
	};

	const onKeyDown = (e) => {
		if (e.key === 'Shift') {
			shiftKey = true;
		}
	};

	const onKeyUp = (e) => {
		if (e.key === 'Shift') {
			shiftKey = false;
		}
	};

	const onFocus = () => {};

	const onBlur = () => {
		shiftKey = false;
		selectedChatId = null;
	};

	onMount(async () => {
		showPinnedChat = localStorage?.showPinnedChat ? localStorage.showPinnedChat === 'true' : true;

		mobile.subscribe((e) => {
			if ($showSidebar && e) {
				showSidebar.set(false);
			}

			if (!$showSidebar && !e) {
				showSidebar.set(true);
			}
		});

		showSidebar.set(!$mobile ? localStorage.sidebar === 'true' : false);
		showSidebar.subscribe((value) => {
			localStorage.sidebar = value;
		});

		await initChannels();
		await initChatList();

		window.addEventListener('keydown', onKeyDown);
		window.addEventListener('keyup', onKeyUp);

		window.addEventListener('touchstart', onTouchStart);
		window.addEventListener('touchend', onTouchEnd);

		window.addEventListener('focus', onFocus);
		window.addEventListener('blur', onBlur);

		const dropZone = document.getElementById('sidebar');

		dropZone?.addEventListener('dragover', onDragOver);
		dropZone?.addEventListener('drop', onDrop);
		dropZone?.addEventListener('dragleave', onDragLeave);
	});

	onDestroy(() => {
		window.removeEventListener('keydown', onKeyDown);
		window.removeEventListener('keyup', onKeyUp);

		window.removeEventListener('touchstart', onTouchStart);
		window.removeEventListener('touchend', onTouchEnd);

		window.removeEventListener('focus', onFocus);
		window.removeEventListener('blur', onBlur);

		const dropZone = document.getElementById('sidebar');

		dropZone?.removeEventListener('dragover', onDragOver);
		dropZone?.removeEventListener('drop', onDrop);
		dropZone?.removeEventListener('dragleave', onDragLeave);
	});

	// Dynamically calculate scroll area height based on UI elements
	let totalOffset = 110;
	$: {
		let pinnedSectionHeight = 0;
		if ($pinnedChats.length > 0) {
			if (showPinnedChat) {
				// expanded header
				pinnedSectionHeight = 26 + $pinnedChats.length * 36;
			} else {
				// collapse header
				pinnedSectionHeight = 26;
			}
		}
		const workspaceVisible =
			$user?.role === 'admin' ||
			$user?.permissions?.workspace?.models ||
			$user?.permissions?.workspace?.knowledge ||
			$user?.permissions?.workspace?.prompts ||
			$user?.permissions?.workspace?.tools;

		totalOffset = 110 + pinnedSectionHeight + (workspaceVisible ? 34 : 0);
	}
</script>

<ArchivedChatsModal
	bind:show={$showArchivedChats}
	on:change={async () => {
		await initChatList();
	}}
/>

<ChannelModal
	bind:show={showCreateChannel}
	onSubmit={async ({ name, access_control }) => {
		const res = await createNewChannel({
			name: name,
			access_control: access_control
		}).catch((error) => {
			toast.error(error);
			return null;
		});

		if (res) {
			$socket.emit('join-channels', { user: { id: $user.id } });
			await initChannels();
			showCreateChannel = false;
		}
	}}
/>
<div>
	<!-- sidebar-new-chat-button -->
	<a
		id="sidebar-new-chat-button"
		class="sidebar-new-chat-btn dark:hover:bg-gray-900 transition"
		href="/"
		draggable="false"
		on:click={async () => {
			selectedChatId = null;
			await goto('/');
			const newChatButton = document.getElementById('new-chat-button');
			setTimeout(() => {
				newChatButton?.click();
				if ($mobile) {
					showSidebar.set(false);
				}
			}, 0);
		}}
	>
		<div>
			<PlusCircle />
		</div>
		<div class="dark:text-white">
			New {$i18n.t('Chat')}
		</div>
	</a>

	{#if $user?.role === 'admin' || $user?.permissions?.workspace?.models || $user?.permissions?.workspace?.knowledge || $user?.permissions?.workspace?.prompts || $user?.permissions?.workspace?.tools}
		<div class="sidebar-main-label px-1.5 flex justify-center dark:text-gray-200">
			<a
				class="flex-grow flex space-x-3 rounded-lg px-2 py-[7px] hover:bg-steel-200 dark:hover:bg-gray-900 transition"
				href="/workspace"
				on:click={() => {
					selectedChatId = null;
					chatId.set('');

					if ($mobile) {
						showSidebar.set(false);
					}
				}}
				draggable="false"
			>
				<div class="self-center">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						fill="none"
						viewBox="0 0 24 24"
						stroke-width="2"
						stroke="currentColor"
						class="size-[1.1rem]"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							d="M13.5 16.875h3.375m0 0h3.375m-3.375 0V13.5m0 3.375v3.375M6 10.5h2.25a2.25 2.25 0 0 0 2.25-2.25V6a2.25 2.25 0 0 0-2.25-2.25H6A2.25 2.25 0 0 0 3.75 6v2.25A2.25 2.25 0 0 0 6 10.5Zm0 9.75h2.25A2.25 2.25 0 0 0 10.5 18v-2.25a2.25 2.25 0 0 0-2.25-2.25H6a2.25 2.25 0 0 0-2.25 2.25V18A2.25 2.25 0 0 0 6 20.25Zm9.75-9.75H18a2.25 2.25 0 0 0 2.25-2.25V6A2.25 2.25 0 0 0 18 3.75h-2.25A2.25 2.25 0 0 0 13.5 6v2.25a2.25 2.25 0 0 0 2.25 2.25Z"
						/>
					</svg>
				</div>

				<div class="flex self-center">
					<div class=" self-center font-medium text-sm">{$i18n.t('Workspace')}</div>
				</div>
			</a>
		</div>
	{/if}

	<div class="relative {$temporaryChatEnabled ? 'opacity-20' : ''}">
		{#if $temporaryChatEnabled}
			<div class="absolute z-40 w-full h-full flex justify-center"></div>
		{/if}
		{#if $config?.features?.enable_sidebar_search}
			<SearchInput
				bind:value={search}
				on:input={searchDebounceHandler}
				placeholder={$i18n.t('Search')}
			/>
		{/if}
		{#if $config?.features?.enable_sidebar_create_folder}
			<div class="absolute z-40 right-3.5 top-1">
				<Tooltip content={$i18n.t('New folder')}>
					<button
						class="p-1 rounded-lg bg-gray-50 hover:bg-gray-100 dark:bg-gray-950 dark:hover:bg-gray-900 text-gray-600 dark:text-gray-500 transition"
						on:click={() => {
							createFolder();
						}}
					>
						<Plus className=" size-3" strokeWidth="2.5" />
					</button>
				</Tooltip>
			</div>
		{/if}
	</div>

	<div
		class="chat-content-wrapper {$temporaryChatEnabled ? 'opacity-20' : ''}"
		style={`height: calc(100vh - ${totalOffset}px)`}
	>
		{#if !search && $pinnedChats.length > 0}
			<div class="flex flex-col space-y-1 rounded-xl">
				<Folder
					className="px-2"
					bind:open={showPinnedChat}
					on:change={(e) => {
						localStorage.setItem('showPinnedChat', e.detail);
						console.log(e.detail);
					}}
					on:import={(e) => {
						importChatHandler(e.detail, true);
					}}
					on:drop={async (e) => {
						const { type, id, item } = e.detail;

						if (type === 'chat') {
							let chat = await getChatById(id).catch((error) => {
								return null;
							});
							if (!chat && item) {
								chat = await importChat(item.chat, item?.meta ?? {});
							}

							if (chat) {
								console.log(chat);
								if (chat.folder_id) {
									const res = await updateChatFolderIdById(chat.id, null).catch((error) => {
										toast.error(error);
										return null;
									});
								}

								if (!chat.pinned) {
									const res = await toggleChatPinnedStatusById(chat.id);
								}

								initChatList();
							}
						}
					}}
					name={$i18n.t('Pinned')}
				>
					<div
						class="ml-3 pl-1 mt-[1px] flex flex-col overflow-y-auto scrollbar-hidden border-s border-gray-100 dark:border-gray-900"
					>
						{#each $pinnedChats as chat, idx}
							<ChatItem
								className=""
								id={chat.id}
								title={chat.title}
								{shiftKey}
								selected={selectedChatId === chat.id}
								on:select={() => {
									selectedChatId = chat.id;
								}}
								on:unselect={() => {
									selectedChatId = null;
								}}
								on:change={async () => {
									initChatList();
								}}
								on:tag={(e) => {
									const { type, name } = e.detail;
									tagEventHandler(type, name, chat.id);
								}}
							/>
						{/each}
					</div>
				</Folder>
			</div>
		{/if}

		{#if $config?.features?.enable_channels && ($user.role === 'admin' || $channels.length > 0) && !search}
			<Folder
				className="px-2 mt-0.5"
				name={$i18n.t('Channels')}
				dragAndDrop={false}
				onAdd={$user.role === 'admin'
					? () => {
							showCreateChannel = true;
						}
					: null}
				onAddLabel={$i18n.t('Create Channel')}
			>
				{#each $channels as channel}
					<ChannelItem
						{channel}
						onUpdate={async () => {
							await initChannels();
						}}
					/>
				{/each}
			</Folder>
		{/if}

		{#if !search && folders}
			<Folders
				{folders}
				on:import={(e) => {
					const { folderId, items } = e.detail;
					importChatHandler(items, false, folderId);
				}}
				on:update={async (e) => {
					initChatList();
				}}
				on:change={async () => {
					initChatList();
				}}
			/>
		{/if}

		<Folder
			collapsible={!search}
			className="px-2 mt-0.5"
			name={$i18n.t('Chats')}
			on:import={(e) => {
				importChatHandler(e.detail);
			}}
			on:drop={async (e) => {
				const { type, id, item } = e.detail;

				if (type === 'chat') {
					let chat = await getChatById(id).catch((error) => {
						return null;
					});
					if (!chat && item) {
						chat = await importChat(item.chat, item?.meta ?? {});
					}

					if (chat) {
						console.log(chat);
						if (chat.folder_id) {
							const res = await updateChatFolderIdById(chat.id, null).catch((error) => {
								toast.error(error);
								return null;
							});
						}

						if (chat.pinned) {
							const res = await toggleChatPinnedStatusById(chat.id);
						}

						initChatList();
					}
				} else if (type === 'folder') {
					if (folders[id].parent_id === null) {
						return;
					}

					const res = await updateFolderParentIdById(id, null).catch((error) => {
						toast.error(error);
						return null;
					});

					if (res) {
						await initFolders();
					}
				}
			}}
		>
			{#if $temporaryChatEnabled}
				<div class="absolute z-40 w-full h-full flex justify-center"></div>
			{/if}

			<div class=" flex-1 flex flex-col overflow-y-auto scrollbar-hidden">
				<div class="pt-1.5">
					{#if $chats}
						{#each $chats as chat, idx}
							{#if idx === 0 || (idx > 0 && chat.time_range !== $chats[idx - 1].time_range)}
								<div
									class="sidebar-text w-full pl-2.5 text-xs dark:text-gray-500 font-medium {idx ===
									0
										? ''
										: 'pt-5'} pb-1.5"
								>
									{$i18n.t(chat.time_range)}
									<!-- localisation keys for time_range to be recognized from the i18next parser (so they don't get automatically removed):
							{$i18n.t('Today')}
							{$i18n.t('Yesterday')}
							{$i18n.t('Previous 7 days')}
							{$i18n.t('Previous 30 days')}
							{$i18n.t('January')}
							{$i18n.t('February')}
							{$i18n.t('March')}
							{$i18n.t('April')}
							{$i18n.t('May')}
							{$i18n.t('June')}
							{$i18n.t('July')}
							{$i18n.t('August')}
							{$i18n.t('September')}
							{$i18n.t('October')}
							{$i18n.t('November')}
							{$i18n.t('December')}
							-->
								</div>
							{/if}

							<ChatItem
								className=""
								id={chat.id}
								title={chat.title}
								{shiftKey}
								selected={selectedChatId === chat.id}
								on:select={() => {
									selectedChatId = chat.id;
								}}
								on:unselect={() => {
									selectedChatId = null;
								}}
								on:change={async () => {
									initChatList();
								}}
								on:tag={(e) => {
									const { type, name } = e.detail;
									tagEventHandler(type, name, chat.id);
								}}
							/>
						{/each}

						{#if $scrollPaginationEnabled && !allChatsLoaded}
							<Loader
								on:visible={(e) => {
									if (!chatListLoading) {
										loadMoreChats();
									}
								}}
							>
								<div
									class="w-full flex justify-center py-1 text-xs animate-pulse items-center gap-2"
								>
									<Spinner className=" size-4" />
									<div class=" ">Loading...</div>
								</div>
							</Loader>
						{/if}
					{:else}
						<div class="w-full flex justify-center py-1 text-xs animate-pulse items-center gap-2">
							<Spinner className=" size-4" />
							<div class=" ">Loading...</div>
						</div>
					{/if}
				</div>
			</div>
		</Folder>
	</div>

	<div class="px-2">
		<div class="flex flex-col">
			{#if $user !== undefined && $config?.features?.enable_sidebar_user_profile}
				<UserMenu
					role={$user.role}
					on:show={(e) => {
						if (e.detail === 'archived-chat') {
							showArchivedChats.set(true);
						}
					}}
				>
					<button
						class=" flex items-center rounded-xl py-2.5 px-2.5 w-full hover:bg-gray-100 dark:hover:bg-gray-900 transition"
						on:click={() => {
							showDropdown = !showDropdown;
						}}
					>
						<div class=" self-center mr-3">
							<img
								src={$user.profile_image_url}
								class=" max-w-[30px] object-cover rounded-full"
								alt="User profile"
							/>
						</div>
						<div class=" self-center font-medium">{$user.name}</div>
					</button>
				</UserMenu>
			{/if}
		</div>
	</div>
</div>

<style>
	.scrollbar-hidden:active::-webkit-scrollbar-thumb,
	.scrollbar-hidden:focus::-webkit-scrollbar-thumb,
	.scrollbar-hidden:hover::-webkit-scrollbar-thumb {
		visibility: visible;
	}
	.scrollbar-hidden::-webkit-scrollbar-thumb {
		visibility: hidden;
	}
</style>
