var Co = Object.defineProperty;
var Cn = (e) => {
	throw TypeError(e);
};
var So = (e, t, r) =>
	t in e ? Co(e, t, { enumerable: !0, configurable: !0, writable: !0, value: r }) : (e[t] = r);
var be = (e, t, r) => So(e, typeof t != 'symbol' ? t + '' : t, r),
	Sn = (e, t, r) => t.has(e) || Cn('Cannot ' + r);
var he = (e, t, r) => (Sn(e, t, 'read from private field'), r ? r.call(e) : t.get(e)),
	xr = (e, t, r) =>
		t.has(e)
			? Cn('Cannot add the same private member more than once')
			: t instanceof WeakSet
				? t.add(e)
				: t.set(e, r),
	Er = (e, t, r, n) => (Sn(e, t, 'write to private field'), n ? n.call(e, r) : t.set(e, r), r);
const Io = '5';
var Fn;
typeof window < 'u' &&
	((Fn = window.__svelte ?? (window.__svelte = {})).v ?? (Fn.v = /* @__PURE__ */ new Set())).add(
		Io
	);
const Vr = 1,
	Rr = 2,
	Yn = 4,
	No = 8,
	zo = 16,
	Lo = 1,
	Mo = 2,
	qn = 4,
	$o = 8,
	To = 16,
	Zn = 1,
	jo = 2,
	Br = '[',
	Fr = '[!',
	Yr = ']',
	_t = {},
	pe = Symbol(),
	Ao = 'http://www.w3.org/1999/xhtml',
	Uo = '@attach',
	In = !1;
var cr = Array.isArray,
	Oo = Array.prototype.indexOf,
	qr = Array.from,
	tr = Object.keys,
	Tt = Object.defineProperty,
	Ze = Object.getOwnPropertyDescriptor,
	Jn = Object.getOwnPropertyDescriptors,
	Do = Object.prototype,
	Ho = Array.prototype,
	Zr = Object.getPrototypeOf,
	Nn = Object.isExtensible;
function zt(e) {
	return typeof e == 'function';
}
const Ne = () => {};
function Po(e) {
	return e();
}
function jt(e) {
	for (var t = 0; t < e.length; t++) e[t]();
}
const ze = 2,
	Jr = 4,
	Bt = 8,
	Kr = 16,
	Qe = 32,
	dt = 64,
	Wr = 128,
	_e = 256,
	rr = 512,
	ye = 1024,
	Ge = 2048,
	lt = 4096,
	Je = 8192,
	Gr = 16384,
	Kn = 32768,
	Ft = 65536,
	zn = 1 << 17,
	Vo = 1 << 18,
	Wn = 1 << 19,
	zr = 1 << 20,
	Xr = 1 << 21,
	Re = Symbol('$state'),
	Qr = Symbol('legacy props'),
	Ro = Symbol(''),
	Gn = new (class extends Error {
		constructor() {
			super(...arguments);
			be(this, 'name', 'StaleReactionError');
			be(this, 'message', 'The reaction that called `getAbortSignal()` was re-run or destroyed');
		}
	})(),
	Xn = 3,
	At = 8;
function Qn(e) {
	return e === this.v;
}
function ei(e, t) {
	return e != e
		? t == t
		: e !== t || (e !== null && typeof e == 'object') || typeof e == 'function';
}
function ti(e) {
	return !ei(e, this.v);
}
function Bo(e) {
	throw new Error('https://svelte.dev/e/effect_in_teardown');
}
function Fo() {
	throw new Error('https://svelte.dev/e/effect_in_unowned_derived');
}
function Yo(e) {
	throw new Error('https://svelte.dev/e/effect_orphan');
}
function qo() {
	throw new Error('https://svelte.dev/e/effect_update_depth_exceeded');
}
function Zo() {
	throw new Error('https://svelte.dev/e/hydration_failed');
}
function Jo(e) {
	throw new Error('https://svelte.dev/e/props_invalid_value');
}
function Ko() {
	throw new Error('https://svelte.dev/e/state_descriptors_fixed');
}
function Wo() {
	throw new Error('https://svelte.dev/e/state_prototype_fixed');
}
function Go() {
	throw new Error('https://svelte.dev/e/state_unsafe_mutation');
}
let xt = !1,
	Xo = !1;
function Qo() {
	xt = !0;
}
function ea(e) {
	throw new Error('https://svelte.dev/e/lifecycle_outside_component');
}
let F = null;
function Ln(e) {
	F = e;
}
function J(e, t = !1, r) {
	var n = (F = {
		p: F,
		c: null,
		d: !1,
		e: null,
		m: !1,
		s: e,
		x: null,
		l: null
	});
	xt &&
		!t &&
		(F.l = {
			s: null,
			u: null,
			r1: [],
			r2: yt(!1)
		}),
		vr(() => {
			n.d = !0;
		});
}
function K(e) {
	const t = F;
	if (t !== null) {
		e !== void 0 && (t.x = e);
		const s = t.e;
		if (s !== null) {
			var r = H,
				n = O;
			t.e = null;
			try {
				for (var i = 0; i < s.length; i++) {
					var o = s[i];
					st(o.effect), Be(o.reaction), vi(o.fn);
				}
			} finally {
				st(r), Be(n);
			}
		}
		(F = t.p), (t.m = !0);
	}
	return e || /** @type {T} */ {};
}
function dr() {
	return !xt || (F !== null && F.l === null);
}
function wt(e) {
	if (typeof e != 'object' || e === null || Re in e) return e;
	const t = Zr(e);
	if (t !== Do && t !== Ho) return e;
	var r = /* @__PURE__ */ new Map(),
		n = cr(e),
		i = /* @__PURE__ */ fe(0),
		o = O,
		s = (a) => {
			var l = O;
			Be(o);
			var u = a();
			return Be(l), u;
		};
	return (
		n &&
			r.set(
				'length',
				/* @__PURE__ */ fe(
					/** @type {any[]} */
					e.length
				)
			),
		new Proxy(
			/** @type {any} */
			e,
			{
				defineProperty(a, l, u) {
					(!('value' in u) || u.configurable === !1 || u.enumerable === !1 || u.writable === !1) &&
						Ko();
					var f = r.get(l);
					return (
						f === void 0
							? (f = s(() => {
									var d = /* @__PURE__ */ fe(u.value);
									return r.set(l, d), d;
								}))
							: j(f, u.value, !0),
						!0
					);
				},
				deleteProperty(a, l) {
					var u = r.get(l);
					if (u === void 0) {
						if (l in a) {
							const c = s(() => /* @__PURE__ */ fe(pe));
							r.set(l, c), Cr(i);
						}
					} else {
						if (n && typeof l == 'string') {
							var f =
									/** @type {Source<number>} */
									r.get('length'),
								d = Number(l);
							Number.isInteger(d) && d < f.v && j(f, d);
						}
						j(u, pe), Cr(i);
					}
					return !0;
				},
				get(a, l, u) {
					var b;
					if (l === Re) return e;
					var f = r.get(l),
						d = l in a;
					if (
						(f === void 0 &&
							(!d || ((b = Ze(a, l)) != null && b.writable)) &&
							((f = s(() => {
								var p = wt(d ? a[l] : pe),
									k = /* @__PURE__ */ fe(p);
								return k;
							})),
							r.set(l, f)),
						f !== void 0)
					) {
						var c = w(f);
						return c === pe ? void 0 : c;
					}
					return Reflect.get(a, l, u);
				},
				getOwnPropertyDescriptor(a, l) {
					var u = Reflect.getOwnPropertyDescriptor(a, l);
					if (u && 'value' in u) {
						var f = r.get(l);
						f && (u.value = w(f));
					} else if (u === void 0) {
						var d = r.get(l),
							c = d == null ? void 0 : d.v;
						if (d !== void 0 && c !== pe)
							return {
								enumerable: !0,
								configurable: !0,
								value: c,
								writable: !0
							};
					}
					return u;
				},
				has(a, l) {
					var c;
					if (l === Re) return !0;
					var u = r.get(l),
						f = (u !== void 0 && u.v !== pe) || Reflect.has(a, l);
					if (u !== void 0 || (H !== null && (!f || ((c = Ze(a, l)) != null && c.writable)))) {
						u === void 0 &&
							((u = s(() => {
								var b = f ? wt(a[l]) : pe,
									p = /* @__PURE__ */ fe(b);
								return p;
							})),
							r.set(l, u));
						var d = w(u);
						if (d === pe) return !1;
					}
					return f;
				},
				set(a, l, u, f) {
					var m;
					var d = r.get(l),
						c = l in a;
					if (n && l === 'length')
						for (var b = u; b < /** @type {Source<number>} */ d.v; b += 1) {
							var p = r.get(b + '');
							p !== void 0
								? j(p, pe)
								: b in a && ((p = s(() => /* @__PURE__ */ fe(pe))), r.set(b + '', p));
						}
					if (d === void 0)
						(!c || ((m = Ze(a, l)) != null && m.writable)) &&
							((d = s(() => /* @__PURE__ */ fe(void 0))), j(d, wt(u)), r.set(l, d));
					else {
						c = d.v !== pe;
						var k = s(() => wt(u));
						j(d, k);
					}
					var S = Reflect.getOwnPropertyDescriptor(a, l);
					if ((S != null && S.set && S.set.call(f, u), !c)) {
						if (n && typeof l == 'string') {
							var I =
									/** @type {Source<number>} */
									r.get('length'),
								v = Number(l);
							Number.isInteger(v) && v >= I.v && j(I, v + 1);
						}
						Cr(i);
					}
					return !0;
				},
				ownKeys(a) {
					w(i);
					var l = Reflect.ownKeys(a).filter((d) => {
						var c = r.get(d);
						return c === void 0 || c.v !== pe;
					});
					for (var [u, f] of r) f.v !== pe && !(u in a) && l.push(u);
					return l;
				},
				setPrototypeOf() {
					Wo();
				}
			}
		)
	);
}
function Cr(e, t = 1) {
	j(e, e.v + t);
}
function Mn(e) {
	try {
		if (e !== null && typeof e == 'object' && Re in e) return e[Re];
	} catch {}
	return e;
}
function ta(e, t) {
	return Object.is(Mn(e), Mn(t));
}
// @__NO_SIDE_EFFECTS__
function Et(e) {
	var t = ze | Ge,
		r = O !== null && (O.f & ze) !== 0 ? /** @type {Derived} */ O : null;
	return (
		H === null || (r !== null && (r.f & _e) !== 0) ? (t |= _e) : (H.f |= Wn),
		{
			ctx: F,
			deps: null,
			effects: null,
			equals: Qn,
			f: t,
			fn: e,
			reactions: null,
			rv: 0,
			v:
				/** @type {V} */
				null,
			wv: 0,
			parent: r ?? H,
			ac: null
		}
	);
}
// @__NO_SIDE_EFFECTS__
function te(e) {
	const t = /* @__PURE__ */ Et(e);
	return xi(t), t;
}
// @__NO_SIDE_EFFECTS__
function ri(e) {
	const t = /* @__PURE__ */ Et(e);
	return (t.equals = ti), t;
}
function ni(e) {
	var t = e.effects;
	if (t !== null) {
		e.effects = null;
		for (var r = 0; r < t.length; r += 1)
			ke(
				/** @type {Effect} */
				t[r]
			);
	}
}
function ra(e) {
	for (var t = e.parent; t !== null; ) {
		if ((t.f & ze) === 0)
			return (
				/** @type {Effect} */
				t
			);
		t = t.parent;
	}
	return null;
}
function ii(e) {
	var t,
		r = H;
	st(ra(e));
	try {
		ni(e), (t = Si(e));
	} finally {
		st(r);
	}
	return t;
}
function oi(e) {
	var t = ii(e);
	if ((e.equals(t) || ((e.v = t), (e.wv = Ei())), !St)) {
		var r = (at || (e.f & _e) !== 0) && e.deps !== null ? lt : ye;
		Oe(e, r);
	}
}
const Ut = /* @__PURE__ */ new Map();
function yt(e, t) {
	var r = {
		f: 0,
		// TODO ideally we could skip this altogether, but it causes type errors
		v: e,
		reactions: null,
		equals: Qn,
		rv: 0,
		wv: 0
	};
	return r;
}
// @__NO_SIDE_EFFECTS__
function fe(e, t) {
	const r = yt(e);
	return xi(r), r;
}
// @__NO_SIDE_EFFECTS__
function ot(e, t = !1, r = !0) {
	var i;
	const n = yt(e);
	return (
		t || (n.equals = ti),
		xt && r && F !== null && F.l !== null && ((i = F.l).s ?? (i.s = [])).push(n),
		n
	);
}
function na(e, t) {
	return (
		j(
			e,
			Ae(() => w(e))
		),
		t
	);
}
function j(e, t, r = !1) {
	O !== null && // since we are untracking the function inside `$inspect.with` we need to add this check
		// to ensure we error if state is set inside an inspect effect
		(!Pe || (O.f & zn) !== 0) &&
		dr() &&
		(O.f & (ze | Kr | zn)) !== 0 &&
		!((re == null ? void 0 : re.reaction) === O && re.sources.includes(e)) &&
		Go();
	let n = r ? wt(t) : t;
	return Lr(e, n);
}
function Lr(e, t) {
	if (!e.equals(t)) {
		var r = e.v;
		St ? Ut.set(e, t) : Ut.set(e, r),
			(e.v = t),
			(e.f & ze) !== 0 &&
				((e.f & Ge) !== 0 &&
					ii(
						/** @type {Derived} */
						e
					),
				Oe(e, (e.f & _e) === 0 ? ye : lt)),
			(e.wv = Ei()),
			ai(e, Ge),
			dr() &&
				H !== null &&
				(H.f & ye) !== 0 &&
				(H.f & (Qe | dt)) === 0 &&
				(Ee === null ? ha([e]) : Ee.push(e));
	}
	return t;
}
function $n(e, t = 1) {
	var r = w(e),
		n = t === 1 ? r++ : r--;
	return j(e, r), n;
}
function ai(e, t) {
	var r = e.reactions;
	if (r !== null)
		for (var n = dr(), i = r.length, o = 0; o < i; o++) {
			var s = r[o],
				a = s.f;
			(a & Ge) === 0 &&
				((!n && s === H) ||
					(Oe(s, t),
					(a & (ye | _e)) !== 0 &&
						((a & ze) !== 0
							? ai(
									/** @type {Derived} */
									s,
									lt
								)
							: br(
									/** @type {Effect} */
									s
								))));
		}
}
function fr(e) {
	console.warn('https://svelte.dev/e/hydration_mismatch');
}
function ia() {
	console.warn('https://svelte.dev/e/select_multiple_invalid_value');
}
let U = !1;
function je(e) {
	U = e;
}
let D;
function Ue(e) {
	if (e === null) throw (fr(), _t);
	return (D = e);
}
function Ct() {
	return Ue(
		/** @type {TemplateNode} */
		/* @__PURE__ */ et(D)
	);
}
function E(e) {
	if (U) {
		if (/* @__PURE__ */ et(D) !== null) throw (fr(), _t);
		D = e;
	}
}
function Ve(e = 1) {
	if (U) {
		for (var t = e, r = D; t--; ) r = /** @type {TemplateNode} */ /* @__PURE__ */ et(r);
		D = r;
	}
}
function Mr() {
	for (var e = 0, t = D; ; ) {
		if (t.nodeType === At) {
			var r =
				/** @type {Comment} */
				t.data;
			if (r === Yr) {
				if (e === 0) return t;
				e -= 1;
			} else (r === Br || r === Fr) && (e += 1);
		}
		var n =
			/** @type {TemplateNode} */
			/* @__PURE__ */ et(t);
		t.remove(), (t = n);
	}
}
function si(e) {
	if (!e || e.nodeType !== At) throw (fr(), _t);
	return (
		/** @type {Comment} */
		e.data
	);
}
var Tn, li, ui, ci;
function $r() {
	if (Tn === void 0) {
		(Tn = window), (li = /Firefox/.test(navigator.userAgent));
		var e = Element.prototype,
			t = Node.prototype,
			r = Text.prototype;
		(ui = Ze(t, 'firstChild').get),
			(ci = Ze(t, 'nextSibling').get),
			Nn(e) &&
				((e.__click = void 0),
				(e.__className = void 0),
				(e.__attributes = null),
				(e.__style = void 0),
				(e.__e = void 0)),
			Nn(r) && (r.__t = void 0);
	}
}
function kt(e = '') {
	return document.createTextNode(e);
}
// @__NO_SIDE_EFFECTS__
function Se(e) {
	return ui.call(e);
}
// @__NO_SIDE_EFFECTS__
function et(e) {
	return ci.call(e);
}
function C(e, t) {
	if (!U) return /* @__PURE__ */ Se(e);
	var r =
		/** @type {TemplateNode} */
		/* @__PURE__ */ Se(D);
	if (r === null) r = D.appendChild(kt());
	else if (t && r.nodeType !== Xn) {
		var n = kt();
		return r == null || r.before(n), Ue(n), n;
	}
	return Ue(r), r;
}
function Ke(e, t) {
	if (!U) {
		var r =
			/** @type {DocumentFragment} */
			/* @__PURE__ */ Se(
				/** @type {Node} */
				e
			);
		return r instanceof Comment && r.data === '' ? /* @__PURE__ */ et(r) : r;
	}
	return D;
}
function ie(e, t = 1, r = !1) {
	let n = U ? D : e;
	for (var i; t--; ) (i = n), (n = /** @type {TemplateNode} */ /* @__PURE__ */ et(n));
	if (!U) return n;
	if (r && (n == null ? void 0 : n.nodeType) !== Xn) {
		var o = kt();
		return n === null ? i == null || i.after(o) : n.before(o), Ue(o), o;
	}
	return Ue(n), /** @type {TemplateNode} */ n;
}
function di(e) {
	e.textContent = '';
}
function fi(e) {
	H === null && O === null && Yo(),
		O !== null && (O.f & _e) !== 0 && H === null && Fo(),
		St && Bo();
}
function oa(e, t) {
	var r = t.last;
	r === null ? (t.last = t.first = e) : ((r.next = e), (e.prev = r), (t.last = e));
}
function tt(e, t, r, n = !0) {
	var i = H,
		o = {
			ctx: F,
			deps: null,
			nodes_start: null,
			nodes_end: null,
			f: e | Ge,
			first: null,
			fn: t,
			last: null,
			next: null,
			parent: i,
			b: i && i.b,
			prev: null,
			teardown: null,
			transitions: null,
			wv: 0,
			ac: null
		};
	if (r)
		try {
			pr(o), (o.f |= Kn);
		} catch (l) {
			throw (ke(o), l);
		}
	else t !== null && br(o);
	var s =
		r &&
		o.deps === null &&
		o.first === null &&
		o.nodes_start === null &&
		o.teardown === null &&
		(o.f & (Wn | Wr)) === 0;
	if (!s && n && (i !== null && oa(o, i), O !== null && (O.f & ze) !== 0)) {
		var a =
			/** @type {Derived} */
			O;
		(a.effects ?? (a.effects = [])).push(o);
	}
	return o;
}
function vr(e) {
	const t = tt(Bt, null, !1);
	return Oe(t, ye), (t.teardown = e), t;
}
function Tr(e) {
	fi();
	var t = H !== null && (H.f & Qe) !== 0 && F !== null && !F.m;
	if (t) {
		var r =
			/** @type {ComponentContext} */
			F;
		(r.e ?? (r.e = [])).push({
			fn: e,
			effect: H,
			reaction: O
		});
	} else return vi(e);
}
function vi(e) {
	return tt(Jr | Xr, e, !1);
}
function aa(e) {
	return fi(), tt(Bt | Xr, e, !0);
}
function sa(e) {
	const t = tt(dt, e, !0);
	return () => {
		ke(t);
	};
}
function la(e) {
	const t = tt(dt, e, !0);
	return (r = {}) =>
		new Promise((n) => {
			r.outro
				? Ot(t, () => {
						ke(t), n(void 0);
					})
				: (ke(t), n(void 0));
		});
}
function en(e) {
	return tt(Jr, e, !1);
}
function hi(e, t) {
	var r =
			/** @type {ComponentContextLegacy} */
			F,
		n = { effect: null, ran: !1 };
	r.l.r1.push(n),
		(n.effect = hr(() => {
			e(), !n.ran && ((n.ran = !0), j(r.l.r2, !0), Ae(t));
		}));
}
function pi() {
	var e =
		/** @type {ComponentContextLegacy} */
		F;
	hr(() => {
		if (w(e.l.r2)) {
			for (var t of e.l.r1) {
				var r = t.effect;
				(r.f & ye) !== 0 && Oe(r, lt), qt(r) && pr(r), (t.ran = !1);
			}
			e.l.r2.v = !1;
		}
	});
}
function hr(e) {
	return tt(Bt, e, !0);
}
function we(e, t = [], r = Et) {
	const n = t.map(r);
	return ft(() => e(...n.map(w)));
}
function ft(e, t = 0) {
	var r = tt(Bt | Kr | t, e, !0);
	return r;
}
function Xe(e, t = !0) {
	return tt(Bt | Qe, e, !0, t);
}
function bi(e) {
	var t = e.teardown;
	if (t !== null) {
		const r = St,
			n = O;
		jn(!0), Be(null);
		try {
			t.call(null);
		} finally {
			jn(r), Be(n);
		}
	}
}
function gi(e, t = !1) {
	var i;
	var r = e.first;
	for (e.first = e.last = null; r !== null; ) {
		(i = r.ac) == null || i.abort(Gn);
		var n = r.next;
		(r.f & dt) !== 0 ? (r.parent = null) : ke(r, t), (r = n);
	}
}
function ua(e) {
	for (var t = e.first; t !== null; ) {
		var r = t.next;
		(t.f & Qe) === 0 && ke(t), (t = r);
	}
}
function ke(e, t = !0) {
	var r = !1;
	(t || (e.f & Vo) !== 0) &&
		e.nodes_start !== null &&
		e.nodes_end !== null &&
		(ca(
			e.nodes_start,
			/** @type {TemplateNode} */
			e.nodes_end
		),
		(r = !0)),
		gi(e, t && !r),
		ar(e, 0),
		Oe(e, Gr);
	var n = e.transitions;
	if (n !== null) for (const o of n) o.stop();
	bi(e);
	var i = e.parent;
	i !== null && i.first !== null && mi(e),
		(e.next =
			e.prev =
			e.teardown =
			e.ctx =
			e.deps =
			e.fn =
			e.nodes_start =
			e.nodes_end =
			e.ac =
				null);
}
function ca(e, t) {
	for (; e !== null; ) {
		var r = e === t ? null : /** @type {TemplateNode} */ /* @__PURE__ */ et(e);
		e.remove(), (e = r);
	}
}
function mi(e) {
	var t = e.parent,
		r = e.prev,
		n = e.next;
	r !== null && (r.next = n),
		n !== null && (n.prev = r),
		t !== null && (t.first === e && (t.first = n), t.last === e && (t.last = r));
}
function Ot(e, t) {
	var r = [];
	tn(e, r, !0),
		wi(r, () => {
			ke(e), t && t();
		});
}
function wi(e, t) {
	var r = e.length;
	if (r > 0) {
		var n = () => --r || t();
		for (var i of e) i.out(n);
	} else t();
}
function tn(e, t, r) {
	if ((e.f & Je) === 0) {
		if (((e.f ^= Je), e.transitions !== null))
			for (const s of e.transitions) (s.is_global || r) && t.push(s);
		for (var n = e.first; n !== null; ) {
			var i = n.next,
				o = (n.f & Ft) !== 0 || (n.f & Qe) !== 0;
			tn(n, t, o ? r : !1), (n = i);
		}
	}
}
function nr(e) {
	_i(e, !0);
}
function _i(e, t) {
	if ((e.f & Je) !== 0) {
		e.f ^= Je;
		for (var r = e.first; r !== null; ) {
			var n = r.next,
				i = (r.f & Ft) !== 0 || (r.f & Qe) !== 0;
			_i(r, i ? t : !1), (r = n);
		}
		if (e.transitions !== null) for (const o of e.transitions) (o.is_global || t) && o.in();
	}
}
let Dt = [],
	jr = [];
function yi() {
	var e = Dt;
	(Dt = []), jt(e);
}
function da() {
	var e = jr;
	(jr = []), jt(e);
}
function Yt(e) {
	Dt.length === 0 && queueMicrotask(yi), Dt.push(e);
}
function fa() {
	Dt.length > 0 && yi(), jr.length > 0 && da();
}
function va(e) {
	var t =
		/** @type {Effect} */
		H;
	if ((t.f & Kn) === 0) {
		if ((t.f & Wr) === 0) throw e;
		t.fn(e);
	} else ki(e, t);
}
function ki(e, t) {
	for (; t !== null; ) {
		if ((t.f & Wr) !== 0)
			try {
				t.b.error(e);
				return;
			} catch {}
		t = t.parent;
	}
	throw e;
}
let Ht = !1,
	Pt = null,
	ut = !1,
	St = !1;
function jn(e) {
	St = e;
}
let $t = [];
let O = null,
	Pe = !1;
function Be(e) {
	O = e;
}
let H = null;
function st(e) {
	H = e;
}
let re = null;
function xi(e) {
	O !== null &&
		O.f & zr &&
		(re === null ? (re = { reaction: O, sources: [e] }) : re.sources.push(e));
}
let ve = null,
	ge = 0,
	Ee = null;
function ha(e) {
	Ee = e;
}
let ir = 1,
	or = 0,
	at = !1;
function Ei() {
	return ++ir;
}
function qt(e) {
	var d;
	var t = e.f;
	if ((t & Ge) !== 0) return !0;
	if ((t & lt) !== 0) {
		var r = e.deps,
			n = (t & _e) !== 0;
		if (r !== null) {
			var i,
				o,
				s = (t & rr) !== 0,
				a = n && H !== null && !at,
				l = r.length;
			if (s || a) {
				var u =
						/** @type {Derived} */
						e,
					f = u.parent;
				for (i = 0; i < l; i++)
					(o = r[i]),
						(s || !((d = o == null ? void 0 : o.reactions) != null && d.includes(u))) &&
							(o.reactions ?? (o.reactions = [])).push(u);
				s && (u.f ^= rr), a && f !== null && (f.f & _e) === 0 && (u.f ^= _e);
			}
			for (i = 0; i < l; i++)
				if (
					((o = r[i]),
					qt(
						/** @type {Derived} */
						o
					) &&
						oi(
							/** @type {Derived} */
							o
						),
					o.wv > e.wv)
				)
					return !0;
		}
		(!n || (H !== null && !at)) && Oe(e, ye);
	}
	return !1;
}
function Ci(e, t, r = !0) {
	var n = e.reactions;
	if (n !== null)
		for (var i = 0; i < n.length; i++) {
			var o = n[i];
			((re == null ? void 0 : re.reaction) === O && re.sources.includes(e)) ||
				((o.f & ze) !== 0
					? Ci(
							/** @type {Derived} */
							o,
							t,
							!1
						)
					: t === o &&
						(r ? Oe(o, Ge) : (o.f & ye) !== 0 && Oe(o, lt),
						br(
							/** @type {Effect} */
							o
						)));
		}
}
function Si(e) {
	var b;
	var t = ve,
		r = ge,
		n = Ee,
		i = O,
		o = at,
		s = re,
		a = F,
		l = Pe,
		u = e.f;
	(ve = /** @type {null | Value[]} */ null),
		(ge = 0),
		(Ee = null),
		(at = (u & _e) !== 0 && (Pe || !ut || O === null)),
		(O = (u & (Qe | dt)) === 0 ? e : null),
		(re = null),
		Ln(e.ctx),
		(Pe = !1),
		or++,
		(e.f |= zr),
		e.ac !== null && (e.ac.abort(Gn), (e.ac = null));
	try {
		var f =
				/** @type {Function} */
				(0, e.fn)(),
			d = e.deps;
		if (ve !== null) {
			var c;
			if ((ar(e, ge), d !== null && ge > 0))
				for (d.length = ge + ve.length, c = 0; c < ve.length; c++) d[ge + c] = ve[c];
			else e.deps = d = ve;
			if (
				!at || // Deriveds that already have reactions can cleanup, so we still add them as reactions
				((u & ze) !== 0 && /** @type {import('#client').Derived} */ e.reactions !== null)
			)
				for (c = ge; c < d.length; c++) ((b = d[c]).reactions ?? (b.reactions = [])).push(e);
		} else d !== null && ge < d.length && (ar(e, ge), (d.length = ge));
		if (dr() && Ee !== null && !Pe && d !== null && (e.f & (ze | lt | Ge)) === 0)
			for (c = 0; c < /** @type {Source[]} */ Ee.length; c++)
				Ci(
					Ee[c],
					/** @type {Effect} */
					e
				);
		return (
			i !== null &&
				i !== e &&
				(or++, Ee !== null && (n === null ? (n = Ee) : n.push(.../** @type {Source[]} */ Ee))),
			f
		);
	} catch (p) {
		va(p);
	} finally {
		(ve = t), (ge = r), (Ee = n), (O = i), (at = o), (re = s), Ln(a), (Pe = l), (e.f ^= zr);
	}
}
function pa(e, t) {
	let r = t.reactions;
	if (r !== null) {
		var n = Oo.call(r, e);
		if (n !== -1) {
			var i = r.length - 1;
			i === 0 ? (r = t.reactions = null) : ((r[n] = r[i]), r.pop());
		}
	}
	r === null &&
		(t.f & ze) !== 0 && // Destroying a child effect while updating a parent effect can cause a dependency to appear
		// to be unused, when in fact it is used by the currently-updating parent. Checking `new_deps`
		// allows us to skip the expensive work of disconnecting and immediately reconnecting it
		(ve === null || !ve.includes(t)) &&
		(Oe(t, lt),
		(t.f & (_e | rr)) === 0 && (t.f ^= rr),
		ni(
			/** @type {Derived} **/
			t
		),
		ar(
			/** @type {Derived} **/
			t,
			0
		));
}
function ar(e, t) {
	var r = e.deps;
	if (r !== null) for (var n = t; n < r.length; n++) pa(e, r[n]);
}
function pr(e) {
	var t = e.f;
	if ((t & Gr) === 0) {
		Oe(e, ye);
		var r = H,
			n = ut;
		(H = e), (ut = !0);
		try {
			(t & Kr) !== 0 ? ua(e) : gi(e), bi(e);
			var i = Si(e);
			(e.teardown = typeof i == 'function' ? i : null), (e.wv = ir);
			var o;
			In && Xo && (e.f & Ge) !== 0 && e.deps;
		} finally {
			(ut = n), (H = r);
		}
	}
}
function ba() {
	try {
		qo();
	} catch (e) {
		if (Pt !== null) ki(e, Pt);
		else throw e;
	}
}
function Ii() {
	var e = ut;
	try {
		var t = 0;
		for (ut = !0; $t.length > 0; ) {
			t++ > 1e3 && ba();
			var r = $t,
				n = r.length;
			$t = [];
			for (var i = 0; i < n; i++) {
				var o = ma(r[i]);
				ga(o);
			}
			Ut.clear();
		}
	} finally {
		(Ht = !1), (ut = e), (Pt = null);
	}
}
function ga(e) {
	var t = e.length;
	if (t !== 0) {
		for (var r = 0; r < t; r++) {
			var n = e[r];
			if ((n.f & (Gr | Je)) === 0 && qt(n)) {
				var i = ir;
				if (
					(pr(n),
					n.deps === null &&
						n.first === null &&
						n.nodes_start === null &&
						(n.teardown === null ? mi(n) : (n.fn = null)),
					ir > i && (n.f & Xr) !== 0)
				)
					break;
			}
		}
		for (; r < t; r += 1) br(e[r]);
	}
}
function br(e) {
	Ht || ((Ht = !0), queueMicrotask(Ii));
	for (var t = (Pt = e); t.parent !== null; ) {
		t = t.parent;
		var r = t.f;
		if ((r & (dt | Qe)) !== 0) {
			if ((r & ye) === 0) return;
			t.f ^= ye;
		}
	}
	$t.push(t);
}
function ma(e) {
	for (var t = [], r = e; r !== null; ) {
		var n = r.f,
			i = (n & (Qe | dt)) !== 0,
			o = i && (n & ye) !== 0;
		if (!o && (n & Je) === 0) {
			(n & Jr) !== 0 ? t.push(r) : i ? (r.f ^= ye) : qt(r) && pr(r);
			var s = r.first;
			if (s !== null) {
				r = s;
				continue;
			}
		}
		var a = r.parent;
		for (r = r.next; r === null && a !== null; ) (r = a.next), (a = a.parent);
	}
	return t;
}
function _(e) {
	for (var t; ; ) {
		if ((fa(), $t.length === 0)) return (Ht = !1), (Pt = null), /** @type {T} */ t;
		(Ht = !0), Ii();
	}
}
function w(e) {
	var t = e.f,
		r = (t & ze) !== 0;
	if (O !== null && !Pe) {
		if ((re == null ? void 0 : re.reaction) !== O || !(re != null && re.sources.includes(e))) {
			var n = O.deps;
			e.rv < or &&
				((e.rv = or),
				ve === null && n !== null && n[ge] === e
					? ge++
					: ve === null
						? (ve = [e])
						: (!at || !ve.includes(e)) && ve.push(e));
		}
	} else if (
		r &&
		/** @type {Derived} */
		e.deps === null &&
		/** @type {Derived} */
		e.effects === null
	) {
		var i =
				/** @type {Derived} */
				e,
			o = i.parent;
		o !== null && (o.f & _e) === 0 && (i.f ^= _e);
	}
	return r && ((i = /** @type {Derived} */ e), qt(i) && oi(i)), St && Ut.has(e) ? Ut.get(e) : e.v;
}
function Ae(e) {
	var t = Pe;
	try {
		return (Pe = !0), e();
	} finally {
		Pe = t;
	}
}
const wa = -7169;
function Oe(e, t) {
	e.f = (e.f & wa) | t;
}
function rn(e) {
	if (!(typeof e != 'object' || !e || e instanceof EventTarget)) {
		if (Re in e) Ar(e);
		else if (!Array.isArray(e))
			for (let t in e) {
				const r = e[t];
				typeof r == 'object' && r && Re in r && Ar(r);
			}
	}
}
function Ar(e, t = /* @__PURE__ */ new Set()) {
	if (
		typeof e == 'object' &&
		e !== null && // We don't want to traverse DOM elements
		!(e instanceof EventTarget) &&
		!t.has(e)
	) {
		t.add(e), e instanceof Date && e.getTime();
		for (let n in e)
			try {
				Ar(e[n], t);
			} catch {}
		const r = Zr(e);
		if (
			r !== Object.prototype &&
			r !== Array.prototype &&
			r !== Map.prototype &&
			r !== Set.prototype &&
			r !== Date.prototype
		) {
			const n = Jn(r);
			for (let i in n) {
				const o = n[i].get;
				if (o)
					try {
						o.call(e);
					} catch {}
			}
		}
	}
}
function _a(e, t) {
	if (t) {
		const r = document.body;
		(e.autofocus = !0),
			Yt(() => {
				document.activeElement === r && e.focus();
			});
	}
}
function ya(e) {
	var t = O,
		r = H;
	Be(null), st(null);
	try {
		return e();
	} finally {
		Be(t), st(r);
	}
}
const Ni = /* @__PURE__ */ new Set(),
	Ur = /* @__PURE__ */ new Set();
function zi(e, t, r, n = {}) {
	function i(o) {
		if ((n.capture || Mt.call(t, o), !o.cancelBubble))
			return ya(() => (r == null ? void 0 : r.call(this, o)));
	}
	return (
		e.startsWith('pointer') || e.startsWith('touch') || e === 'wheel'
			? Yt(() => {
					t.addEventListener(e, i, n);
				})
			: t.addEventListener(e, i, n),
		i
	);
}
function ee(e, t, r, n, i) {
	var o = { capture: n, passive: i },
		s = zi(e, t, r, o);
	(t === document.body || // @ts-ignore
		t === window || // @ts-ignore
		t === document || // Firefox has quirky behavior, it can happen that we still get "canplay" events when the element is already removed
		t instanceof HTMLMediaElement) &&
		vr(() => {
			t.removeEventListener(e, s, o);
		});
}
function Zt(e) {
	for (var t = 0; t < e.length; t++) Ni.add(e[t]);
	for (var r of Ur) r(e);
}
function Mt(e) {
	var v;
	var t = this,
		r =
			/** @type {Node} */
			t.ownerDocument,
		n = e.type,
		i = ((v = e.composedPath) == null ? void 0 : v.call(e)) || [],
		o =
			/** @type {null | Element} */
			i[0] || e.target,
		s = 0,
		a = e.__root;
	if (a) {
		var l = i.indexOf(a);
		if (l !== -1 && (t === document || t === /** @type {any} */ window)) {
			e.__root = t;
			return;
		}
		var u = i.indexOf(t);
		if (u === -1) return;
		l <= u && (s = l);
	}
	if (((o = /** @type {Element} */ i[s] || e.target), o !== t)) {
		Tt(e, 'currentTarget', {
			configurable: !0,
			get() {
				return o || r;
			}
		});
		var f = O,
			d = H;
		Be(null), st(null);
		try {
			for (var c, b = []; o !== null; ) {
				var p = o.assignedSlot || o.parentNode || /** @type {any} */ o.host || null;
				try {
					var k = o['__' + n];
					if (
						k != null &&
						(!(/** @type {any} */ o.disabled) || // DOM could've been updated already by the time this is reached, so we check this as well
							// -> the target could not have been disabled because it emits the event in the first place
							e.target === o)
					)
						if (cr(k)) {
							var [S, ...I] = k;
							S.apply(o, [e, ...I]);
						} else k.call(o, e);
				} catch (m) {
					c ? b.push(m) : (c = m);
				}
				if (e.cancelBubble || p === t || p === null) break;
				o = p;
			}
			if (c) {
				for (let m of b)
					queueMicrotask(() => {
						throw m;
					});
				throw c;
			}
		} finally {
			(e.__root = t), delete e.currentTarget, Be(f), st(d);
		}
	}
}
function Li(e) {
	var t = document.createElement('template');
	return (t.innerHTML = e.replaceAll('<!>', '<!---->')), t.content;
}
function We(e, t) {
	var r =
		/** @type {Effect} */
		H;
	r.nodes_start === null && ((r.nodes_start = e), (r.nodes_end = t));
}
// @__NO_SIDE_EFFECTS__
function ae(e, t) {
	var r = (t & Zn) !== 0,
		n = (t & jo) !== 0,
		i,
		o = !e.startsWith('<!>');
	return () => {
		if (U) return We(D, null), D;
		i === void 0 &&
			((i = Li(o ? e : '<!>' + e)), r || (i = /** @type {Node} */ /* @__PURE__ */ Se(i)));
		var s =
			/** @type {TemplateNode} */
			n || li ? document.importNode(i, !0) : i.cloneNode(!0);
		if (r) {
			var a =
					/** @type {TemplateNode} */
					/* @__PURE__ */ Se(s),
				l =
					/** @type {TemplateNode} */
					s.lastChild;
			We(a, l);
		} else We(s, s);
		return s;
	};
}
// @__NO_SIDE_EFFECTS__
function ka(e, t, r = 'svg') {
	var n = !e.startsWith('<!>'),
		i = (t & Zn) !== 0,
		o = `<${r}>${n ? e : '<!>' + e}</${r}>`,
		s;
	return () => {
		if (U) return We(D, null), D;
		if (!s) {
			var a =
					/** @type {DocumentFragment} */
					Li(o),
				l =
					/** @type {Element} */
					/* @__PURE__ */ Se(a);
			if (i)
				for (s = document.createDocumentFragment(); /* @__PURE__ */ Se(l); )
					s.appendChild(
						/** @type {Node} */
						/* @__PURE__ */ Se(l)
					);
			else s = /** @type {Element} */ /* @__PURE__ */ Se(l);
		}
		var u =
			/** @type {TemplateNode} */
			s.cloneNode(!0);
		if (i) {
			var f =
					/** @type {TemplateNode} */
					/* @__PURE__ */ Se(u),
				d =
					/** @type {TemplateNode} */
					u.lastChild;
			We(f, d);
		} else We(u, u);
		return u;
	};
}
// @__NO_SIDE_EFFECTS__
function W(e, t) {
	return /* @__PURE__ */ ka(e, t, 'svg');
}
function It() {
	if (U) return We(D, null), D;
	var e = document.createDocumentFragment(),
		t = document.createComment(''),
		r = kt();
	return e.append(t, r), We(t, r), e;
}
function L(e, t) {
	if (U) {
		(H.nodes_end = D), Ct();
		return;
	}
	e !== null &&
		e.before(
			/** @type {Node} */
			t
		);
}
function xa(e) {
	return e.endsWith('capture') && e !== 'gotpointercapture' && e !== 'lostpointercapture';
}
const Ea = [
	'beforeinput',
	'click',
	'change',
	'dblclick',
	'contextmenu',
	'focusin',
	'focusout',
	'input',
	'keydown',
	'keyup',
	'mousedown',
	'mousemove',
	'mouseout',
	'mouseover',
	'mouseup',
	'pointerdown',
	'pointermove',
	'pointerout',
	'pointerover',
	'pointerup',
	'touchend',
	'touchmove',
	'touchstart'
];
function Ca(e) {
	return Ea.includes(e);
}
const Sa = {
	// no `class: 'className'` because we handle that separately
	formnovalidate: 'formNoValidate',
	ismap: 'isMap',
	nomodule: 'noModule',
	playsinline: 'playsInline',
	readonly: 'readOnly',
	defaultvalue: 'defaultValue',
	defaultchecked: 'defaultChecked',
	srcobject: 'srcObject',
	novalidate: 'noValidate',
	allowfullscreen: 'allowFullscreen',
	disablepictureinpicture: 'disablePictureInPicture',
	disableremoteplayback: 'disableRemotePlayback'
};
function Ia(e) {
	return (e = e.toLowerCase()), Sa[e] ?? e;
}
const Na = ['touchstart', 'touchmove'];
function za(e) {
	return Na.includes(e);
}
function Vt(e, t) {
	var r = t == null ? '' : typeof t == 'object' ? t + '' : t;
	r !== (e.__t ?? (e.__t = e.nodeValue)) && ((e.__t = r), (e.nodeValue = r + ''));
}
function Mi(e, t) {
	return $i(e, t);
}
function La(e, t) {
	$r(), (t.intro = t.intro ?? !1);
	const r = t.target,
		n = U,
		i = D;
	try {
		for (
			var o =
				/** @type {TemplateNode} */
				/* @__PURE__ */ Se(r);
			o && (o.nodeType !== At || /** @type {Comment} */ o.data !== Br);

		)
			o = /** @type {TemplateNode} */ /* @__PURE__ */ et(o);
		if (!o) throw _t;
		je(!0),
			Ue(
				/** @type {Comment} */
				o
			),
			Ct();
		const s = $i(e, { ...t, anchor: o });
		if (D === null || D.nodeType !== At || /** @type {Comment} */ D.data !== Yr) throw (fr(), _t);
		return je(!1), /**  @type {Exports} */ s;
	} catch (s) {
		if (s === _t) return t.recover === !1 && Zo(), $r(), di(r), je(!1), Mi(e, t);
		throw s;
	} finally {
		je(n), Ue(i);
	}
}
const bt = /* @__PURE__ */ new Map();
function $i(e, { target: t, anchor: r, props: n = {}, events: i, context: o, intro: s = !0 }) {
	$r();
	var a = /* @__PURE__ */ new Set(),
		l = (d) => {
			for (var c = 0; c < d.length; c++) {
				var b = d[c];
				if (!a.has(b)) {
					a.add(b);
					var p = za(b);
					t.addEventListener(b, Mt, { passive: p });
					var k = bt.get(b);
					k === void 0
						? (document.addEventListener(b, Mt, { passive: p }), bt.set(b, 1))
						: bt.set(b, k + 1);
				}
			}
		};
	l(qr(Ni)), Ur.add(l);
	var u = void 0,
		f = la(() => {
			var d = r ?? t.appendChild(kt());
			return (
				Xe(() => {
					if (o) {
						J({});
						var c =
							/** @type {ComponentContext} */
							F;
						c.c = o;
					}
					i && (n.$$events = i),
						U &&
							We(
								/** @type {TemplateNode} */
								d,
								null
							),
						(u = e(d, n) || {}),
						U && (H.nodes_end = D),
						o && K();
				}),
				() => {
					var p;
					for (var c of a) {
						t.removeEventListener(c, Mt);
						var b =
							/** @type {number} */
							bt.get(c);
						--b === 0 ? (document.removeEventListener(c, Mt), bt.delete(c)) : bt.set(c, b);
					}
					Ur.delete(l), d !== r && ((p = d.parentNode) == null || p.removeChild(d));
				}
			);
		});
	return Or.set(u, f), u;
}
let Or = /* @__PURE__ */ new WeakMap();
function Ma(e, t) {
	const r = Or.get(e);
	return r ? (Or.delete(e), r(t)) : Promise.resolve();
}
function Dr(e, t, ...r) {
	var n = e,
		i = Ne,
		o;
	ft(() => {
		i !== (i = t()) &&
			(o && (ke(o), (o = null)),
			(o = Xe(() =>
				/** @type {SnippetFn} */
				i(n, ...r)
			)));
	}, Ft),
		U && (n = D);
}
function Jt(e) {
	F === null && ea(),
		xt && F.l !== null
			? $a(F).m.push(e)
			: Tr(() => {
					const t = Ae(e);
					if (typeof t == 'function')
						return (
							/** @type {() => void} */
							t
						);
				});
}
function $a(e) {
	var t =
		/** @type {ComponentContextLegacy} */
		e.l;
	return t.u ?? (t.u = { a: [], b: [], m: [] });
}
function oe(e, t, [r, n] = [0, 0]) {
	U && r === 0 && Ct();
	var i = e,
		o = null,
		s = null,
		a = pe,
		l = r > 0 ? Ft : 0,
		u = !1;
	const f = (c, b = !0) => {
			(u = !0), d(b, c);
		},
		d = (c, b) => {
			if (a === (a = c)) return;
			let p = !1;
			if (U && n !== -1) {
				if (r === 0) {
					const S = si(i);
					S === Br
						? (n = 0)
						: S === Fr
							? (n = 1 / 0)
							: ((n = parseInt(S.substring(1))), n !== n && (n = a ? 1 / 0 : -1));
				}
				const k = n > r;
				!!a === k && ((i = Mr()), Ue(i), je(!1), (p = !0), (n = -1));
			}
			a
				? (o ? nr(o) : b && (o = Xe(() => b(i))),
					s &&
						Ot(s, () => {
							s = null;
						}))
				: (s ? nr(s) : b && (s = Xe(() => b(i, [r + 1, n]))),
					o &&
						Ot(o, () => {
							o = null;
						})),
				p && je(!0);
		};
	ft(() => {
		(u = !1), t(f), u || d(null, null);
	}, l),
		U && (i = D);
}
function Ta(e, t) {
	return t;
}
function ja(e, t, r, n) {
	for (var i = [], o = t.length, s = 0; s < o; s++) tn(t[s].e, i, !0);
	var a = o > 0 && i.length === 0 && r !== null;
	if (a) {
		var l =
			/** @type {Element} */
			/** @type {Element} */
			r.parentNode;
		di(l),
			l.append(
				/** @type {Element} */
				r
			),
			n.clear(),
			nt(e, t[0].prev, t[o - 1].next);
	}
	wi(i, () => {
		for (var u = 0; u < o; u++) {
			var f = t[u];
			a || (n.delete(f.k), nt(e, f.prev, f.next)), ke(f.e, !a);
		}
	});
}
function Ti(e, t, r, n, i, o = null) {
	var s = e,
		a = { flags: t, items: /* @__PURE__ */ new Map(), first: null },
		l = (t & Yn) !== 0;
	if (l) {
		var u =
			/** @type {Element} */
			e;
		s = U
			? Ue(
					/** @type {Comment | Text} */
					/* @__PURE__ */ Se(u)
				)
			: u.appendChild(kt());
	}
	U && Ct();
	var f = null,
		d = !1,
		c = /* @__PURE__ */ ri(() => {
			var b = r();
			return cr(b) ? b : b == null ? [] : qr(b);
		});
	ft(() => {
		var b = w(c),
			p = b.length;
		if (d && p === 0) return;
		d = p === 0;
		let k = !1;
		if (U) {
			var S = si(s) === Fr;
			S !== (p === 0) && ((s = Mr()), Ue(s), je(!1), (k = !0));
		}
		if (U) {
			for (var I = null, v, m = 0; m < p; m++) {
				if (D.nodeType === At && /** @type {Comment} */ D.data === Yr) {
					(s = /** @type {Comment} */ D), (k = !0), je(!1);
					break;
				}
				var h = b[m],
					g = n(h, m);
				(v = ji(D, a, I, null, h, g, m, i, t, r)), a.items.set(g, v), (I = v);
			}
			p > 0 && Ue(Mr());
		}
		U || Aa(b, a, s, i, t, n, r),
			o !== null &&
				(p === 0
					? f
						? nr(f)
						: (f = Xe(() => o(s)))
					: f !== null &&
						Ot(f, () => {
							f = null;
						})),
			k && je(!0),
			w(c);
	}),
		U && (s = D);
}
function Aa(e, t, r, n, i, o, s) {
	var $, V, se, ce;
	var a = (i & No) !== 0,
		l = (i & (Vr | Rr)) !== 0,
		u = e.length,
		f = t.items,
		d = t.first,
		c = d,
		b,
		p = null,
		k,
		S = [],
		I = [],
		v,
		m,
		h,
		g;
	if (a)
		for (g = 0; g < u; g += 1)
			(v = e[g]),
				(m = o(v, g)),
				(h = f.get(m)),
				h !== void 0 &&
					(($ = h.a) == null || $.measure(), (k ?? (k = /* @__PURE__ */ new Set())).add(h));
	for (g = 0; g < u; g += 1) {
		if (((v = e[g]), (m = o(v, g)), (h = f.get(m)), h === void 0)) {
			var z = c ? /** @type {TemplateNode} */ c.e.nodes_start : r;
			(p = ji(z, t, p, p === null ? t.first : p.next, v, m, g, n, i, s)),
				f.set(m, p),
				(S = []),
				(I = []),
				(c = p.next);
			continue;
		}
		if (
			(l && Ua(h, v, g, i),
			(h.e.f & Je) !== 0 &&
				(nr(h.e),
				a && ((V = h.a) == null || V.unfix(), (k ?? (k = /* @__PURE__ */ new Set())).delete(h))),
			h !== c)
		) {
			if (b !== void 0 && b.has(h)) {
				if (S.length < I.length) {
					var A = I[0],
						Y;
					p = A.prev;
					var M = S[0],
						P = S[S.length - 1];
					for (Y = 0; Y < S.length; Y += 1) An(S[Y], A, r);
					for (Y = 0; Y < I.length; Y += 1) b.delete(I[Y]);
					nt(t, M.prev, P.next),
						nt(t, p, M),
						nt(t, P, A),
						(c = A),
						(p = P),
						(g -= 1),
						(S = []),
						(I = []);
				} else
					b.delete(h),
						An(h, c, r),
						nt(t, h.prev, h.next),
						nt(t, h, p === null ? t.first : p.next),
						nt(t, p, h),
						(p = h);
				continue;
			}
			for (S = [], I = []; c !== null && c.k !== m; )
				(c.e.f & Je) === 0 && (b ?? (b = /* @__PURE__ */ new Set())).add(c),
					I.push(c),
					(c = c.next);
			if (c === null) continue;
			h = c;
		}
		S.push(h), (p = h), (c = h.next);
	}
	if (c !== null || b !== void 0) {
		for (var X = b === void 0 ? [] : qr(b); c !== null; )
			(c.e.f & Je) === 0 && X.push(c), (c = c.next);
		var q = X.length;
		if (q > 0) {
			var N = (i & Yn) !== 0 && u === 0 ? r : null;
			if (a) {
				for (g = 0; g < q; g += 1) (se = X[g].a) == null || se.measure();
				for (g = 0; g < q; g += 1) (ce = X[g].a) == null || ce.fix();
			}
			ja(t, X, N, f);
		}
	}
	a &&
		Yt(() => {
			var le;
			if (k !== void 0) for (h of k) (le = h.a) == null || le.apply();
		}),
		(H.first = t.first && t.first.e),
		(H.last = p && p.e);
}
function Ua(e, t, r, n) {
	(n & Vr) !== 0 && Lr(e.v, t),
		(n & Rr) !== 0
			? Lr(
					/** @type {Value<number>} */
					e.i,
					r
				)
			: (e.i = r);
}
function ji(e, t, r, n, i, o, s, a, l, u) {
	var f = (l & Vr) !== 0,
		d = (l & zo) === 0,
		c = f ? (d ? /* @__PURE__ */ ot(i, !1, !1) : yt(i)) : i,
		b = (l & Rr) === 0 ? s : yt(s),
		p = {
			i: b,
			v: c,
			k: o,
			a: null,
			// @ts-expect-error
			e: null,
			prev: r,
			next: n
		};
	try {
		return (
			(p.e = Xe(() => a(e, c, b, u), U)),
			(p.e.prev = r && r.e),
			(p.e.next = n && n.e),
			r === null ? (t.first = p) : ((r.next = p), (r.e.next = p.e)),
			n !== null && ((n.prev = p), (n.e.prev = p.e)),
			p
		);
	} finally {
	}
}
function An(e, t, r) {
	for (
		var n = e.next ? /** @type {TemplateNode} */ e.next.e.nodes_start : r,
			i = t ? /** @type {TemplateNode} */ t.e.nodes_start : r,
			o =
				/** @type {TemplateNode} */
				e.e.nodes_start;
		o !== n;

	) {
		var s =
			/** @type {TemplateNode} */
			/* @__PURE__ */ et(o);
		i.before(o), (o = s);
	}
}
function nt(e, t, r) {
	t === null ? (e.first = r) : ((t.next = r), (t.e.next = r && r.e)),
		r !== null && ((r.prev = t), (r.e.prev = t && t.e));
}
function gr(e, t, r, n, i) {
	var a;
	U && Ct();
	var o = (a = t.$$slots) == null ? void 0 : a[r],
		s = !1;
	o === !0 && ((o = t.children), (s = !0)), o === void 0 || o(e, s ? () => n : n);
}
function Oa(e, t, r) {
	U && Ct();
	var n = e,
		i,
		o;
	ft(() => {
		i !== (i = t()) && (o && (Ot(o), (o = null)), i && (o = Xe(() => r(n, i))));
	}, Ft),
		U && (n = D);
}
function vt(e, t) {
	Yt(() => {
		var r = e.getRootNode(),
			n =
				/** @type {ShadowRoot} */
				r.host
					? /** @type {ShadowRoot} */
						r
					: /** @type {Document} */
						(r.head ?? /** @type {Document} */ r.ownerDocument.head);
		if (!n.querySelector('#' + t.hash)) {
			const i = document.createElement('style');
			(i.id = t.hash), (i.textContent = t.code), n.appendChild(i);
		}
	});
}
function Da(e, t) {
	var r = void 0,
		n;
	ft(() => {
		r !== (r = t()) &&
			(n && (ke(n), (n = null)),
			r &&
				(n = Xe(() => {
					en(() =>
						/** @type {(node: Element) => void} */
						r(e)
					);
				})));
	});
}
function Ai(e) {
	var t,
		r,
		n = '';
	if (typeof e == 'string' || typeof e == 'number') n += e;
	else if (typeof e == 'object')
		if (Array.isArray(e)) {
			var i = e.length;
			for (t = 0; t < i; t++) e[t] && (r = Ai(e[t])) && (n && (n += ' '), (n += r));
		} else for (r in e) e[r] && (n && (n += ' '), (n += r));
	return n;
}
function Ha() {
	for (var e, t, r = 0, n = '', i = arguments.length; r < i; r++)
		(e = arguments[r]) && (t = Ai(e)) && (n && (n += ' '), (n += t));
	return n;
}
function Pa(e) {
	return typeof e == 'object' ? Ha(e) : (e ?? '');
}
const Un = [
	...` 	
\r\f \v\uFEFF`
];
function Va(e, t, r) {
	var n = e == null ? '' : '' + e;
	if ((t && (n = n ? n + ' ' + t : t), r)) {
		for (var i in r)
			if (r[i]) n = n ? n + ' ' + i : i;
			else if (n.length)
				for (var o = i.length, s = 0; (s = n.indexOf(i, s)) >= 0; ) {
					var a = s + o;
					(s === 0 || Un.includes(n[s - 1])) && (a === n.length || Un.includes(n[a]))
						? (n = (s === 0 ? '' : n.substring(0, s)) + n.substring(a + 1))
						: (s = a);
				}
	}
	return n === '' ? null : n;
}
function On(e, t = !1) {
	var r = t ? ' !important;' : ';',
		n = '';
	for (var i in e) {
		var o = e[i];
		o != null && o !== '' && (n += ' ' + i + ': ' + o + r);
	}
	return n;
}
function Sr(e) {
	return e[0] !== '-' || e[1] !== '-' ? e.toLowerCase() : e;
}
function Ra(e, t) {
	if (t) {
		var r = '',
			n,
			i;
		if ((Array.isArray(t) ? ((n = t[0]), (i = t[1])) : (n = t), e)) {
			e = String(e)
				.replaceAll(/\s*\/\*.*?\*\/\s*/g, '')
				.trim();
			var o = !1,
				s = 0,
				a = !1,
				l = [];
			n && l.push(...Object.keys(n).map(Sr)), i && l.push(...Object.keys(i).map(Sr));
			var u = 0,
				f = -1;
			const k = e.length;
			for (var d = 0; d < k; d++) {
				var c = e[d];
				if (
					(a
						? c === '/' && e[d - 1] === '*' && (a = !1)
						: o
							? o === c && (o = !1)
							: c === '/' && e[d + 1] === '*'
								? (a = !0)
								: c === '"' || c === "'"
									? (o = c)
									: c === '('
										? s++
										: c === ')' && s--,
					!a && o === !1 && s === 0)
				) {
					if (c === ':' && f === -1) f = d;
					else if (c === ';' || d === k - 1) {
						if (f !== -1) {
							var b = Sr(e.substring(u, f).trim());
							if (!l.includes(b)) {
								c !== ';' && d++;
								var p = e.substring(u, d).trim();
								r += ' ' + p + ';';
							}
						}
						(u = d + 1), (f = -1);
					}
				}
			}
		}
		return n && (r += On(n)), i && (r += On(i, !0)), (r = r.trim()), r === '' ? null : r;
	}
	return e == null ? null : String(e);
}
function me(e, t, r, n, i, o) {
	var s = e.__className;
	if (U || s !== r || s === void 0) {
		var a = Va(r, n, o);
		(!U || a !== e.getAttribute('class')) &&
			(a == null ? e.removeAttribute('class') : t ? (e.className = a) : e.setAttribute('class', a)),
			(e.__className = r);
	} else if (o && i !== o)
		for (var l in o) {
			var u = !!o[l];
			(i == null || u !== !!i[l]) && e.classList.toggle(l, u);
		}
	return o;
}
function Ir(e, t = {}, r, n) {
	for (var i in r) {
		var o = r[i];
		t[i] !== o && (r[i] == null ? e.style.removeProperty(i) : e.style.setProperty(i, o, n));
	}
}
function Ba(e, t, r, n) {
	var i = e.__style;
	if (U || i !== t) {
		var o = Ra(t, n);
		(!U || o !== e.getAttribute('style')) &&
			(o == null ? e.removeAttribute('style') : (e.style.cssText = o)),
			(e.__style = t);
	} else
		n &&
			(Array.isArray(n)
				? (Ir(e, r == null ? void 0 : r[0], n[0]),
					Ir(e, r == null ? void 0 : r[1], n[1], 'important'))
				: Ir(e, r, n));
	return n;
}
function Hr(e, t, r = !1) {
	if (e.multiple) {
		if (t == null) return;
		if (!cr(t)) return ia();
		for (var n of e.options) n.selected = t.includes(Dn(n));
		return;
	}
	for (n of e.options) {
		var i = Dn(n);
		if (ta(i, t)) {
			n.selected = !0;
			return;
		}
	}
	(!r || t !== void 0) && (e.selectedIndex = -1);
}
function Fa(e) {
	var t = new MutationObserver(() => {
		Hr(e, e.__value);
	});
	t.observe(e, {
		// Listen to option element changes
		childList: !0,
		subtree: !0,
		// because of <optgroup>
		// Listen to option element value attribute changes
		// (doesn't get notified of select value changes,
		// because that property is not reflected as an attribute)
		attributes: !0,
		attributeFilter: ['value']
	}),
		vr(() => {
			t.disconnect();
		});
}
function Dn(e) {
	return '__value' in e ? e.__value : e.value;
}
const mt = Symbol('class'),
	Lt = Symbol('style'),
	Ui = Symbol('is custom element'),
	Oi = Symbol('is html');
function Ya(e, t) {
	t ? e.hasAttribute('selected') || e.setAttribute('selected', '') : e.removeAttribute('selected');
}
function Z(e, t, r, n) {
	var i = Di(e);
	(U &&
		((i[t] = e.getAttribute(t)),
		t === 'src' || t === 'srcset' || (t === 'href' && e.nodeName === 'LINK'))) ||
		(i[t] !== (i[t] = r) &&
			(t === 'loading' && (e[Ro] = r),
			r == null
				? e.removeAttribute(t)
				: typeof r != 'string' && Hi(e).includes(t)
					? (e[t] = r)
					: e.setAttribute(t, r)));
}
function qa(e, t, r, n, i = !1) {
	var o = Di(e),
		s = o[Ui],
		a = !o[Oi];
	let l = U && s;
	l && je(!1);
	var u = t || {},
		f = e.tagName === 'OPTION';
	for (var d in t) d in r || (r[d] = null);
	r.class ? (r.class = Pa(r.class)) : (n || r[mt]) && (r.class = null),
		r[Lt] && (r.style ?? (r.style = null));
	var c = Hi(e);
	for (const m in r) {
		let h = r[m];
		if (f && m === 'value' && h == null) {
			(e.value = e.__value = ''), (u[m] = h);
			continue;
		}
		if (m === 'class') {
			var b = e.namespaceURI === 'http://www.w3.org/1999/xhtml';
			me(e, b, h, n, t == null ? void 0 : t[mt], r[mt]), (u[m] = h), (u[mt] = r[mt]);
			continue;
		}
		if (m === 'style') {
			Ba(e, h, t == null ? void 0 : t[Lt], r[Lt]), (u[m] = h), (u[Lt] = r[Lt]);
			continue;
		}
		var p = u[m];
		if (!(h === p && !(h === void 0 && e.hasAttribute(m)))) {
			u[m] = h;
			var k = m[0] + m[1];
			if (k !== '$$')
				if (k === 'on') {
					const g = {},
						z = '$$' + m;
					let A = m.slice(2);
					var S = Ca(A);
					if ((xa(A) && ((A = A.slice(0, -7)), (g.capture = !0)), !S && p)) {
						if (h != null) continue;
						e.removeEventListener(A, u[z], g), (u[z] = null);
					}
					if (h != null)
						if (S) (e[`__${A}`] = h), Zt([A]);
						else {
							let Y = function (M) {
								u[m].call(this, M);
							};
							u[z] = zi(A, e, Y, g);
						}
					else S && (e[`__${A}`] = void 0);
				} else if (m === 'style') Z(e, m, h);
				else if (m === 'autofocus')
					_a(
						/** @type {HTMLElement} */
						e,
						!!h
					);
				else if (!s && (m === '__value' || (m === 'value' && h != null))) e.value = e.__value = h;
				else if (m === 'selected' && f)
					Ya(
						/** @type {HTMLOptionElement} */
						e,
						h
					);
				else {
					var I = m;
					a || (I = Ia(I));
					var v = I === 'defaultValue' || I === 'defaultChecked';
					if (h == null && !s && !v)
						if (((o[m] = null), I === 'value' || I === 'checked')) {
							let g =
								/** @type {HTMLInputElement} */
								e;
							const z = t === void 0;
							if (I === 'value') {
								let A = g.defaultValue;
								g.removeAttribute(I), (g.defaultValue = A), (g.value = g.__value = z ? A : null);
							} else {
								let A = g.defaultChecked;
								g.removeAttribute(I), (g.defaultChecked = A), (g.checked = z ? A : !1);
							}
						} else e.removeAttribute(m);
					else
						v || (c.includes(I) && (s || typeof h != 'string'))
							? (e[I] = h)
							: typeof h != 'function' && Z(e, I, h);
				}
		}
	}
	return l && je(!0), u;
}
function sr(e, t, r = [], n, i = !1, o = Et) {
	const s = r.map(o);
	var a = void 0,
		l = {},
		u = e.nodeName === 'SELECT',
		f = !1;
	if (
		(ft(() => {
			var c = t(...s.map(w)),
				b = qa(e, a, c, n, i);
			f &&
				u &&
				'value' in c &&
				Hr(
					/** @type {HTMLSelectElement} */
					e,
					c.value
				);
			for (let k of Object.getOwnPropertySymbols(l)) c[k] || ke(l[k]);
			for (let k of Object.getOwnPropertySymbols(c)) {
				var p = c[k];
				k.description === Uo &&
					(!a || p !== a[k]) &&
					(l[k] && ke(l[k]), (l[k] = Xe(() => Da(e, () => p)))),
					(b[k] = p);
			}
			a = b;
		}),
		u)
	) {
		var d =
			/** @type {HTMLSelectElement} */
			e;
		en(() => {
			Hr(
				d,
				/** @type {Record<string | symbol, any>} */
				a.value,
				!0
			),
				Fa(d);
		});
	}
	f = !0;
}
function Di(e) {
	return (
		/** @type {Record<string | symbol, unknown>} **/
		// @ts-expect-error
		e.__attributes ??
		(e.__attributes = {
			[Ui]: e.nodeName.includes('-'),
			[Oi]: e.namespaceURI === Ao
		})
	);
}
var Hn = /* @__PURE__ */ new Map();
function Hi(e) {
	var t = Hn.get(e.nodeName);
	if (t) return t;
	Hn.set(e.nodeName, (t = []));
	for (var r, n = e, i = Element.prototype; i !== n; ) {
		r = Jn(n);
		for (var o in r) r[o].set && t.push(o);
		n = Zr(n);
	}
	return t;
}
function Pn(e, t) {
	return e === t || (e == null ? void 0 : e[Re]) === t;
}
function it(e = {}, t, r, n) {
	return (
		en(() => {
			var i, o;
			return (
				hr(() => {
					(i = o),
						(o = (n == null ? void 0 : n()) || []),
						Ae(() => {
							e !== r(...o) && (t(e, ...o), i && Pn(r(...i), e) && t(null, ...i));
						});
				}),
				() => {
					Yt(() => {
						o && Pn(r(...o), e) && t(null, ...o);
					});
				}
			);
		}),
		e
	);
}
function Za(e = !1) {
	const t =
			/** @type {ComponentContextLegacy} */
			F,
		r = t.l.u;
	if (!r) return;
	let n = () => rn(t.s);
	if (e) {
		let i = 0,
			o =
				/** @type {Record<string, any>} */
				{};
		const s = /* @__PURE__ */ Et(() => {
			let a = !1;
			const l = t.s;
			for (const u in l) l[u] !== o[u] && ((o[u] = l[u]), (a = !0));
			return a && i++, i;
		});
		n = () => w(s);
	}
	r.b.length &&
		aa(() => {
			Vn(t, n), jt(r.b);
		}),
		Tr(() => {
			const i = Ae(() => r.m.map(Po));
			return () => {
				for (const o of i) typeof o == 'function' && o();
			};
		}),
		r.a.length &&
			Tr(() => {
				Vn(t, n), jt(r.a);
			});
}
function Vn(e, t) {
	if (e.l.s) for (const r of e.l.s) w(r);
	t();
}
function nn(e, t, r) {
	if (e == null) return t(void 0), r && r(void 0), Ne;
	const n = Ae(() =>
		e.subscribe(
			t,
			// @ts-expect-error
			r
		)
	);
	return n.unsubscribe ? () => n.unsubscribe() : n;
}
const gt = [];
function Ja(e, t) {
	return {
		subscribe: mr(e, t).subscribe
	};
}
function mr(e, t = Ne) {
	let r = null;
	const n = /* @__PURE__ */ new Set();
	function i(a) {
		if (ei(e, a) && ((e = a), r)) {
			const l = !gt.length;
			for (const u of n) u[1](), gt.push(u, e);
			if (l) {
				for (let u = 0; u < gt.length; u += 2) gt[u][0](gt[u + 1]);
				gt.length = 0;
			}
		}
	}
	function o(a) {
		i(
			a(
				/** @type {T} */
				e
			)
		);
	}
	function s(a, l = Ne) {
		const u = [a, l];
		return (
			n.add(u),
			n.size === 1 && (r = t(i, o) || Ne),
			a(
				/** @type {T} */
				e
			),
			() => {
				n.delete(u), n.size === 0 && r && (r(), (r = null));
			}
		);
	}
	return { set: i, update: o, subscribe: s };
}
function Ka(e, t, r) {
	const n = !Array.isArray(e),
		i = n ? [e] : e;
	if (!i.every(Boolean)) throw new Error('derived() expects stores as input, got a falsy value');
	const o = t.length < 2;
	return Ja(r, (s, a) => {
		let l = !1;
		const u = [];
		let f = 0,
			d = Ne;
		const c = () => {
				if (f) return;
				d();
				const p = t(n ? u[0] : u, s, a);
				o ? s(p) : (d = typeof p == 'function' ? p : Ne);
			},
			b = i.map((p, k) =>
				nn(
					p,
					(S) => {
						(u[k] = S), (f &= ~(1 << k)), l && c();
					},
					() => {
						f |= 1 << k;
					}
				)
			);
		return (
			(l = !0),
			c(),
			function () {
				jt(b), d(), (l = !1);
			}
		);
	});
}
function Wa(e) {
	let t;
	return nn(e, (r) => (t = r))(), t;
}
let Xt = !1,
	Pr = Symbol();
function Ie(e, t, r) {
	const n =
		r[t] ??
		(r[t] = {
			store: null,
			source: /* @__PURE__ */ ot(void 0),
			unsubscribe: Ne
		});
	if (n.store !== e && !(Pr in r))
		if ((n.unsubscribe(), (n.store = e ?? null), e == null))
			(n.source.v = void 0), (n.unsubscribe = Ne);
		else {
			var i = !0;
			(n.unsubscribe = nn(e, (o) => {
				i ? (n.source.v = o) : j(n.source, o);
			})),
				(i = !1);
		}
	return e && Pr in r ? Wa(e) : w(n.source);
}
function Kt() {
	const e = {};
	function t() {
		vr(() => {
			for (var r in e) e[r].unsubscribe();
			Tt(e, Pr, {
				enumerable: !1,
				value: !0
			});
		});
	}
	return [e, t];
}
function Ga(e) {
	var t = Xt;
	try {
		return (Xt = !1), [e(), Xt];
	} finally {
		Xt = t;
	}
}
const Xa = {
	get(e, t) {
		if (!e.exclude.includes(t)) return e.props[t];
	},
	set(e, t) {
		return !1;
	},
	getOwnPropertyDescriptor(e, t) {
		if (!e.exclude.includes(t) && t in e.props)
			return {
				enumerable: !0,
				configurable: !0,
				value: e.props[t]
			};
	},
	has(e, t) {
		return e.exclude.includes(t) ? !1 : t in e.props;
	},
	ownKeys(e) {
		return Reflect.ownKeys(e.props).filter((t) => !e.exclude.includes(t));
	}
};
// @__NO_SIDE_EFFECTS__
function on(e, t, r) {
	return new Proxy({ props: e, exclude: t }, Xa);
}
const Qa = {
	get(e, t) {
		if (!e.exclude.includes(t)) return w(e.version), t in e.special ? e.special[t]() : e.props[t];
	},
	set(e, t, r) {
		return (
			t in e.special ||
				(e.special[t] = y(
					{
						get [t]() {
							return e.props[t];
						}
					},
					/** @type {string} */
					t,
					qn
				)),
			e.special[t](r),
			$n(e.version),
			!0
		);
	},
	getOwnPropertyDescriptor(e, t) {
		if (!e.exclude.includes(t) && t in e.props)
			return {
				enumerable: !0,
				configurable: !0,
				value: e.props[t]
			};
	},
	deleteProperty(e, t) {
		return e.exclude.includes(t) || (e.exclude.push(t), $n(e.version)), !0;
	},
	has(e, t) {
		return e.exclude.includes(t) ? !1 : t in e.props;
	},
	ownKeys(e) {
		return Reflect.ownKeys(e.props).filter((t) => !e.exclude.includes(t));
	}
};
function lr(e, t) {
	return new Proxy({ props: e, exclude: t, special: {}, version: yt(0) }, Qa);
}
const es = {
	get(e, t) {
		let r = e.props.length;
		for (; r--; ) {
			let n = e.props[r];
			if ((zt(n) && (n = n()), typeof n == 'object' && n !== null && t in n)) return n[t];
		}
	},
	set(e, t, r) {
		let n = e.props.length;
		for (; n--; ) {
			let i = e.props[n];
			zt(i) && (i = i());
			const o = Ze(i, t);
			if (o && o.set) return o.set(r), !0;
		}
		return !1;
	},
	getOwnPropertyDescriptor(e, t) {
		let r = e.props.length;
		for (; r--; ) {
			let n = e.props[r];
			if ((zt(n) && (n = n()), typeof n == 'object' && n !== null && t in n)) {
				const i = Ze(n, t);
				return i && !i.configurable && (i.configurable = !0), i;
			}
		}
	},
	has(e, t) {
		if (t === Re || t === Qr) return !1;
		for (let r of e.props) if ((zt(r) && (r = r()), r != null && t in r)) return !0;
		return !1;
	},
	ownKeys(e) {
		const t = [];
		for (let r of e.props)
			if ((zt(r) && (r = r()), !!r)) {
				for (const n in r) t.includes(n) || t.push(n);
				for (const n of Object.getOwnPropertySymbols(r)) t.includes(n) || t.push(n);
			}
		return t;
	}
};
function Pi(...e) {
	return new Proxy({ props: e }, es);
}
function ts(e) {
	var t;
	return ((t = e.ctx) == null ? void 0 : t.d) ?? !1;
}
function y(e, t, r, n) {
	var I;
	var i = !xt || (r & Mo) !== 0,
		o = (r & $o) !== 0,
		s = (r & To) !== 0,
		a =
			/** @type {V} */
			n,
		l = !0,
		u = () => (
			l &&
				((l = !1),
				(a = s
					? Ae(
							/** @type {() => V} */
							n
						)
					: /** @type {V} */
						n)),
			a
		),
		f;
	if (o) {
		var d = Re in e || Qr in e;
		f = ((I = Ze(e, t)) == null ? void 0 : I.set) ?? (d && t in e ? (v) => (e[t] = v) : void 0);
	}
	var c,
		b = !1;
	o
		? ([c, b] = Ga(
				() =>
					/** @type {V} */
					e[t]
			))
		: (c = /** @type {V} */ e[t]),
		c === void 0 && n !== void 0 && ((c = u()), f && (i && Jo(), f(c)));
	var p;
	if (
		(i
			? (p = () => {
					var v =
						/** @type {V} */
						e[t];
					return v === void 0 ? u() : ((l = !0), v);
				})
			: (p = () => {
					var v =
						/** @type {V} */
						e[t];
					return v !== void 0 && (a = /** @type {V} */ void 0), v === void 0 ? a : v;
				}),
		i && (r & qn) === 0)
	)
		return p;
	if (f) {
		var k = e.$$legacy;
		return function (v, m) {
			return arguments.length > 0 ? ((!i || !m || k || b) && f(m ? p() : v), v) : p();
		};
	}
	var S = ((r & Lo) !== 0 ? Et : ri)(p);
	return (
		o && w(S),
		function (v, m) {
			if (arguments.length > 0) {
				const h = m ? w(S) : i && o ? wt(v) : v;
				return j(S, h), a !== void 0 && (a = h), v;
			}
			return ts(S) ? S.v : w(S);
		}
	);
}
function rs(e) {
	return new ns(e);
}
var qe, Ce;
class ns {
	/**
	 * @param {ComponentConstructorOptions & {
	 *  component: any;
	 * }} options
	 */
	constructor(t) {
		/** @type {any} */
		xr(this, qe);
		/** @type {Record<string, any>} */
		xr(this, Ce);
		var o;
		var r = /* @__PURE__ */ new Map(),
			n = (s, a) => {
				var l = /* @__PURE__ */ ot(a, !1, !1);
				return r.set(s, l), l;
			};
		const i = new Proxy(
			{ ...(t.props || {}), $$events: {} },
			{
				get(s, a) {
					return w(r.get(a) ?? n(a, Reflect.get(s, a)));
				},
				has(s, a) {
					return a === Qr ? !0 : (w(r.get(a) ?? n(a, Reflect.get(s, a))), Reflect.has(s, a));
				},
				set(s, a, l) {
					return j(r.get(a) ?? n(a, l), l), Reflect.set(s, a, l);
				}
			}
		);
		Er(
			this,
			Ce,
			(t.hydrate ? La : Mi)(t.component, {
				target: t.target,
				anchor: t.anchor,
				props: i,
				context: t.context,
				intro: t.intro ?? !1,
				recover: t.recover
			})
		),
			(!((o = t == null ? void 0 : t.props) != null && o.$$host) || t.sync === !1) && _(),
			Er(this, qe, i.$$events);
		for (const s of Object.keys(he(this, Ce)))
			s === '$set' ||
				s === '$destroy' ||
				s === '$on' ||
				Tt(this, s, {
					get() {
						return he(this, Ce)[s];
					},
					/** @param {any} value */
					set(a) {
						he(this, Ce)[s] = a;
					},
					enumerable: !0
				});
		(he(this, Ce).$set =
			/** @param {Record<string, any>} next */
			(s) => {
				Object.assign(i, s);
			}),
			(he(this, Ce).$destroy = () => {
				Ma(he(this, Ce));
			});
	}
	/** @param {Record<string, any>} props */
	$set(t) {
		he(this, Ce).$set(t);
	}
	/**
	 * @param {string} event
	 * @param {(...args: any[]) => any} callback
	 * @returns {any}
	 */
	$on(t, r) {
		he(this, qe)[t] = he(this, qe)[t] || [];
		const n = (...i) => r.call(this, ...i);
		return (
			he(this, qe)[t].push(n),
			() => {
				he(this, qe)[t] = he(this, qe)[t].filter(
					/** @param {any} fn */
					(i) => i !== n
				);
			}
		);
	}
	$destroy() {
		he(this, Ce).$destroy();
	}
}
(qe = new WeakMap()), (Ce = new WeakMap());
let Vi;
typeof HTMLElement == 'function' &&
	(Vi = class extends HTMLElement {
		/**
		 * @param {*} $$componentCtor
		 * @param {*} $$slots
		 * @param {*} use_shadow_dom
		 */
		constructor(t, r, n) {
			super();
			/** The Svelte component constructor */
			be(this, '$$ctor');
			/** Slots */
			be(this, '$$s');
			/** @type {any} The Svelte component instance */
			be(this, '$$c');
			/** Whether or not the custom element is connected */
			be(this, '$$cn', !1);
			/** @type {Record<string, any>} Component props data */
			be(this, '$$d', {});
			/** `true` if currently in the process of reflecting component props back to attributes */
			be(this, '$$r', !1);
			/** @type {Record<string, CustomElementPropDefinition>} Props definition (name, reflected, type etc) */
			be(this, '$$p_d', {});
			/** @type {Record<string, EventListenerOrEventListenerObject[]>} Event listeners */
			be(this, '$$l', {});
			/** @type {Map<EventListenerOrEventListenerObject, Function>} Event listener unsubscribe functions */
			be(this, '$$l_u', /* @__PURE__ */ new Map());
			/** @type {any} The managed render effect for reflecting attributes */
			be(this, '$$me');
			(this.$$ctor = t), (this.$$s = r), n && this.attachShadow({ mode: 'open' });
		}
		/**
		 * @param {string} type
		 * @param {EventListenerOrEventListenerObject} listener
		 * @param {boolean | AddEventListenerOptions} [options]
		 */
		addEventListener(t, r, n) {
			if (((this.$$l[t] = this.$$l[t] || []), this.$$l[t].push(r), this.$$c)) {
				const i = this.$$c.$on(t, r);
				this.$$l_u.set(r, i);
			}
			super.addEventListener(t, r, n);
		}
		/**
		 * @param {string} type
		 * @param {EventListenerOrEventListenerObject} listener
		 * @param {boolean | AddEventListenerOptions} [options]
		 */
		removeEventListener(t, r, n) {
			if ((super.removeEventListener(t, r, n), this.$$c)) {
				const i = this.$$l_u.get(r);
				i && (i(), this.$$l_u.delete(r));
			}
		}
		async connectedCallback() {
			if (((this.$$cn = !0), !this.$$c)) {
				let t = function (i) {
					return (o) => {
						const s = document.createElement('slot');
						i !== 'default' && (s.name = i), L(o, s);
					};
				};
				if ((await Promise.resolve(), !this.$$cn || this.$$c)) return;
				const r = {},
					n = is(this);
				for (const i of this.$$s)
					i in n &&
						(i === 'default' && !this.$$d.children
							? ((this.$$d.children = t(i)), (r.default = !0))
							: (r[i] = t(i)));
				for (const i of this.attributes) {
					const o = this.$$g_p(i.name);
					o in this.$$d || (this.$$d[o] = Qt(o, i.value, this.$$p_d, 'toProp'));
				}
				for (const i in this.$$p_d)
					!(i in this.$$d) && this[i] !== void 0 && ((this.$$d[i] = this[i]), delete this[i]);
				(this.$$c = rs({
					component: this.$$ctor,
					target: this.shadowRoot || this,
					props: {
						...this.$$d,
						$$slots: r,
						$$host: this
					}
				})),
					(this.$$me = sa(() => {
						hr(() => {
							var i;
							this.$$r = !0;
							for (const o of tr(this.$$c)) {
								if (!((i = this.$$p_d[o]) != null && i.reflect)) continue;
								this.$$d[o] = this.$$c[o];
								const s = Qt(o, this.$$d[o], this.$$p_d, 'toAttribute');
								s == null
									? this.removeAttribute(this.$$p_d[o].attribute || o)
									: this.setAttribute(this.$$p_d[o].attribute || o, s);
							}
							this.$$r = !1;
						});
					}));
				for (const i in this.$$l)
					for (const o of this.$$l[i]) {
						const s = this.$$c.$on(i, o);
						this.$$l_u.set(o, s);
					}
				this.$$l = {};
			}
		}
		// We don't need this when working within Svelte code, but for compatibility of people using this outside of Svelte
		// and setting attributes through setAttribute etc, this is helpful
		/**
		 * @param {string} attr
		 * @param {string} _oldValue
		 * @param {string} newValue
		 */
		attributeChangedCallback(t, r, n) {
			var i;
			this.$$r ||
				((t = this.$$g_p(t)),
				(this.$$d[t] = Qt(t, n, this.$$p_d, 'toProp')),
				(i = this.$$c) == null || i.$set({ [t]: this.$$d[t] }));
		}
		disconnectedCallback() {
			(this.$$cn = !1),
				Promise.resolve().then(() => {
					!this.$$cn && this.$$c && (this.$$c.$destroy(), this.$$me(), (this.$$c = void 0));
				});
		}
		/**
		 * @param {string} attribute_name
		 */
		$$g_p(t) {
			return (
				tr(this.$$p_d).find(
					(r) =>
						this.$$p_d[r].attribute === t || (!this.$$p_d[r].attribute && r.toLowerCase() === t)
				) || t
			);
		}
	});
function Qt(e, t, r, n) {
	var o;
	const i = (o = r[e]) == null ? void 0 : o.type;
	if (((t = i === 'Boolean' && typeof t != 'boolean' ? t != null : t), !n || !r[e])) return t;
	if (n === 'toAttribute')
		switch (i) {
			case 'Object':
			case 'Array':
				return t == null ? null : JSON.stringify(t);
			case 'Boolean':
				return t ? '' : null;
			case 'Number':
				return t ?? null;
			default:
				return t;
		}
	else
		switch (i) {
			case 'Object':
			case 'Array':
				return t && JSON.parse(t);
			case 'Boolean':
				return t;
			// conversion already handled above
			case 'Number':
				return t != null ? +t : t;
			default:
				return t;
		}
}
function is(e) {
	const t = {};
	return (
		e.childNodes.forEach((r) => {
			t[
				/** @type {Element} node */
				r.slot || 'default'
			] = !0;
		}),
		t
	);
}
function G(e, t, r, n, i, o) {
	let s = class extends Vi {
		constructor() {
			super(e, r, i), (this.$$p_d = t);
		}
		static get observedAttributes() {
			return tr(t).map((a) => (t[a].attribute || a).toLowerCase());
		}
	};
	return (
		tr(t).forEach((a) => {
			Tt(s.prototype, a, {
				get() {
					return this.$$c && a in this.$$c ? this.$$c[a] : this.$$d[a];
				},
				set(l) {
					var d;
					(l = Qt(a, l, t)), (this.$$d[a] = l);
					var u = this.$$c;
					if (u) {
						var f = (d = Ze(u, a)) == null ? void 0 : d.get;
						f ? (u[a] = l) : u.$set({ [a]: l });
					}
				}
			});
		}),
		n.forEach((a) => {
			Tt(s.prototype, a, {
				get() {
					var l;
					return (l = this.$$c) == null ? void 0 : l[a];
				}
			});
		}),
		(e.element = /** @type {any} */ s),
		s
	);
}
var os = /* @__PURE__ */ ae('<a><!></a>'),
	as = /* @__PURE__ */ ae('<button><!></button>');
const ss = {
	hash: 'svelte-zftwkm',
	code: `:root, :host {
    /* global button */--ai-button-radius: var(--ai-radius-round);--ai-button-padding-x: 2rem;--ai-button-padding-y: 0.75rem;--ai-button-height: 2.5rem;--ai-button-box-shadow: inset 0 0 0 1px;

    /* primary button */--ai-button-background-primary-rest: var(--ai-color-black);--ai-button-background-primary-hover: var(
      --ai-color-neutral-900
    );--ai-button-background-primary-disabled: var(
      --ai-color-neutral-200
    );--ai-button-text-primary-rest: var(--ai-color-white);--ai-button-text-primary-hover: var(--ai-button-text-primary-rest);--ai-button-text-primary-disabled: var(--ai-color-neutral-800);

    /* inverted button */--ai-button-background-inverted-rest: var(--ai-color-white);--ai-button-background-inverted-hover: var(--ai-color-neutral-200);--ai-button-background-inverted-disabled: var(--ai-color-neutral-500);--ai-button-text-inverted-rest: var(--ai-color-black);--ai-button-text-inverted-hover: var(--ai-color-black);--ai-button-text-inverted-disabled: var(--ai-color-neutral-900);--ai-button-border-inverted-rest: none;--ai-button-border-inverted-hover: none;

    /* unstyled */--ai-button-text-unstyled-disabled: var(--ai-color-neutral-700);--ai-button-text-unstyled-inverted-disabled: var(--ai-color-neutral-700);

    /* outline button */--ai-button-background-outline-hover: var(--ai-color-black);--ai-button-text-outline-rest: var(--ai-color-black);--ai-button-text-outline-hover: var(--ai-color-white);--ai-button-border-outline-rest: var(--ai-color-black);--ai-button-border-outline-hover: var(--ai-button-border-outline-rest);

    /* outline inverted button */--ai-button-background-outline-inverted-hover: var(--ai-color-white);--ai-button-text-outline-inverted-rest: var(--ai-color-white);--ai-button-text-outline-inverted-hover: var(--ai-color-black);--ai-button-border-outline-inverted-rest: var(--ai-color-white);--ai-button-border-outline-inverted-hover: var(
      --ai-button-border-outline-inverted-rest
    );}.ai-button.svelte-zftwkm {background-color:var(--ai-button-background-primary-rest);border-radius:var(--ai-button-radius);color:var(--ai-button-text-primary-rest);font-weight:var(--ai-font-weight-medium);padding-inline:var(--ai-button-padding-x);padding-block:var(--ai-button-padding-y);margin:0;}.ai-button.svelte-zftwkm:is(:where(.svelte-zftwkm):hover, :where(.svelte-zftwkm):focus) {background-color:var(--ai-button-background-primary-hover);color:var(--ai-button-text-primary-hover);}.ai-button--small.svelte-zftwkm {--ai-button-padding-x: var(--ai-size-24);--ai-button-padding-y: var(--ai-size-8);font-size:var(--ai-size-12);}.ai-button--outline.svelte-zftwkm {background-color:transparent;box-shadow:var(--ai-button-box-shadow) var(--ai-button-border-outline-rest);color:var(--ai-button-text-outline-rest);}.ai-button--outline.svelte-zftwkm:is(:where(.svelte-zftwkm):hover, :where(.svelte-zftwkm):focus) {background-color:var(--ai-button-background-outline-hover);box-shadow:var(--ai-button-box-shadow) var(--ai-button-border-outline-hover);color:var(--ai-button-text-outline-hover);}.ai-button--outline-inverted.svelte-zftwkm {background-color:transparent;box-shadow:var(--ai-button-box-shadow) var(--ai-button-border-outline-inverted-rest);color:var(--ai-button-text-outline-inverted-rest);}.ai-button--outline-inverted.svelte-zftwkm:is(:where(.svelte-zftwkm):hover, :where(.svelte-zftwkm):focus) {background-color:var(--ai-button-background-outline-inverted-hover);box-shadow:var(--ai-button-box-shadow) var(--ai-button-border-outline-inverted-hover);color:var(--ai-button-text-outline-inverted-hover);}.ai-button--inverted.svelte-zftwkm {background-color:var(--ai-button-background-inverted-rest);color:var(--ai-button-text-inverted-rest);box-shadow:none;border:var(--ai-button-border-inverted-rest);}.ai-button--inverted.svelte-zftwkm:is(:where(.svelte-zftwkm):hover, :where(.svelte-zftwkm):focus) {background-color:var(--ai-button-background-inverted-hover);color:var(--ai-button-text-inverted-hover);border:var(--ai-button-border-inverted-hover);}.ai-button--unstyled.svelte-zftwkm {background:none;color:inherit;border:none;box-shadow:none;padding:0;font:inherit;cursor:pointer;text-decoration:underline;text-underline-offset:var(--ai-font-underline-offset);}.ai-button--unstyled.svelte-zftwkm:is(:where(.svelte-zftwkm):hover, :where(.svelte-zftwkm):focus) {background:none;color:inherit;border:none;box-shadow:none;text-decoration:underline;}.ai-menu-buttons.svelte-zftwkm {display:flex;gap:var(--ai-size-16);}

  /* disabled button styles */:is(.disabled.svelte-zftwkm, [disabled].svelte-zftwkm, [aria-disabled=true].svelte-zftwkm) {background:var(--ai-button-background-primary-disabled);color:var(--ai-button-text-primary-disabled);cursor:not-allowed;}.ai-button--inverted.svelte-zftwkm:is(.disabled:where(.svelte-zftwkm), [disabled]:where(.svelte-zftwkm), [aria-disabled=true]:where(.svelte-zftwkm)) {background:var(--ai-button-background-inverted-disabled);color:var(--ai-button-text-inverted-disabled);}.ai-button--outline.svelte-zftwkm:is(.disabled:where(.svelte-zftwkm), [disabled]:where(.svelte-zftwkm), [aria-disabled=true]:where(.svelte-zftwkm)) {background:transparent;box-shadow:var(--ai-button-box-shadow) var(--ai-color-neutral-700);color:var(--ai-color-neutral-700);}.ai-button--outline-inverted.svelte-zftwkm:is(.disabled:where(.svelte-zftwkm), [disabled]:where(.svelte-zftwkm), [aria-disabled=true]:where(.svelte-zftwkm)) {box-shadow:var(--ai-button-box-shadow) var(--ai-color-neutral-200);color:var(--ai-color-neutral-600);}.ai-button--unstyled.svelte-zftwkm:is(.disabled:where(.svelte-zftwkm), [disabled]:where(.svelte-zftwkm), [aria-disabled=true]:where(.svelte-zftwkm)) {background:none;color:inherit;opacity:0.75;}`
};
function Ri(e, t) {
	J(t, !0), vt(e, ss);
	let r = y(t, 'variant', 7, 'primary'),
		n = y(t, 'theme', 7, 'default'),
		i = y(t, 'href', 7, null),
		o = y(t, 'disabled', 7, !1),
		s = y(t, 'onclick', 7),
		a = y(t, 'children', 7, null),
		l = y(t, 'size', 7, 'medium'),
		u = y(t, 'type', 7, 'button'),
		f = y(t, 'class', 7, ''),
		d = /* @__PURE__ */ on(t, [
			'$$slots',
			'$$events',
			'$$legacy',
			'$$host',
			'variant',
			'theme',
			'href',
			'disabled',
			'onclick',
			'children',
			'size',
			'type',
			'class'
		]);
	const c = /* @__PURE__ */ te(
			() => () =>
				[
					'usa-button',
					'ai-button',
					l() === 'small' ? 'ai-button--small' : '',
					n() === 'inverted' && r() === 'primary' ? 'ai-button--inverted' : '',
					n() === 'inverted' && r() === 'secondary' ? 'ai-button--outline-inverted' : '',
					r() === 'primary' ? 'usa-button--primary' : '',
					r() === 'secondary' ? 'usa-button--outline ai-button--outline' : '',
					r() === 'unstyled' ? 'ai-button--unstyled' : ''
				]
					.filter(Boolean)
					.join(' ')
		),
		b = /* @__PURE__ */ te(() => () => `${w(c)()} ${f()}`.trim());
	var p = It(),
		k = Ke(p);
	{
		var S = (v) => {
				var m = os();
				sr(
					m,
					(g, z) => ({
						class: g,
						href: i(),
						onclick: s(),
						...d,
						[mt]: z
					}),
					[() => w(b)(), () => ({ disabled: o() })],
					'svelte-zftwkm'
				);
				var h = C(m);
				Dr(h, () => a() ?? Ne), E(m), L(v, m);
			},
			I = (v) => {
				var m = as();
				sr(
					m,
					(g) => ({
						class: g,
						type: u(),
						disabled: o(),
						onclick: s(),
						...d
					}),
					[() => w(b)()],
					'svelte-zftwkm'
				);
				var h = C(m);
				Dr(h, () => a() ?? Ne), E(m), L(v, m);
			};
		oe(k, (v) => {
			i() ? v(S) : v(I, !1);
		});
	}
	return (
		L(e, p),
		K({
			get variant() {
				return r();
			},
			set variant(v = 'primary') {
				r(v), _();
			},
			get theme() {
				return n();
			},
			set theme(v = 'default') {
				n(v), _();
			},
			get href() {
				return i();
			},
			set href(v = null) {
				i(v), _();
			},
			get disabled() {
				return o();
			},
			set disabled(v = !1) {
				o(v), _();
			},
			get onclick() {
				return s();
			},
			set onclick(v) {
				s(v), _();
			},
			get children() {
				return a();
			},
			set children(v = null) {
				a(v), _();
			},
			get size() {
				return l();
			},
			set size(v = 'medium') {
				l(v), _();
			},
			get type() {
				return u();
			},
			set type(v = 'button') {
				u(v), _();
			},
			get class() {
				return f();
			},
			set class(v = '') {
				f(v), _();
			}
		})
	);
}
G(
	Ri,
	{
		variant: {},
		theme: {},
		href: {},
		disabled: {},
		onclick: {},
		children: {},
		size: {},
		type: {},
		class: {}
	},
	[],
	[],
	!0
);
function ls(e, t) {
	J(t, !0);
	let r = y(t, 'variant', 7, 'primary'),
		n = y(t, 'theme', 7, 'default'),
		i = y(t, 'href', 7, null),
		o = y(t, 'disabled', 7, !1),
		s = y(t, 'size', 7, 'medium'),
		a = y(t, 'type', 7, 'button'),
		l = y(t, 'onclick', 7),
		u = y(t, 'children', 7, null),
		f = y(t, 'class', 7, ''),
		d = /* @__PURE__ */ on(t, [
			'$$slots',
			'$$events',
			'$$legacy',
			'$$host',
			'variant',
			'theme',
			'href',
			'disabled',
			'size',
			'type',
			'onclick',
			'children',
			'class'
		]);
	return (
		Ri(
			e,
			Pi(
				{
					get variant() {
						return r();
					},
					get theme() {
						return n();
					},
					get href() {
						return i();
					},
					get disabled() {
						return o();
					},
					get size() {
						return s();
					},
					get type() {
						return a();
					},
					get onclick() {
						return l();
					},
					get children() {
						return u();
					},
					get class() {
						return f();
					}
				},
				() => d,
				{
					$$slots: {
						default: (c, b) => {
							var p = It(),
								k = Ke(p);
							gr(k, t, 'default', {}), L(c, p);
						}
					}
				}
			)
		),
		K({
			get variant() {
				return r();
			},
			set variant(c = 'primary') {
				r(c), _();
			},
			get theme() {
				return n();
			},
			set theme(c = 'default') {
				n(c), _();
			},
			get href() {
				return i();
			},
			set href(c = null) {
				i(c), _();
			},
			get disabled() {
				return o();
			},
			set disabled(c = !1) {
				o(c), _();
			},
			get size() {
				return s();
			},
			set size(c = 'medium') {
				s(c), _();
			},
			get type() {
				return a();
			},
			set type(c = 'button') {
				a(c), _();
			},
			get onclick() {
				return l();
			},
			set onclick(c) {
				l(c), _();
			},
			get children() {
				return u();
			},
			set children(c = null) {
				u(c), _();
			},
			get class() {
				return f();
			},
			set class(c = '') {
				f(c), _();
			}
		})
	);
}
customElements.define(
	'graymatter-button',
	G(
		ls,
		{
			variant: { attribute: 'variant', reflect: !0, type: 'String' },
			theme: { attribute: 'theme', reflect: !0, type: 'String' },
			href: { attribute: 'href', reflect: !0, type: 'String' },
			disabled: { attribute: 'disabled', reflect: !0, type: 'Boolean' },
			size: { attribute: 'size', reflect: !0, type: 'String' },
			type: { attribute: 'type', reflect: !0, type: 'String' },
			onclick: {},
			children: {},
			class: {}
		},
		['default'],
		[],
		!0
	)
);
const Wt = typeof window < 'u';
function us(e, t) {
	if (Wt && typeof sessionStorage < 'u') {
		const r = sessionStorage.getItem(e);
		if (r !== null)
			try {
				return JSON.parse(r);
			} catch (n) {
				return console.warn(`Failed to parse stored value for ${e}:`, n), t;
			}
	}
	return t;
}
function Bi(e, t) {
	const r = us(e, t),
		n = mr(r);
	return (
		Wt &&
			n.subscribe((i) => {
				try {
					sessionStorage.setItem(e, JSON.stringify(i));
				} catch (o) {
					console.warn(`Failed to persist ${e} to sessionStorage:`, o);
				}
			}),
		n
	);
}
Bi('subNav-selectedId', null);
const ct = Bi('subNav-selectedTitle', '');
function Fi(e, t) {
	if (Wt && typeof localStorage < 'u') {
		const r = localStorage.getItem(e);
		if (r !== null)
			try {
				return JSON.parse(r);
			} catch (n) {
				return console.warn(`Failed to parse stored value for ${e}:`, n), t;
			}
	}
	return t;
}
function Yi(e, t) {
	const r = Fi(e, t),
		n = mr(r);
	return (
		Wt &&
			n.subscribe((i) => {
				try {
					localStorage.setItem(e, JSON.stringify(i));
				} catch (o) {
					console.warn(`Failed to persist ${e} to localStorage:`, o);
				}
			}),
		n
	);
}
const $e = Yi('globalSideNav-isExpanded', !1),
	ht = Yi('globalSideNav-selectedItem', null),
	an = Ka(ht, (e) => e === 'discover'),
	Te = {
		toggleExpanded: () => {
			$e.update((e) => !e);
		},
		setExpanded: (e) => {
			$e.set(e);
		},
		setSelectedItem: (e) => {
			ht.set(e), e === 'discover' ? $e.set(!1) : Wt && window.innerWidth > 640 && $e.set(!0);
		},
		handleLayoutTransition: (e) => {
			if (e) $e.set(!1);
			else {
				const t = Fi('globalSideNav-selectedItem', null);
				t && t !== 'discover'
					? (console.log('[NavigationStore] Opening navigation for desktop (non-discover page)'),
						$e.set(!0))
					: $e.set(!1);
			}
		}
	},
	ur = /* @__PURE__ */ Object.freeze(
		/* @__PURE__ */ Object.defineProperty(
			{
				__proto__: null,
				isExpanded: $e,
				isMenuButtonDisabled: an,
				navigationStore: Te,
				selectedItem: ht,
				selectedSubNavItemTitle: ct
			},
			Symbol.toStringTag,
			{ value: 'Module' }
		)
	),
	cs = mr(!1);
var ds = /* @__PURE__ */ ae(
	'<div part="global-header"><div class="header-content svelte-1p8bjnn" part="header-content"><span class="header-text svelte-1p8bjnn"> </span></div></div>'
);
const fs = {
	hash: 'svelte-1p8bjnn',
	code: '.global-header.svelte-1p8bjnn {opacity:0;transition:opacity 0.2s;}.global-header.visible.svelte-1p8bjnn {opacity:1;transition:opacity 0.2s;}.header-content.svelte-1p8bjnn {display:flex;align-items:center;gap:var(--ai-size-16);background:var(--ai-color-white);height:var(--ai-size-56);padding:0 5.8rem;}.header-content.svelte-1p8bjnn {display:flex;align-items:center;gap:var(--ai-size-16);align-self:stretch;}.header-text.svelte-1p8bjnn {color:var(--ai-color-black);font-size:var(--ai-size-16);font-weight:400;line-height:1.3;}'
};
function qi(e, t) {
	J(t, !0), vt(e, fs);
	const [r, n] = Kt(),
		i = () => Ie(ht, '$selectedItem', r),
		o = () => Ie($e, '$isExpanded', r),
		s = () => Ie(cs, '$subNavReady', r),
		a = () => Ie(ct, '$selectedSubNavItemTitle', r),
		l = y(t, 'customHeaderText', 7, '');
	let u = !1;
	const f = /* @__PURE__ */ te(() => () => i() && i() !== 'discover' && !o() && !u && s()),
		d = /* @__PURE__ */ te(() => () => l() || a());
	Jt(() => {
		const v = (h) => {
				if (typeof window < 'u' && window.innerWidth <= 640) return;
				let g = null;
				if (typeof window < 'u' && ((g = sessionStorage.getItem('subNav-topLevelTitle')), g))
					try {
						g = JSON.parse(g);
					} catch {}
				if (typeof g == 'string' && g.trim() !== '')
					ct.set(g), sessionStorage.setItem('subNav-selectedTitle', JSON.stringify(g));
				else {
					const z = h;
					z.detail &&
						z.detail.title &&
						(ct.set(z.detail.title),
						sessionStorage.setItem('subNav-selectedTitle', JSON.stringify(z.detail.title)));
				}
			},
			m = (h) => {
				const g = h,
					{ isMobile: z } = g.detail;
				z ||
					((u = !0),
					setTimeout(() => {
						u = !1;
					}, 300));
			};
		return (
			window.addEventListener('sectionVisible', v),
			window.addEventListener('layoutTransition', m),
			() => {
				window.removeEventListener('sectionVisible', v),
					window.removeEventListener('layoutTransition', m);
			}
		);
	});
	var c = ds();
	let b;
	var p = C(c),
		k = C(p),
		S = C(k, !0);
	E(k),
		E(p),
		E(c),
		we(
			(v, m) => {
				(b = me(c, 1, 'global-header svelte-1p8bjnn', null, b, v)), Vt(S, m);
			},
			[() => ({ visible: w(f)() }), () => w(d)()]
		),
		L(e, c);
	var I = K({
		get customHeaderText() {
			return l();
		},
		set customHeaderText(v = '') {
			l(v), _();
		}
	});
	return n(), I;
}
G(qi, { customHeaderText: {} }, [], [], !0);
function vs(e, t) {
	J(t, !0);
	let r = y(t, 'customHeaderText', 7, '');
	return (
		qi(e, {
			get customHeaderText() {
				return r();
			}
		}),
		K({
			get customHeaderText() {
				return r();
			},
			set customHeaderText(n = '') {
				r(n), _();
			}
		})
	);
}
customElements.define(
	'graymatter-desktop-header',
	G(
		vs,
		{
			customHeaderText: {
				attribute: 'custom-header-text',
				reflect: !0,
				type: 'String'
			}
		},
		[],
		[],
		!0
	)
);
Qo();
var hs = /* @__PURE__ */ W(
		'<path fill-rule="evenodd" clip-rule="evenodd" d="M10.7883 3.2108C11.2367 2.13286 12.7637 2.13286 13.212 3.2108L15.294 8.21652L20.6981 8.64976C21.8619 8.74306 22.3337 10.1953 21.4471 10.9549L17.3298 14.4818L18.5877 19.7553C18.8585 20.8909 17.6232 21.7884 16.6268 21.1799L12.0002 18.354L7.37353 21.1799C6.37721 21.7884 5.14182 20.8909 5.4127 19.7553L6.67062 14.4818L2.55328 10.9549C1.66664 10.1953 2.13851 8.74306 3.30224 8.64976L8.70633 8.21652L10.7883 3.2108Z" fill="black"></path>'
	),
	ps = /* @__PURE__ */ W(
		'<path d="M11.4807 3.49883C11.6729 3.03685 12.3273 3.03685 12.5195 3.49883L14.6454 8.61028C14.7264 8.80504 14.9096 8.93811 15.1199 8.95497L20.6381 9.39736C21.1369 9.43735 21.3391 10.0598 20.9591 10.3853L16.7548 13.9867C16.5946 14.1239 16.5246 14.3392 16.5736 14.5444L17.858 19.9293C17.9741 20.416 17.4447 20.8007 17.0177 20.5398L12.2933 17.6542C12.1133 17.5443 11.8869 17.5443 11.7069 17.6542L6.98251 20.5398C6.55551 20.8007 6.02606 20.416 6.14215 19.9293L7.42664 14.5444C7.47558 14.3392 7.40562 14.1239 7.24543 13.9867L3.04111 10.3853C2.66112 10.0598 2.86335 9.43735 3.36209 9.39736L8.88034 8.95497C9.0906 8.93811 9.27375 8.80504 9.35476 8.61028L11.4807 3.49883Z" stroke="#585C63" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>'
	),
	bs = /* @__PURE__ */ W(
		'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><!></svg>'
	);
function sn(e, t) {
	J(t, !1);
	let r = y(t, 'isSelected', 12, !1);
	var n = bs(),
		i = C(n);
	{
		var o = (a) => {
				var l = hs();
				L(a, l);
			},
			s = (a) => {
				var l = ps();
				L(a, l);
			};
		oe(i, (a) => {
			r() ? a(o) : a(s, !1);
		});
	}
	return (
		E(n),
		L(e, n),
		K({
			get isSelected() {
				return r();
			},
			set isSelected(a) {
				r(a), _();
			}
		})
	);
}
G(sn, { isSelected: {} }, [], [], !0);
var gs = /* @__PURE__ */ W(
		'<path fill-rule="evenodd" clip-rule="evenodd" d="M17.0508 3C19.6009 3.00039 21.6688 5.06802 21.6689 7.61816V13.2832C21.6688 15.8333 19.6009 17.901 17.0508 17.9014H13.2686L9.23926 21.7168C8.58852 22.3323 7.5166 21.8705 7.5166 20.9746V17.8984C5.01299 17.8445 3.00011 15.7997 3 13.2832V7.61816C3.00012 5.06785 5.06785 3.00012 7.61816 3H17.0508Z" fill="black"></path>'
	),
	ms = /* @__PURE__ */ W(
		'<path d="M17.0508 3C19.6009 3.00039 21.6688 5.06802 21.6689 7.61816V13.2832C21.6688 15.8333 19.6009 17.901 17.0508 17.9014H13.2686L9.23926 21.7168C8.58852 22.3323 7.5166 21.8705 7.5166 20.9746V17.8984C5.01299 17.8445 3.00011 15.7997 3 13.2832V7.61816C3.00012 5.06785 5.06785 3.00012 7.61816 3H17.0508ZM7.61816 4.45898C5.87327 4.4591 4.4591 5.87327 4.45898 7.61816V13.2832C4.4591 15.0281 5.87327 16.4432 7.61816 16.4434L8.24609 16.4424C8.64868 16.4424 8.97539 16.7693 8.97559 17.1719V19.958L12.4775 16.6426C12.6186 16.5091 12.7994 16.4443 12.9795 16.4443V16.4434H17.0508C18.7954 16.443 20.2099 15.0279 20.21 13.2832V7.61816C20.2098 5.87344 18.7954 4.45938 17.0508 4.45898H7.61816Z" fill="#585C63"></path>'
	),
	ws = /* @__PURE__ */ W(
		'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><!></svg>'
	);
function ln(e, t) {
	J(t, !1);
	let r = y(t, 'isSelected', 12, !1);
	var n = ws(),
		i = C(n);
	{
		var o = (a) => {
				var l = gs();
				L(a, l);
			},
			s = (a) => {
				var l = ms();
				L(a, l);
			};
		oe(i, (a) => {
			r() ? a(o) : a(s, !1);
		});
	}
	return (
		E(n),
		L(e, n),
		K({
			get isSelected() {
				return r();
			},
			set isSelected(a) {
				r(a), _();
			}
		})
	);
}
G(ln, { isSelected: {} }, [], [], !0);
var _s = /* @__PURE__ */ W(
		'<rect x="2.75" y="6.02856" width="3.7459" height="12.9262" fill="black" stroke="black" stroke-width="1.5"></rect><rect x="9.96338" y="9.96313" width="3.7459" height="8.9918" fill="black" stroke="black" stroke-width="1.5"></rect><rect x="17.1763" y="2.75" width="3.7459" height="16.2049" fill="black" stroke="black" stroke-width="1.5"></rect><path d="M2.32764 22H21.344" stroke="black" stroke-width="1.5" stroke-linecap="square"></path>',
		1
	),
	ys = /* @__PURE__ */ W(
		'<rect x="2.75" y="6.02856" width="3.7459" height="12.9262" stroke="#585C63" stroke-width="1.5"></rect><rect x="9.96338" y="9.96313" width="3.7459" height="8.9918" stroke="#585C63" stroke-width="1.5"></rect><rect x="17.1763" y="2.75" width="3.7459" height="16.2049" stroke="#585C63" stroke-width="1.5"></rect><path d="M2.32764 22H21.344" stroke="#585C63" stroke-width="1.5" stroke-linecap="square"></path>',
		1
	),
	ks = /* @__PURE__ */ W(
		'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><!></svg>'
	);
function un(e, t) {
	J(t, !1);
	let r = y(t, 'isSelected', 12, !1);
	var n = ks(),
		i = C(n);
	{
		var o = (a) => {
				var l = _s();
				Ve(3), L(a, l);
			},
			s = (a) => {
				var l = ys();
				Ve(3), L(a, l);
			};
		oe(i, (a) => {
			r() ? a(o) : a(s, !1);
		});
	}
	return (
		E(n),
		L(e, n),
		K({
			get isSelected() {
				return r();
			},
			set isSelected(a) {
				r(a), _();
			}
		})
	);
}
G(un, { isSelected: {} }, [], [], !0);
var xs = /* @__PURE__ */ W(
		'<rect x="4.41699" y="4.62451" width="15.1664" height="15.1664" rx="1.75" stroke="black" stroke-width="1.5"></rect><g><path d="M9.5 3.66664V2" stroke="black" stroke-width="1.5" stroke-linecap="round"></path><path d="M3.66664 14.4998L2 14.4998" stroke="black" stroke-width="1.5" stroke-linecap="round"></path><path d="M22.0001 14.4998L20.3335 14.4998" stroke="black" stroke-width="1.5" stroke-linecap="round"></path><path d="M9.5 21.9999V20.3333" stroke="black" stroke-width="1.5" stroke-linecap="round"></path><path d="M12.0005 3.66664V2" stroke="black" stroke-width="1.5" stroke-linecap="round"></path><path d="M3.66664 12.0005L2 12.0005" stroke="black" stroke-width="1.5" stroke-linecap="round"></path><path d="M22.0001 12.0005L20.3335 12.0005" stroke="black" stroke-width="1.5" stroke-linecap="round"></path><path d="M12.0005 21.9999V20.3333" stroke="black" stroke-width="1.5" stroke-linecap="round"></path><path d="M14.4995 3.66664V2" stroke="black" stroke-width="1.5" stroke-linecap="round"></path><path d="M3.66664 9.5L2 9.5" stroke="black" stroke-width="1.5" stroke-linecap="round"></path><path d="M22.0001 9.5L20.3335 9.5" stroke="black" stroke-width="1.5" stroke-linecap="round"></path><path d="M14.4995 21.9999V20.3333" stroke="black" stroke-width="1.5" stroke-linecap="round"></path></g><path d="M10.5 8.52856H13.7559C14.7224 8.52856 15.5059 9.31207 15.5059 10.2786V13.5344C15.5059 14.5009 14.7224 15.2844 13.7559 15.2844H10.5C9.5335 15.2844 8.75 14.5009 8.75 13.5344V10.2786C8.75 9.31207 9.5335 8.52856 10.5 8.52856Z" fill="black" stroke="black" stroke-width="1.5"></path>',
		1
	),
	Es = /* @__PURE__ */ W(
		'<rect x="4.41699" y="4.62451" width="15.1664" height="15.1664" rx="1.75" stroke="#585C63" stroke-width="1.5"></rect><g><path d="M9.5 3.66664V2" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path><path d="M3.66664 14.4995L2 14.4995" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path><path d="M22.0001 14.4995L20.3335 14.4995" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path><path d="M9.5 21.9999V20.3333" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path><path d="M12.0005 3.66664V2" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path><path d="M3.66664 12.0002L2 12.0002" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path><path d="M22.0001 12.0002L20.3335 12.0002" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path><path d="M12.0005 21.9999V20.3333" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path><path d="M14.4995 3.66664V2" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path><path d="M3.66664 9.49976L2 9.49976" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path><path d="M22.0001 9.49976L20.3335 9.49976" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path><path d="M14.4995 21.9999V20.3333" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path></g><rect x="8.52881" y="8.52856" width="6.75594" height="6.75594" rx="1.75" stroke="#585C63" stroke-width="1.5"></rect>',
		1
	),
	Cs = /* @__PURE__ */ W(
		'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><!></svg>'
	);
function cn(e, t) {
	J(t, !1);
	let r = y(t, 'isSelected', 12, !1);
	var n = Cs(),
		i = C(n);
	{
		var o = (a) => {
				var l = xs();
				Ve(2), L(a, l);
			},
			s = (a) => {
				var l = Es();
				Ve(2), L(a, l);
			};
		oe(i, (a) => {
			r() ? a(o) : a(s, !1);
		});
	}
	return (
		E(n),
		L(e, n),
		K({
			get isSelected() {
				return r();
			},
			set isSelected(a) {
				r(a), _();
			}
		})
	);
}
G(cn, { isSelected: {} }, [], [], !0);
var Ss = /* @__PURE__ */ W(
		'<path d="M18.75 3H5.25C4.00736 3 3 4.09719 3 5.45064V7.90129V18.5207C3 19.8742 4.00736 20.9714 5.25 20.9714H18.75C19.9926 20.9714 21 19.8742 21 18.5207V7.90129V5.45064C21 4.09719 19.9926 3 18.75 3Z" stroke="#C7CDD6" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path stroke="#C7CDD6" stroke-width="1.5"></path>',
		1
	),
	Is = /* @__PURE__ */ W(
		'<path d="M12.25 18L6 12M6 12L12.25 6M6 12L21 12" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M3 3V21" stroke="black" stroke-width="1.5" stroke-linecap="round"></path>',
		1
	),
	Ns = /* @__PURE__ */ W(
		'<path d="M11.75 18.25L18 12M18 12L11.75 5.75M18 12L3 12" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M21 3V21" stroke="black" stroke-width="1.5" stroke-linecap="round"></path>',
		1
	),
	zs = /* @__PURE__ */ W(
		'<path d="M8.18018 5.45068V18.5208" stroke="#585C63" stroke-width="1.5"></path>'
	),
	Ls = /* @__PURE__ */ W(
		'<path d="M9.18018 3.27222V20.9203" stroke="#585C63" stroke-width="1.5"></path>'
	),
	Ms = /* @__PURE__ */ W(
		'<path d="M18.75 3H5.25C4.00736 3 3 4.09719 3 5.45064V7.90129V18.5207C3 19.8742 4.00736 20.9714 5.25 20.9714H18.75C19.9926 20.9714 21 19.8742 21 18.5207V7.90129V5.45064C21 4.09719 19.9926 3 18.75 3Z" stroke="#585C63" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><!>',
		1
	),
	$s = /* @__PURE__ */ W('<svg><!></svg>');
function dn(e, t) {
	const r = lr(t, ['children', '$$slots', '$$events', '$$legacy', '$$host']),
		n = lr(r, ['isDisabled', 'isHovered', 'isOpen']);
	J(t, !1);
	let i = y(t, 'isDisabled', 12, !1),
		o = y(t, 'isHovered', 12, !1),
		s = y(t, 'isOpen', 12, !1);
	var a = $s();
	sr(a, () => ({
		xmlns: 'http://www.w3.org/2000/svg',
		width: '24',
		height: '24',
		viewBox: '0 0 24 24',
		fill: 'none',
		...n
	}));
	var l = C(a);
	{
		var u = (d) => {
				var c = Ss(),
					b = ie(Ke(c));
				we(() =>
					Z(
						b,
						'd',
						`M${s() ? '8.18018' : '9.18018'} ${s() ? '5.45068' : '3.27222'}V${s() ? '18.5208' : '20.9203'}`
					)
				),
					L(d, c);
			},
			f = (d, c) => {
				{
					var b = (k) => {
							var S = It(),
								I = Ke(S);
							{
								var v = (h) => {
										var g = Is();
										Ve(), L(h, g);
									},
									m = (h) => {
										var g = Ns();
										Ve(), L(h, g);
									};
								oe(I, (h) => {
									s() ? h(v) : h(m, !1);
								});
							}
							L(k, S);
						},
						p = (k) => {
							var S = Ms(),
								I = ie(Ke(S));
							{
								var v = (h) => {
										var g = zs();
										L(h, g);
									},
									m = (h) => {
										var g = Ls();
										L(h, g);
									};
								oe(I, (h) => {
									s() ? h(v) : h(m, !1);
								});
							}
							L(k, S);
						};
					oe(
						d,
						(k) => {
							o() ? k(b) : k(p, !1);
						},
						c
					);
				}
			};
		oe(l, (d) => {
			i() ? d(u) : d(f, !1);
		});
	}
	return (
		E(a),
		L(e, a),
		K({
			get isDisabled() {
				return i();
			},
			set isDisabled(d) {
				i(d), _();
			},
			get isHovered() {
				return o();
			},
			set isHovered(d) {
				o(d), _();
			},
			get isOpen() {
				return s();
			},
			set isOpen(d) {
				s(d), _();
			}
		})
	);
}
G(dn, { isDisabled: {}, isHovered: {}, isOpen: {} }, [], [], !0);
var Ts = /* @__PURE__ */ ae(
		'<span class="icon svelte-1x8yhrg"><img alt="" aria-hidden="true" width="20" height="20"/></span>'
	),
	js = /* @__PURE__ */ ae(
		'<button type="button" class="menu-item svelte-1x8yhrg" role="menuitem"><!> <div class="menu-text svelte-1x8yhrg"> </div></button>'
	),
	As = /* @__PURE__ */ ae('<div tabindex="-1" role="menu"></div>'),
	Us = /* @__PURE__ */ ae(
		'<div style="position: relative; display: inline-block;"><button class="user-profile-button svelte-1x8yhrg" tabindex="0" aria-haspopup="menu"><div class="user-profile-container svelte-1x8yhrg"><div class="user-profile-icon svelte-1x8yhrg" part="user-profile-icon"><span class="user-initials svelte-1x8yhrg"> </span></div></div></button> <!></div>'
	);
const Os = {
	hash: 'svelte-1x8yhrg',
	code: `.user-profile-container.svelte-1x8yhrg,
  .user-profile-icon.svelte-1x8yhrg {display:flex;align-items:center;justify-content:center;}.user-profile-container.svelte-1x8yhrg,
  .user-profile-icon.svelte-1x8yhrg {display:flex;align-items:center;justify-content:center;}.user-profile-icon.svelte-1x8yhrg {background:var(--ai-color-blue-800);border-radius:var(--ai-size-4);box-sizing:border-box;height:var(--ai-size-40);padding:var(--ai-size-4);white-space:nowrap;width:var(--ai-size-40);}.user-initials.svelte-1x8yhrg {color:var(--ai-color-white);font-size:var(--ai-size-16);font-style:normal;font-weight:400;line-height:1;overflow:hidden;text-align:center;text-overflow:ellipsis;}.user-profile-button.svelte-1x8yhrg {background:none;border:none;padding:0;cursor:pointer;display:flex;justify-content:center;align-items:center;}.user-profile-button.svelte-1x8yhrg:focus {outline:var(--ai-size-2) solid var(--ai-color-blue-600);outline-offset:var(--ai-size-2);border-radius:var(--ai-size-4);}.user-profile-button.svelte-1x8yhrg:focus:not(:focus-visible) {outline:none;}.user-profile-container.svelte-1x8yhrg {width:var(--ai-size-40);height:var(--ai-size-40);aspect-ratio:1/1;}.profile-context-menu.svelte-1x8yhrg {display:flex;width:13.75rem;padding:var(--ai-size-12);flex-direction:column;align-items:flex-start;gap:var(--ai-size-2);border-radius:var(--ai-size-8);border:0.0625rem solid var(--ai-color-steel-200);background:var(--ai-color-white);box-shadow:0 0.1875rem var(--ai-size-6) 0 rgba(50, 54, 61, 0.20);position:absolute;z-index:var(--ai-layer-50);}.profile-context-menu.desktop-align.svelte-1x8yhrg {left:100%;top:auto;bottom:0;margin-left:var(--ai-size-8);
    /* aligns bottom of menu with bottom of button */transform:translateY(0);}.profile-context-menu.mobile-align.svelte-1x8yhrg {top:110%;right:0;left:auto;bottom:auto;margin-left:0;transform:none;}.menu-item.svelte-1x8yhrg {display:flex;align-items:center;gap:var(--ai-size-10);flex:1 0 0;width:100%;cursor:pointer;padding:var(--ai-size-8);background:none;border:none;box-shadow:none;outline:none;}.menu-item.svelte-1x8yhrg:focus {background:var(--ai-color-steel-200);border-radius:var(--ai-size-4);outline:var(--ai-size-2) solid var(--ai-color-blue-600);outline-offset:var(--ai-size-2);}.menu-item.svelte-1x8yhrg:focus:not(:focus-visible) {outline:none;}.menu-item.svelte-1x8yhrg:hover {background:var(--ai-color-steel-200);border-radius:var(--ai-size-4);}.icon.svelte-1x8yhrg {width:var(--ai-size-20);height:var(--ai-size-20);display:flex;align-items:center;justify-content:center;}.menu-text.svelte-1x8yhrg {color:var(--ai-color-black);font-size:var(--ai-size-14);font-style:normal;font-weight:400;line-height:130%;}`
};
function fn(e, t) {
	J(t, !1), vt(e, Os);
	let r = y(t, 'menuItems', 28, () => []),
		n = y(t, 'userMeta', 12),
		i = y(t, 'ariaLabel', 12, 'Open profile menu'),
		o = y(t, 'menuAlign', 12, 'desktop'),
		s = `profile-menu-${Math.random().toString(36).slice(2, 10)}`,
		a = /* @__PURE__ */ ot(!1);
	function l() {
		j(a, !w(a));
	}
	function u() {
		j(a, !1);
	}
	function f(M) {
		try {
			typeof M == 'string'
				? new Function('return ' + M)()()
				: typeof M == 'function'
					? M()
					: console.error('Invalid onClick type:', typeof M);
		} catch (P) {
			console.error('Error executing onClick function:', P);
		}
	}
	function d(M) {
		const P = M.relatedTarget;
		(!P || !P.closest('.profile-context-menu')) && u();
	}
	let c = /* @__PURE__ */ ot(),
		b = /* @__PURE__ */ ot([]),
		p = /* @__PURE__ */ ot([]);
	function k(M, P) {
		var X, q, N;
		if (M.key === 'ArrowDown') {
			M.preventDefault();
			const $ = (P + 1) % r().length;
			(X = w(b)[$]) == null || X.focus();
		} else if (M.key === 'ArrowUp') {
			M.preventDefault();
			const $ = (P - 1 + r().length) % r().length;
			(q = w(b)[$]) == null || q.focus();
		} else M.key === 'Escape' && (u(), (N = w(c)) == null || N.focus(), M.stopPropagation());
	}
	function S(M) {
		var P;
		w(a) && M.key === 'Escape' && (u(), (P = w(c)) == null || P.focus(), M.stopPropagation());
	}
	Jt(
		() => (
			window.addEventListener('keydown', S, !0),
			() => {
				window.removeEventListener('keydown', S, !0);
			}
		)
	),
		hi(
			() => w(p),
			() => {
				j(b, w(p).filter(Boolean));
			}
		),
		pi(),
		Za();
	var I = Us(),
		v = C(I),
		m = C(v),
		h = C(m),
		g = C(h),
		z = C(g, !0);
	E(g),
		E(h),
		E(m),
		E(v),
		it(
			v,
			(M) => j(c, M),
			() => w(c)
		);
	var A = ie(v, 2);
	{
		var Y = (M) => {
			var P = As();
			Ti(
				P,
				7,
				r,
				(X) => X.label,
				(X, q, N) => {
					var $ = js(),
						V = C($);
					{
						var se = (Q) => {
							var ue = Ts(),
								Fe = C(ue);
							E(ue), we(() => Z(Fe, 'src', (w(q), Ae(() => w(q).icon)))), L(Q, ue);
						};
						oe(V, (Q) => {
							w(q), Ae(() => w(q).icon) && Q(se);
						});
					}
					var ce = ie(V, 2),
						le = C(ce, !0);
					E(ce),
						E($),
						it(
							$,
							(Q, ue) => na(p, (w(p)[ue] = Q)),
							(Q) => {
								var ue;
								return (ue = w(p)) == null ? void 0 : ue[Q];
							},
							() => [w(N)]
						),
						we(() => Vt(le, (w(q), Ae(() => w(q).label)))),
						ee('click', $, () => {
							f(w(q).onClick), u();
						}),
						ee('keydown', $, (Q) => k(Q, w(N))),
						L(X, $);
				}
			),
				E(P),
				we(() => {
					me(
						P,
						1,
						`profile-context-menu ${o() === 'desktop' ? 'desktop-align' : 'mobile-align'}`,
						'svelte-1x8yhrg'
					),
						Z(P, 'id', s);
				}),
				L(M, P);
		};
		oe(A, (M) => {
			w(a) && M(Y);
		});
	}
	return (
		E(I),
		we(() => {
			Z(v, 'aria-label', i()),
				Z(v, 'aria-expanded', w(a)),
				Z(v, 'aria-controls', s),
				Vt(
					z,
					(rn(n()),
					Ae(() => {
						var M;
						return (M = n()) == null ? void 0 : M.initials;
					}))
				);
		}),
		ee('click', v, l),
		ee('blur', v, d),
		L(e, I),
		K({
			get menuItems() {
				return r();
			},
			set menuItems(M) {
				r(M), _();
			},
			get userMeta() {
				return n();
			},
			set userMeta(M) {
				n(M), _();
			},
			get ariaLabel() {
				return i();
			},
			set ariaLabel(M) {
				i(M), _();
			},
			get menuAlign() {
				return o();
			},
			set menuAlign(M) {
				o(M), _();
			}
		})
	);
}
G(fn, { menuItems: {}, userMeta: {}, ariaLabel: {}, menuAlign: {} }, [], [], !0);
const Ds = {};
function Nr(e, t) {
	t || console.warn(`[getAppUrls] Environment variable for ${e} is missing!`);
}
function Zi(e) {
	const t = Ds,
		r = '#',
		n = t.PUBLIC_CHAT_URL,
		i = t.PUBLIC_CONSOLE_URL,
		o = t.PUBLIC_API_URL,
		s = t.PUBLIC_DISCOVER_URL;
	return (
		Nr('chat', n),
		Nr('console', i),
		Nr('api', o),
		{
			chat: n || r,
			console: i || r,
			api: o || '/api',
			discover: s || '/discover'
		}
	);
}
var Hs = /* @__PURE__ */ W(
	'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M9.87891 7.51884C11.0505 6.49372 12.95 6.49372 14.1215 7.51884C15.2931 8.54397 15.2931 10.206 14.1215 11.2312C13.9176 11.4096 13.6917 11.5569 13.4513 11.6733C12.7056 12.0341 12.0002 12.6716 12.0002 13.5V14.25M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12ZM12 17.25H12.0075V17.2575H12V17.25Z" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg>'
);
function Ji(e, t) {
	J(t, !1);
	let r = y(t, 'isHovered', 12, !1);
	var n = Hs(),
		i = C(n);
	return (
		E(n),
		we(() => Z(i, 'stroke', r() ? '#000' : '#585C63')),
		L(e, n),
		K({
			get isHovered() {
				return r();
			},
			set isHovered(o) {
				r(o), _();
			}
		})
	);
}
G(Ji, { isHovered: {} }, [], [], !0);
function Ps(e, t, r, n) {
	var i;
	t() || (Te.toggleExpanded(), (i = r()) == null || i({ expanded: n() }));
}
var Vs =
		// Set navigation flag immediately to prevent header restoration
		// Clear header state for main navigation items (including discover)
		// Clear the store value immediately
		// Set default header from first submenu item after a short delay
		// Delay to ensure page content is loaded
		// Clear header state for Discover page
		(e, t) => t('discover'),
	Rs = (e, t, r) => {
		t(e, 'discover'), r(e, 0);
	},
	Bs = (e, t) => t('chat'),
	Fs = (e, t, r) => {
		t(e, 'chat'), r(e, 1);
	},
	Ys = (e, t) => t('console'),
	qs = (e, t, r) => {
		t(e, 'console'), r(e, 2);
	},
	Zs = (e, t) => t('api'),
	Js = (e, t, r) => {
		t(e, 'api'), r(e, 3);
	},
	Ks = /* @__PURE__ */ ae(
		'<div class="nav-items svelte-1d9jpab" part="nav-items"><a part="discover-item"><div class="icon-container svelte-1d9jpab" part="discover-icon-container"><div class="icon-wrapper svelte-1d9jpab"><!></div></div> <span class="nav-text svelte-1d9jpab">Discover</span></a> <a part="chat-item"><div class="icon-container svelte-1d9jpab" part="chat-icon-container"><div class="icon-wrapper svelte-1d9jpab"><!></div></div> <span class="nav-text svelte-1d9jpab">Chat</span></a> <a part="console-item"><div class="icon-container svelte-1d9jpab" part="console-icon-container"><div class="icon-wrapper svelte-1d9jpab"><!></div></div> <span class="nav-text svelte-1d9jpab">Console</span></a> <a part="api-item"><div class="icon-container svelte-1d9jpab" part="api-icon-container"><div class="icon-wrapper svelte-1d9jpab"><!></div></div> <span class="nav-text svelte-1d9jpab">API</span></a></div>'
	),
	Ws = /* @__PURE__ */ ae(
		'<div class="nav-footer svelte-1d9jpab" part="nav-footer"><div class="nav-footer-icon-container svelte-1d9jpab"><button type="button" class="nav-footer-icon-inner svelte-1d9jpab" tabindex="0" aria-label="Help"><div class="nav-footer-icon-svg-wrapper svelte-1d9jpab"><!></div></button></div> <div class="nav-footer-profile-container svelte-1d9jpab"><!></div></div>'
	),
	Gs = /* @__PURE__ */ ae(
		'<div class="drawer-content svelte-1d9jpab" part="drawer-content"><div class="logo-placeholder svelte-1d9jpab" part="logo-placeholder"></div> <nav aria-label="Sub navigation" part="sub-navigation"><!></nav></div>'
	),
	Xs = /* @__PURE__ */ ae(
		'<div part="side-nav-container"><div class="nav-content svelte-1d9jpab" part="nav-content"><div class="nav-main svelte-1d9jpab" part="nav-main"><div class="menu-section svelte-1d9jpab" part="menu-section"><button><div class="icon-container svelte-1d9jpab" part="icon-container"><div class="icon-wrapper svelte-1d9jpab"><!></div></div></button></div> <!></div> <!></div> <div part="expanded-content"><!></div></div>'
	);
const Qs = {
	hash: 'svelte-1d9jpab',
	code: `.expanded-content.drawer-anim.svelte-1d9jpab {max-width:0;overflow:hidden;pointer-events:none;transition:max-width 0.2s cubic-bezier(.4, 0, .2, 1);}.expanded-content.drawer-anim.open.svelte-1d9jpab {max-width:12.5rem;pointer-events:auto;}.drawer-content.svelte-1d9jpab {width:12.5rem;height:100%;}.logo-placeholder.svelte-1d9jpab {height:var(--ai-size-72);}.menu-button.svelte-1d9jpab {display:flex;align-items:center;justify-content:center;border:none;background:none;cursor:pointer;border-radius:var(--ai-size-8);transition:background-color 0.2s ease;}.menu-button.svelte-1d9jpab:disabled,
  .menu-button.disabled.svelte-1d9jpab {cursor:not-allowed;opacity:0.5;background-color:transparent;}.side-nav-container.svelte-1d9jpab {display:flex;flex-shrink:0;position:relative;z-index:var(--ai-layer-2);height:100%;}.side-nav-container.expanded.svelte-1d9jpab {width:16.875rem;min-width:12.5rem;max-width:16.875rem;height:100%;}.expanded-content.svelte-1d9jpab {display:flex;width:12.5rem;opacity:1;flex:none;height:100%;background:var(--ai-color-steel-100);border-right:0.0625rem solid var(--ai-color-steel-100);overflow:hidden;flex-direction:column;}.side-nav-container.expanded.svelte-1d9jpab .expanded-content:where(.svelte-1d9jpab) {width:12.5rem;opacity:1;overflow-y:auto;}.side-nav-container.svelte-1d9jpab:not(.expanded) .expanded-content:where(.svelte-1d9jpab) {width:0;opacity:0;}.nav-content.svelte-1d9jpab {display:flex;width:var(--ai-size-72);height:100%;padding-bottom:var(--ai-size-16);flex-direction:column;align-items:flex-start;gap:var(--ai-size-4);flex:none;background:var(--ai-color-steel-200);border-right:0.0625rem solid var(--ai-color-steel-200);}.nav-main.svelte-1d9jpab {display:flex;flex-direction:column;align-items:flex-start;gap:var(--ai-size-4);flex:1 0 0;align-self:stretch;}.nav-footer.svelte-1d9jpab {display:flex;padding:var(--ai-size-8) var(--ai-size-4);flex-direction:column;justify-content:center;align-items:center;gap:var(--ai-size-4);align-self:stretch;margin-top:auto;}.menu-section.svelte-1d9jpab {display:flex;padding:var(--ai-size-4);justify-content:center;align-items:center;align-self:stretch;}.menu-button.svelte-1d9jpab {display:flex;padding:var(--ai-size-4);justify-content:center;align-items:center;background:none;border:none;cursor:pointer;opacity:1;transition:opacity 0.2s ease;will-change:opacity;}.menu-button.disabled.svelte-1d9jpab {cursor:not-allowed;opacity:0.5;will-change:opacity;}.menu-button.svelte-1d9jpab:focus,
  .nav-item.svelte-1d9jpab:focus {outline:none;}.menu-button.svelte-1d9jpab:focus .icon-container:where(.svelte-1d9jpab),
  .nav-item.svelte-1d9jpab:focus .icon-container:where(.svelte-1d9jpab) {outline:var(--ai-size-2) solid var(--ai-color-blue-600);outline-offset:var(--ai-size-2);border-radius:var(--ai-size-4);}

  /* Hide focus outline on click */.menu-button.svelte-1d9jpab:focus:not(:focus-visible) .icon-container:where(.svelte-1d9jpab),
  .nav-item.svelte-1d9jpab:focus:not(:focus-visible) .icon-container:where(.svelte-1d9jpab) {outline:none;}.nav-items.svelte-1d9jpab {display:flex;width:var(--ai-size-72);flex-direction:column;align-items:flex-start;gap:var(--ai-size-4);}.nav-item.svelte-1d9jpab {display:flex;height:4.125rem;padding:var(--ai-size-4);flex-direction:column;justify-content:center;align-items:center;gap:var(--ai-size-2);align-self:stretch;cursor:pointer;text-decoration:none;color:inherit;position:relative;flex-shrink:0;}.icon-container.svelte-1d9jpab {display:flex;width:var(--ai-size-40);height:var(--ai-size-40);padding:var(--ai-size-8);justify-content:center;align-items:center;flex-shrink:0;position:relative;contain:layout style;transition:background-color 0.3s ease,
    border-radius 0.3s ease;}.icon-wrapper.svelte-1d9jpab {display:flex;width:var(--ai-size-28);height:var(--ai-size-28);justify-content:center;align-items:center;flex-shrink:0;aspect-ratio:1/1;position:relative;overflow:hidden;transition:transform 0.3s ease;}.icon-wrapper.svelte-1d9jpab svg {width:var(--ai-size-24);height:var(--ai-size-24);flex-shrink:0;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);transition:all 0.3s ease;}.icon-wrapper.svelte-1d9jpab svg path {stroke-width:1.5;vector-effect:non-scaling-stroke;transition:stroke 0.3s ease,
    fill 0.3s ease,
    stroke-width 0.3s ease;}.nav-text.svelte-1d9jpab {font-size:var(--ai-size-12);text-align:center;color:var(--ai-color-neutral-900);line-height:1.3;}.menu-button.disabled.svelte-1d9jpab:hover .icon-container:where(.svelte-1d9jpab),
  .menu-button.svelte-1d9jpab:disabled:hover .icon-container:where(.svelte-1d9jpab) {background:none !important;border-radius:var(--ai-size-4);}.nav-item.hovered.svelte-1d9jpab .icon-container:where(.svelte-1d9jpab),
  .nav-item.svelte-1d9jpab:active .icon-container:where(.svelte-1d9jpab),
  .nav-item.selected.svelte-1d9jpab .icon-container:where(.svelte-1d9jpab),
  .nav-item.selected.hovered.svelte-1d9jpab .icon-container:where(.svelte-1d9jpab),
  .menu-button.svelte-1d9jpab:hover .icon-container:where(.svelte-1d9jpab),
  .menu-button.svelte-1d9jpab:active .icon-container:where(.svelte-1d9jpab) {background-color:var(--ai-color-steel-300);border-radius:var(--ai-size-4);}.nav-item.svelte-1d9jpab {flex:1;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:var(--ai-size-4);text-decoration:none;color:var(--ai-color-neutral-600);transition:color 0.2s ease;height:100%;}.nav-item.svelte-1d9jpab:hover {color:var(--ai-color-neutral-700);}.nav-footer-icon-container.svelte-1d9jpab {display:flex;padding:0 var(--ai-size-4);flex-direction:column;justify-content:center;align-items:center;gap:var(--ai-size-4);align-self:stretch;}.nav-footer-profile-container.svelte-1d9jpab {display:flex;padding:var(--ai-size-8) var(--ai-size-4);flex-direction:column;justify-content:center;align-items:center;gap:var(--ai-size-4);align-self:stretch;}.nav-footer-icon-inner.svelte-1d9jpab {display:flex;width:var(--ai-size-40);height:var(--ai-size-40);padding:var(--ai-size-8);justify-content:center;align-items:center;border-radius:var(--ai-size-4);background:none;border:none;cursor:pointer;transition:background-color 0.3s ease, border-radius 0.3s ease;}.nav-footer-icon-inner.svelte-1d9jpab:hover,
  .nav-footer-icon-inner.svelte-1d9jpab:focus {background-color:var(--ai-color-steel-300);border-radius:var(--ai-size-4);}.nav-footer-icon-inner.svelte-1d9jpab:focus {outline:none;}.nav-footer-icon-inner.svelte-1d9jpab:focus-visible {outline:var(--ai-size-2) solid var(--ai-color-blue-600);outline-offset:var(--ai-size-2);border-radius:var(--ai-size-4);}.nav-footer-icon-svg-wrapper.svelte-1d9jpab {width:var(--ai-size-24);height:var(--ai-size-24);flex-shrink:0;display:flex;align-items:center;justify-content:center;}`
};
function Ki(e, t) {
	J(t, !0), vt(e, Qs);
	const [r, n] = Kt(),
		i = () => Ie(ht, '$selectedItem', r),
		o = () => Ie($e, '$isExpanded', r),
		s = () => Ie(an, '$isMenuButtonDisabled', r),
		a = Zi(),
		l = y(t, 'ssrSelectedItem', 7, void 0),
		u = y(t, 'onNavToggle', 7, void 0),
		f = y(t, 'onNavItemClick', 7, void 0),
		d = y(t, 'children', 7),
		c = y(t, 'showAppIcons', 7, !0),
		b = y(t, 'profileMenuData', 7),
		p = y(t, 'apiUrl', 7),
		k = y(t, 'chatUrl', 7),
		S = y(t, 'consoleUrl', 7),
		I = y(t, 'discoverUrl', 7);
	let v = !1,
		m,
		h = /* @__PURE__ */ fe(null),
		g = /* @__PURE__ */ fe(!1),
		z = /* @__PURE__ */ fe(!1),
		A = /* @__PURE__ */ fe(!1),
		Y = /* @__PURE__ */ fe(null),
		M = /* @__PURE__ */ fe(null),
		P = /* @__PURE__ */ fe(null),
		X = /* @__PURE__ */ fe(null);
	function q(x, B) {
		return v ? x : B;
	}
	function N(x) {
		return q(i() === x, l() === x);
	}
	function $(x) {
		return w(h) === x;
	}
	const V = /* @__PURE__ */ te(() => () => q(o(), l() ? l() !== 'discover' : !0)),
		se = /* @__PURE__ */ te(() => () => q(s(), l() ? l() === 'discover' : !1)),
		ce = /* @__PURE__ */ te(() => () => q(o(), l() ? l() !== 'discover' : !0)),
		le = /* @__PURE__ */ te(() => () => w(z) && w(g) && !w(se)());
	Jt(() => {
		if (
			((v = !0),
			document.documentElement.classList.remove('nav-initially-expanded'),
			document.documentElement.style.removeProperty('--initial-main-top-padding'),
			m && m.classList.add('hydrated'),
			typeof window < 'u')
		) {
			const x = window.matchMedia('(hover: hover)');
			j(z, x.matches, !0),
				x.addEventListener('change', (R) => {
					j(z, R.matches, !0);
				});
			const B = (R) => {
				const ne = R,
					{ isMobile: xe } = ne.detail;
				if (xe) Te.setExpanded(!1);
				else {
					const De = i();
					De && De !== 'discover' ? Te.setExpanded(!0) : Te.setExpanded(!1);
				}
			};
			window.addEventListener('layoutTransition', B);
			const T = (R) => {
				const ne = document.activeElement;
				R.key === 'Escape' &&
					o() &&
					!(ne && ne.closest('.profile-context-menu')) &&
					Te.setExpanded(!1);
			};
			return (
				window.addEventListener('keydown', T),
				() => {
					window.removeEventListener('layoutTransition', B),
						window.removeEventListener('keydown', T);
				}
			);
		}
	});
	function Q(x) {
		var B;
		if (
			(x !== 'discover' &&
				typeof window < 'u' &&
				sessionStorage.setItem('navigatingToMainNav', 'true'),
			Te.setSelectedItem(x),
			x !== 'discover')
		)
			try {
				Promise.resolve()
					.then(() => ur)
					.then(({ selectedSubNavItemTitle: T }) => {
						T.set(''),
							typeof window < 'u' &&
								(sessionStorage.removeItem('subNav-selectedTitle'),
								sessionStorage.removeItem('subNav-selectedId')),
							setTimeout(() => {
								const R = document.getElementById('main-content');
								if (R) {
									const ne = R.querySelector('h2[id], h3[id], h4[id], h5[id], h6[id]');
									if (ne && ne.textContent) {
										const xe = ne.textContent.trim();
										T.set(xe),
											typeof window < 'u' &&
												sessionStorage.setItem('subNav-selectedTitle', JSON.stringify(xe));
									}
								}
							}, 200);
					})
					.catch((T) => {
						console.log('[DesktopSideNav] Error clearing header state:', T);
					});
			} catch (T) {
				console.log('[DesktopSideNav] Import failed for clearing state:', T);
			}
		else
			try {
				Promise.resolve()
					.then(() => ur)
					.then(({ selectedSubNavItemTitle: T }) => {
						T.set(''),
							typeof window < 'u' &&
								(sessionStorage.removeItem('subNav-selectedTitle'),
								sessionStorage.removeItem('subNav-selectedId'));
					})
					.catch((T) => {
						console.log('[DesktopSideNav] Error clearing header state for Discover:', T);
					});
			} catch (T) {
				console.log('[DesktopSideNav] Import failed for clearing Discover state:', T);
			}
		(B = f()) == null || B({ item: x, expanded: o() });
	}
	function ue(x, B) {
		(x.key === 'Enter' || x.key === ' ') && (x.preventDefault(), Q(B));
	}
	function Fe(x, B) {
		var ne, xe, De, pt;
		const T = [w(Y), w(M), w(P), w(X)],
			R = T.length;
		if (R)
			if (x.key === 'ArrowDown') {
				x.preventDefault();
				const de = (B + 1) % R;
				(ne = T[de]) == null || ne.focus();
			} else if (x.key === 'ArrowUp') {
				x.preventDefault();
				const de = (B - 1 + R) % R;
				(xe = T[de]) == null || xe.focus();
			} else
				x.key === 'Home'
					? (x.preventDefault(), (De = T[0]) == null || De.focus())
					: x.key === 'End' && (x.preventDefault(), (pt = T[R - 1]) == null || pt.focus());
	}
	var Nt = Xs();
	let vn;
	var wr = C(Nt),
		_r = C(wr),
		yr = C(_r),
		Le = C(yr);
	let hn;
	Le.__click = [Ps, s, u, o];
	var pn = C(Le),
		bn = C(pn),
		eo = C(bn);
	const to = /* @__PURE__ */ te(() => w(le)()),
		ro = /* @__PURE__ */ te(() => w(se)()),
		no = /* @__PURE__ */ te(() => w(ce)());
	dn(eo, {
		get isHovered() {
			return w(to);
		},
		get isDisabled() {
			return w(ro);
		},
		get isOpen() {
			return w(no);
		}
	}),
		E(bn),
		E(pn),
		E(Le),
		E(yr);
	var io = ie(yr, 2);
	{
		var oo = (x) => {
			var B = Ks(),
				T = C(B);
			let R;
			(T.__click = [Vs, Q]), (T.__keydown = [Rs, ue, Fe]);
			var ne = C(T),
				xe = C(ne),
				De = C(xe);
			const pt = /* @__PURE__ */ te(() => N('discover'));
			sn(De, {
				get isSelected() {
					return w(pt);
				}
			}),
				E(xe),
				E(ne),
				Ve(2),
				E(T),
				it(
					T,
					(Ye) => j(Y, Ye),
					() => w(Y)
				);
			var de = ie(T, 2);
			let Gt;
			(de.__click = [Bs, Q]), (de.__keydown = [Fs, ue, Fe]);
			var rt = C(de),
				mn = C(rt),
				fo = C(mn);
			const vo = /* @__PURE__ */ te(() => N('chat'));
			ln(fo, {
				get isSelected() {
					return w(vo);
				}
			}),
				E(mn),
				E(rt),
				Ve(2),
				E(de),
				it(
					de,
					(Ye) => j(M, Ye),
					() => w(M)
				);
			var Me = ie(de, 2);
			let wn;
			(Me.__click = [Ys, Q]), (Me.__keydown = [qs, ue, Fe]);
			var _n = C(Me),
				yn = C(_n),
				ho = C(yn);
			const po = /* @__PURE__ */ te(() => N('console'));
			un(ho, {
				get isSelected() {
					return w(po);
				}
			}),
				E(yn),
				E(_n),
				Ve(2),
				E(Me),
				it(
					Me,
					(Ye) => j(P, Ye),
					() => w(P)
				);
			var He = ie(Me, 2);
			let kn;
			(He.__click = [Zs, Q]), (He.__keydown = [Js, ue, Fe]);
			var xn = C(He),
				En = C(xn),
				bo = C(En);
			const go = /* @__PURE__ */ te(() => N('api'));
			cn(bo, {
				get isSelected() {
					return w(go);
				}
			}),
				E(En),
				E(xn),
				Ve(2),
				E(He),
				it(
					He,
					(Ye) => j(X, Ye),
					() => w(X)
				),
				E(B),
				we(
					(Ye, mo, wo, _o, yo, ko, xo, Eo) => {
						Z(T, 'href', I() || a.discover),
							(R = me(T, 1, 'nav-item discover-item svelte-1d9jpab', null, R, Ye)),
							Z(T, 'aria-current', mo),
							Z(de, 'href', k() || a.chat),
							(Gt = me(de, 1, 'nav-item chat-item svelte-1d9jpab', null, Gt, wo)),
							Z(de, 'aria-current', _o),
							Z(Me, 'href', S() || a.console),
							(wn = me(Me, 1, 'nav-item console-item svelte-1d9jpab', null, wn, yo)),
							Z(Me, 'aria-current', ko),
							Z(He, 'href', p() || a.api),
							(kn = me(He, 1, 'nav-item api-item svelte-1d9jpab', null, kn, xo)),
							Z(He, 'aria-current', Eo);
					},
					[
						() => ({
							selected: N('discover'),
							hovered: $('discover')
						}),
						() => (N('discover') ? 'page' : void 0),
						() => ({
							selected: N('chat'),
							hovered: $('chat')
						}),
						() => (N('chat') ? 'page' : void 0),
						() => ({
							selected: N('console'),
							hovered: $('console')
						}),
						() => (N('console') ? 'page' : void 0),
						() => ({ selected: N('api'), hovered: $('api') }),
						() => (N('api') ? 'page' : void 0)
					]
				),
				ee('mouseenter', T, () => j(h, 'discover')),
				ee('mouseleave', T, () => j(h, null)),
				ee('mouseenter', de, () => j(h, 'chat')),
				ee('mouseleave', de, () => j(h, null)),
				ee('mouseenter', Me, () => j(h, 'console')),
				ee('mouseleave', Me, () => j(h, null)),
				ee('mouseenter', He, () => j(h, 'api')),
				ee('mouseleave', He, () => j(h, null)),
				L(x, B);
		};
		oe(io, (x) => {
			c() && x(oo);
		});
	}
	E(_r);
	var ao = ie(_r, 2);
	{
		var so = (x) => {
			var B = Ws(),
				T = C(B),
				R = C(T),
				ne = C(R),
				xe = C(ne);
			Ji(xe, {
				get isHovered() {
					return w(A);
				}
			}),
				E(ne),
				E(R),
				E(T);
			var De = ie(T, 2),
				pt = C(De);
			const de = /* @__PURE__ */ te(() => {
					var rt;
					return (rt = b()) == null ? void 0 : rt.userMeta;
				}),
				Gt = /* @__PURE__ */ te(() => {
					var rt;
					return (rt = b()) == null ? void 0 : rt.menuItems;
				});
			fn(pt, {
				get userMeta() {
					return w(de);
				},
				get menuItems() {
					return w(Gt);
				},
				ariaLabel: 'Open profile menu'
			}),
				E(De),
				E(B),
				ee('mouseenter', R, () => j(A, !0)),
				ee('mouseleave', R, () => j(A, !1)),
				ee('focus', R, () => j(A, !0)),
				ee('blur', R, () => j(A, !1)),
				L(x, B);
		};
		oe(ao, (x) => {
			b() && x(so);
		});
	}
	E(wr);
	var kr = ie(wr, 2);
	let gn;
	var lo = C(kr);
	{
		var uo = (x) => {
			var B = Gs(),
				T = ie(C(B), 2),
				R = C(T);
			Dr(R, () => d() ?? Ne), E(T), E(B), L(x, B);
		};
		oe(lo, (x) => {
			w(V)() && x(uo);
		});
	}
	E(kr),
		E(Nt),
		it(
			Nt,
			(x) => (m = x),
			() => m
		),
		we(
			(x, B, T, R, ne) => {
				(vn = me(Nt, 1, 'side-nav-container svelte-1d9jpab', null, vn, x)),
					(hn = me(Le, 1, 'menu-button svelte-1d9jpab', null, hn, B)),
					(Le.disabled = s()),
					Z(Le, 'aria-label', T),
					Z(Le, 'aria-expanded', R),
					(gn = me(kr, 1, 'expanded-content drawer-anim svelte-1d9jpab', null, gn, ne));
			},
			[
				() => ({ expanded: w(V)() }),
				() => ({ disabled: s() }),
				() => (w(V)() ? 'Close navigation menu' : 'Open navigation menu'),
				() => (w(V)() ? 'true' : 'false'),
				() => ({ open: w(V)() })
			]
		),
		ee('mouseenter', Le, () => j(g, !0)),
		ee('mouseleave', Le, () => j(g, !1)),
		ee('focus', Le, () => j(g, !0)),
		ee('blur', Le, () => j(g, !1)),
		L(e, Nt);
	var co = K({
		get ssrSelectedItem() {
			return l();
		},
		set ssrSelectedItem(x = void 0) {
			l(x), _();
		},
		get onNavToggle() {
			return u();
		},
		set onNavToggle(x = void 0) {
			u(x), _();
		},
		get onNavItemClick() {
			return f();
		},
		set onNavItemClick(x = void 0) {
			f(x), _();
		},
		get children() {
			return d();
		},
		set children(x) {
			d(x), _();
		},
		get showAppIcons() {
			return c();
		},
		set showAppIcons(x = !0) {
			c(x), _();
		},
		get profileMenuData() {
			return b();
		},
		set profileMenuData(x) {
			b(x), _();
		},
		get apiUrl() {
			return p();
		},
		set apiUrl(x) {
			p(x), _();
		},
		get chatUrl() {
			return k();
		},
		set chatUrl(x) {
			k(x), _();
		},
		get consoleUrl() {
			return S();
		},
		set consoleUrl(x) {
			S(x), _();
		},
		get discoverUrl() {
			return I();
		},
		set discoverUrl(x) {
			I(x), _();
		}
	});
	return n(), co;
}
Zt(['click', 'keydown']);
G(
	Ki,
	{
		ssrSelectedItem: {},
		onNavToggle: {},
		onNavItemClick: {},
		children: {},
		showAppIcons: {},
		profileMenuData: {},
		apiUrl: {},
		chatUrl: {},
		consoleUrl: {},
		discoverUrl: {}
	},
	[],
	[],
	!0
);
function el(e, t) {
	J(t, !0);
	let r = y(t, 'ssrSelectedItem', 7),
		n = y(t, 'onNavToggle', 7),
		i = y(t, 'onNavItemClick', 7),
		o = y(t, 'showAppIcons', 7, !0),
		s = y(t, 'profileMenuData', 7),
		a = y(t, 'apiUrl', 7),
		l = y(t, 'chatUrl', 7),
		u = y(t, 'consoleUrl', 7),
		f = y(t, 'discoverUrl', 7);
	return (
		Ki(e, {
			get ssrSelectedItem() {
				return r();
			},
			get onNavToggle() {
				return n();
			},
			get onNavItemClick() {
				return i();
			},
			get showAppIcons() {
				return o();
			},
			get profileMenuData() {
				return s();
			},
			get apiUrl() {
				return a();
			},
			get chatUrl() {
				return l();
			},
			get consoleUrl() {
				return u();
			},
			get discoverUrl() {
				return f();
			},
			children: (d, c) => {
				var b = It(),
					p = Ke(b);
				gr(p, t, 'default', {}), L(d, b);
			},
			$$slots: { default: !0 }
		}),
		K({
			get ssrSelectedItem() {
				return r();
			},
			set ssrSelectedItem(d) {
				r(d), _();
			},
			get onNavToggle() {
				return n();
			},
			set onNavToggle(d) {
				n(d), _();
			},
			get onNavItemClick() {
				return i();
			},
			set onNavItemClick(d) {
				i(d), _();
			},
			get showAppIcons() {
				return o();
			},
			set showAppIcons(d = !0) {
				o(d), _();
			},
			get profileMenuData() {
				return s();
			},
			set profileMenuData(d) {
				s(d), _();
			},
			get apiUrl() {
				return a();
			},
			set apiUrl(d) {
				a(d), _();
			},
			get chatUrl() {
				return l();
			},
			set chatUrl(d) {
				l(d), _();
			},
			get consoleUrl() {
				return u();
			},
			set consoleUrl(d) {
				u(d), _();
			},
			get discoverUrl() {
				return f();
			},
			set discoverUrl(d) {
				f(d), _();
			}
		})
	);
}
customElements.define(
	'graymatter-desktop-side-nav',
	G(
		el,
		{
			ssrSelectedItem: {},
			onNavToggle: {},
			onNavItemClick: {},
			showAppIcons: {},
			profileMenuData: {},
			apiUrl: {},
			chatUrl: {},
			consoleUrl: {},
			discoverUrl: {}
		},
		['default'],
		[],
		!0
	)
);
var tl = /* @__PURE__ */ W(
	'<svg><g><path d="M57.5791 20.4177C57.5791 15.5645 53.7309 11.6187 48.9988 11.6187H40.188C38.6688 11.6187 37.4304 10.3567 37.4304 8.80197C37.4304 7.24729 38.6658 5.98522 40.188 5.98522H56.2373V0H38.8757C34.1466 0 30.2983 3.94877 30.2983 8.80197C30.2983 13.6552 34.1466 17.6039 38.8757 17.6039H47.6865C49.2057 17.6039 50.4442 18.866 50.4442 20.4207C50.4442 21.9754 49.2087 23.2374 47.6865 23.2374H31.0284V29.2197H48.9988C53.7309 29.2197 57.5791 25.2709 57.5791 20.4207V20.4177Z"></path><path d="M82.1466 24.7153L82.188 24.7685L83.6747 29.2197H90.6914L80.917 0H68.3525L58.5781 29.2167H65.5949L67.0993 24.7153H82.1466ZM69.186 18.4818L73.3653 5.98522H75.9013L75.919 6.03547L80.1161 18.5793H69.1535L69.186 18.4818Z"></path><path d="M27.198 0H20.5626V15.8158C20.5626 21.4345 16.9626 23.4355 13.5961 23.4355C9.03842 23.4355 6.62956 20.799 6.62956 15.8158V0H0V17.0069C0 20.6985 1.46305 24.0532 4.11724 26.4532C6.64729 28.7409 10.0138 30 13.599 30C20.3586 30 27.198 25.5369 27.198 17.0039V0Z"></path><path d="M101.708 9.78915H93.8662V29.2197H101.708V9.78915Z"></path><path d="M101.823 0H93.8662V5.98227H101.823V0Z"></path></g></svg>'
);
function Rt(e, t) {
	const r = lr(t, ['children', '$$slots', '$$events', '$$legacy', '$$host']),
		n = lr(r, ['isInverted', 'width', 'height']);
	J(t, !1);
	const i = /* @__PURE__ */ ot();
	let o = y(t, 'isInverted', 12, !1),
		s = y(t, 'width', 12),
		a = y(t, 'height', 12);
	const l = 102,
		u = 30;
	s(s() || l),
		a(a() || u),
		hi(
			() => rn(o()),
			() => {
				j(i, o() ? 'var(--ai-color-white)' : 'var(--ai-color-black)');
			}
		),
		pi();
	var f = tl();
	return (
		sr(f, () => ({
			width: s(),
			height: a(),
			viewBox: '0 0 102 30',
			fill: 'none',
			xmlns: 'http://www.w3.org/2000/svg',
			style: `fill: ${w(i) ?? ''};`,
			...n
		})),
		L(e, f),
		K({
			get isInverted() {
				return o();
			},
			set isInverted(d) {
				o(d), _();
			},
			get width() {
				return s();
			},
			set width(d) {
				s(d), _();
			},
			get height() {
				return a();
			},
			set height(d) {
				a(d), _();
			}
		})
	);
}
G(Rt, { isInverted: {}, width: {}, height: {} }, [], [], !0);
function rl(e, t) {
	J(t, !0);
	let r = y(t, 'isInverted', 7, !1),
		n = y(t, 'width', 7, void 0),
		i = y(t, 'height', 7, void 0),
		o = /* @__PURE__ */ on(t, [
			'$$slots',
			'$$events',
			'$$legacy',
			'$$host',
			'isInverted',
			'width',
			'height'
		]);
	return (
		Rt(
			e,
			Pi(
				{
					get isInverted() {
						return r();
					},
					get width() {
						return n();
					},
					get height() {
						return i();
					}
				},
				() => o
			)
		),
		K({
			get isInverted() {
				return r();
			},
			set isInverted(s = !1) {
				r(s), _();
			},
			get width() {
				return n();
			},
			set width(s = void 0) {
				n(s), _();
			},
			get height() {
				return i();
			},
			set height(s = void 0) {
				i(s), _();
			}
		})
	);
}
customElements.define(
	'graymatter-logo',
	G(
		rl,
		{
			isInverted: { attribute: 'is-inverted', reflect: !0, type: 'Boolean' },
			width: { attribute: 'width', reflect: !0, type: 'Number' },
			height: { attribute: 'height', reflect: !0, type: 'Number' }
		},
		[],
		[],
		!0
	)
);
function nl(e) {
	return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, 'default') ? e.default : e;
}
var er = { exports: {} },
	il = er.exports,
	Rn;
function ol() {
	return (
		Rn ||
			((Rn = 1),
			(function (e, t) {
				(function (r, n, i) {
					(e.exports = i()), (e.exports.default = i());
				})('slugify', il, function () {
					var r = JSON.parse(
							`{"$":"dollar","%":"percent","&":"and","<":"less",">":"greater","|":"or","¢":"cent","£":"pound","¤":"currency","¥":"yen","©":"(c)","ª":"a","®":"(r)","º":"o","À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","Æ":"AE","Ç":"C","È":"E","É":"E","Ê":"E","Ë":"E","Ì":"I","Í":"I","Î":"I","Ï":"I","Ð":"D","Ñ":"N","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","Ù":"U","Ú":"U","Û":"U","Ü":"U","Ý":"Y","Þ":"TH","ß":"ss","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","æ":"ae","ç":"c","è":"e","é":"e","ê":"e","ë":"e","ì":"i","í":"i","î":"i","ï":"i","ð":"d","ñ":"n","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","ù":"u","ú":"u","û":"u","ü":"u","ý":"y","þ":"th","ÿ":"y","Ā":"A","ā":"a","Ă":"A","ă":"a","Ą":"A","ą":"a","Ć":"C","ć":"c","Č":"C","č":"c","Ď":"D","ď":"d","Đ":"DJ","đ":"dj","Ē":"E","ē":"e","Ė":"E","ė":"e","Ę":"e","ę":"e","Ě":"E","ě":"e","Ğ":"G","ğ":"g","Ģ":"G","ģ":"g","Ĩ":"I","ĩ":"i","Ī":"i","ī":"i","Į":"I","į":"i","İ":"I","ı":"i","Ķ":"k","ķ":"k","Ļ":"L","ļ":"l","Ľ":"L","ľ":"l","Ł":"L","ł":"l","Ń":"N","ń":"n","Ņ":"N","ņ":"n","Ň":"N","ň":"n","Ō":"O","ō":"o","Ő":"O","ő":"o","Œ":"OE","œ":"oe","Ŕ":"R","ŕ":"r","Ř":"R","ř":"r","Ś":"S","ś":"s","Ş":"S","ş":"s","Š":"S","š":"s","Ţ":"T","ţ":"t","Ť":"T","ť":"t","Ũ":"U","ũ":"u","Ū":"u","ū":"u","Ů":"U","ů":"u","Ű":"U","ű":"u","Ų":"U","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","ź":"z","Ż":"Z","ż":"z","Ž":"Z","ž":"z","Ə":"E","ƒ":"f","Ơ":"O","ơ":"o","Ư":"U","ư":"u","ǈ":"LJ","ǉ":"lj","ǋ":"NJ","ǌ":"nj","Ș":"S","ș":"s","Ț":"T","ț":"t","ə":"e","˚":"o","Ά":"A","Έ":"E","Ή":"H","Ί":"I","Ό":"O","Ύ":"Y","Ώ":"W","ΐ":"i","Α":"A","Β":"B","Γ":"G","Δ":"D","Ε":"E","Ζ":"Z","Η":"H","Θ":"8","Ι":"I","Κ":"K","Λ":"L","Μ":"M","Ν":"N","Ξ":"3","Ο":"O","Π":"P","Ρ":"R","Σ":"S","Τ":"T","Υ":"Y","Φ":"F","Χ":"X","Ψ":"PS","Ω":"W","Ϊ":"I","Ϋ":"Y","ά":"a","έ":"e","ή":"h","ί":"i","ΰ":"y","α":"a","β":"b","γ":"g","δ":"d","ε":"e","ζ":"z","η":"h","θ":"8","ι":"i","κ":"k","λ":"l","μ":"m","ν":"n","ξ":"3","ο":"o","π":"p","ρ":"r","ς":"s","σ":"s","τ":"t","υ":"y","φ":"f","χ":"x","ψ":"ps","ω":"w","ϊ":"i","ϋ":"y","ό":"o","ύ":"y","ώ":"w","Ё":"Yo","Ђ":"DJ","Є":"Ye","І":"I","Ї":"Yi","Ј":"J","Љ":"LJ","Њ":"NJ","Ћ":"C","Џ":"DZ","А":"A","Б":"B","В":"V","Г":"G","Д":"D","Е":"E","Ж":"Zh","З":"Z","И":"I","Й":"J","К":"K","Л":"L","М":"M","Н":"N","О":"O","П":"P","Р":"R","С":"S","Т":"T","У":"U","Ф":"F","Х":"H","Ц":"C","Ч":"Ch","Ш":"Sh","Щ":"Sh","Ъ":"U","Ы":"Y","Ь":"","Э":"E","Ю":"Yu","Я":"Ya","а":"a","б":"b","в":"v","г":"g","д":"d","е":"e","ж":"zh","з":"z","и":"i","й":"j","к":"k","л":"l","м":"m","н":"n","о":"o","п":"p","р":"r","с":"s","т":"t","у":"u","ф":"f","х":"h","ц":"c","ч":"ch","ш":"sh","щ":"sh","ъ":"u","ы":"y","ь":"","э":"e","ю":"yu","я":"ya","ё":"yo","ђ":"dj","є":"ye","і":"i","ї":"yi","ј":"j","љ":"lj","њ":"nj","ћ":"c","ѝ":"u","џ":"dz","Ґ":"G","ґ":"g","Ғ":"GH","ғ":"gh","Қ":"KH","қ":"kh","Ң":"NG","ң":"ng","Ү":"UE","ү":"ue","Ұ":"U","ұ":"u","Һ":"H","һ":"h","Ә":"AE","ә":"ae","Ө":"OE","ө":"oe","Ա":"A","Բ":"B","Գ":"G","Դ":"D","Ե":"E","Զ":"Z","Է":"E'","Ը":"Y'","Թ":"T'","Ժ":"JH","Ի":"I","Լ":"L","Խ":"X","Ծ":"C'","Կ":"K","Հ":"H","Ձ":"D'","Ղ":"GH","Ճ":"TW","Մ":"M","Յ":"Y","Ն":"N","Շ":"SH","Չ":"CH","Պ":"P","Ջ":"J","Ռ":"R'","Ս":"S","Վ":"V","Տ":"T","Ր":"R","Ց":"C","Փ":"P'","Ք":"Q'","Օ":"O''","Ֆ":"F","և":"EV","ء":"a","آ":"aa","أ":"a","ؤ":"u","إ":"i","ئ":"e","ا":"a","ب":"b","ة":"h","ت":"t","ث":"th","ج":"j","ح":"h","خ":"kh","د":"d","ذ":"th","ر":"r","ز":"z","س":"s","ش":"sh","ص":"s","ض":"dh","ط":"t","ظ":"z","ع":"a","غ":"gh","ف":"f","ق":"q","ك":"k","ل":"l","م":"m","ن":"n","ه":"h","و":"w","ى":"a","ي":"y","ً":"an","ٌ":"on","ٍ":"en","َ":"a","ُ":"u","ِ":"e","ْ":"","٠":"0","١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","پ":"p","چ":"ch","ژ":"zh","ک":"k","گ":"g","ی":"y","۰":"0","۱":"1","۲":"2","۳":"3","۴":"4","۵":"5","۶":"6","۷":"7","۸":"8","۹":"9","฿":"baht","ა":"a","ბ":"b","გ":"g","დ":"d","ე":"e","ვ":"v","ზ":"z","თ":"t","ი":"i","კ":"k","ლ":"l","მ":"m","ნ":"n","ო":"o","პ":"p","ჟ":"zh","რ":"r","ს":"s","ტ":"t","უ":"u","ფ":"f","ქ":"k","ღ":"gh","ყ":"q","შ":"sh","ჩ":"ch","ც":"ts","ძ":"dz","წ":"ts","ჭ":"ch","ხ":"kh","ჯ":"j","ჰ":"h","Ṣ":"S","ṣ":"s","Ẁ":"W","ẁ":"w","Ẃ":"W","ẃ":"w","Ẅ":"W","ẅ":"w","ẞ":"SS","Ạ":"A","ạ":"a","Ả":"A","ả":"a","Ấ":"A","ấ":"a","Ầ":"A","ầ":"a","Ẩ":"A","ẩ":"a","Ẫ":"A","ẫ":"a","Ậ":"A","ậ":"a","Ắ":"A","ắ":"a","Ằ":"A","ằ":"a","Ẳ":"A","ẳ":"a","Ẵ":"A","ẵ":"a","Ặ":"A","ặ":"a","Ẹ":"E","ẹ":"e","Ẻ":"E","ẻ":"e","Ẽ":"E","ẽ":"e","Ế":"E","ế":"e","Ề":"E","ề":"e","Ể":"E","ể":"e","Ễ":"E","ễ":"e","Ệ":"E","ệ":"e","Ỉ":"I","ỉ":"i","Ị":"I","ị":"i","Ọ":"O","ọ":"o","Ỏ":"O","ỏ":"o","Ố":"O","ố":"o","Ồ":"O","ồ":"o","Ổ":"O","ổ":"o","Ỗ":"O","ỗ":"o","Ộ":"O","ộ":"o","Ớ":"O","ớ":"o","Ờ":"O","ờ":"o","Ở":"O","ở":"o","Ỡ":"O","ỡ":"o","Ợ":"O","ợ":"o","Ụ":"U","ụ":"u","Ủ":"U","ủ":"u","Ứ":"U","ứ":"u","Ừ":"U","ừ":"u","Ử":"U","ử":"u","Ữ":"U","ữ":"u","Ự":"U","ự":"u","Ỳ":"Y","ỳ":"y","Ỵ":"Y","ỵ":"y","Ỷ":"Y","ỷ":"y","Ỹ":"Y","ỹ":"y","–":"-","‘":"'","’":"'","“":"\\"","”":"\\"","„":"\\"","†":"+","•":"*","…":"...","₠":"ecu","₢":"cruzeiro","₣":"french franc","₤":"lira","₥":"mill","₦":"naira","₧":"peseta","₨":"rupee","₩":"won","₪":"new shequel","₫":"dong","€":"euro","₭":"kip","₮":"tugrik","₯":"drachma","₰":"penny","₱":"peso","₲":"guarani","₳":"austral","₴":"hryvnia","₵":"cedi","₸":"kazakhstani tenge","₹":"indian rupee","₺":"turkish lira","₽":"russian ruble","₿":"bitcoin","℠":"sm","™":"tm","∂":"d","∆":"delta","∑":"sum","∞":"infinity","♥":"love","元":"yuan","円":"yen","﷼":"rial","ﻵ":"laa","ﻷ":"laa","ﻹ":"lai","ﻻ":"la"}`
						),
						n = JSON.parse(
							'{"bg":{"Й":"Y","Ц":"Ts","Щ":"Sht","Ъ":"A","Ь":"Y","й":"y","ц":"ts","щ":"sht","ъ":"a","ь":"y"},"de":{"Ä":"AE","ä":"ae","Ö":"OE","ö":"oe","Ü":"UE","ü":"ue","ß":"ss","%":"prozent","&":"und","|":"oder","∑":"summe","∞":"unendlich","♥":"liebe"},"es":{"%":"por ciento","&":"y","<":"menor que",">":"mayor que","|":"o","¢":"centavos","£":"libras","¤":"moneda","₣":"francos","∑":"suma","∞":"infinito","♥":"amor"},"fr":{"%":"pourcent","&":"et","<":"plus petit",">":"plus grand","|":"ou","¢":"centime","£":"livre","¤":"devise","₣":"franc","∑":"somme","∞":"infini","♥":"amour"},"pt":{"%":"porcento","&":"e","<":"menor",">":"maior","|":"ou","¢":"centavo","∑":"soma","£":"libra","∞":"infinito","♥":"amor"},"uk":{"И":"Y","и":"y","Й":"Y","й":"y","Ц":"Ts","ц":"ts","Х":"Kh","х":"kh","Щ":"Shch","щ":"shch","Г":"H","г":"h"},"vi":{"Đ":"D","đ":"d"},"da":{"Ø":"OE","ø":"oe","Å":"AA","å":"aa","%":"procent","&":"og","|":"eller","$":"dollar","<":"mindre end",">":"større end"},"nb":{"&":"og","Å":"AA","Æ":"AE","Ø":"OE","å":"aa","æ":"ae","ø":"oe"},"it":{"&":"e"},"nl":{"&":"en"},"sv":{"&":"och","Å":"AA","Ä":"AE","Ö":"OE","å":"aa","ä":"ae","ö":"oe"}}'
						);
					function i(o, s) {
						if (typeof o != 'string') throw new Error('slugify: string argument expected');
						s = typeof s == 'string' ? { replacement: s } : s || {};
						var a = n[s.locale] || {},
							l = s.replacement === void 0 ? '-' : s.replacement,
							u = s.trim === void 0 ? !0 : s.trim,
							f = o
								.normalize()
								.split('')
								.reduce(function (d, c) {
									var b = a[c];
									return (
										b === void 0 && (b = r[c]),
										b === void 0 && (b = c),
										b === l && (b = ' '),
										d + b.replace(s.remove || /[^\w\s$*_+~.()'"!\-:@]+/g, '')
									);
								}, '');
						return (
							s.strict && (f = f.replace(/[^A-Za-z0-9\s]/g, '')),
							u && (f = f.trim()),
							(f = f.replace(/\s+/g, l)),
							s.lower && (f = f.toLowerCase()),
							f
						);
					}
					return (
						(i.extend = function (o) {
							Object.assign(r, o);
						}),
						i
					);
				});
			})(er)),
		er.exports
	);
}
var al = ol();
const Bn = /* @__PURE__ */ nl(al);
var sl = (e, t, r) => t(w(r).id),
	ll = /* @__PURE__ */ ae(
		'<a><div class="icon svelte-cugc62"><!></div> <span class="label svelte-cugc62"> </span></a>'
	),
	ul = /* @__PURE__ */ ae('<nav class="mobile-bottom-nav" part="mobile-bottom-nav"></nav>');
const cl = {
	hash: 'svelte-cugc62',
	code: '.nav-item.svelte-cugc62 {flex:1;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:var(--ai-size-4);text-decoration:none;color:var(--ai-color-neutral-600);transition:color 0.2s ease;height:100%;}.nav-item.active.svelte-cugc62 {color:var(--ai-color-neutral-900);}.nav-item.active.svelte-cugc62 .icon:where(.svelte-cugc62) {background-color:var(--ai-color-neutral-200);border-radius:var(--ai-size-8);}.nav-item.svelte-cugc62:hover {color:var(--ai-color-neutral-700);}.icon.svelte-cugc62 {width:var(--ai-size-24);height:var(--ai-size-24);margin-bottom:var(--ai-size-2);display:flex;align-items:center;justify-content:center;padding:var(--ai-size-2);transition:background-color 0.2s ease;}.label.svelte-cugc62 {font-size:0.6875rem; /* 11px */font-weight:500;line-height:1;text-align:center;}'
};
function Wi(e, t) {
	J(t, !0), vt(e, cl);
	const [r, n] = Kt(),
		i = () => Ie(ht, '$selectedItem', r);
	let o = y(t, 'onNavItemClick', 7),
		s = y(t, 'showAppIcons', 7, !0),
		a = y(t, 'apiUrl', 7),
		l = y(t, 'chatUrl', 7),
		u = y(t, 'consoleUrl', 7),
		f = y(t, 'discoverUrl', 7);
	const d = Zi(),
		c = [
			{
				id: 'discover',
				label: 'Discover',
				icon: sn,
				href: f() || d.discover
			},
			{
				id: 'chat',
				label: 'Chat',
				icon: ln,
				href: l() || d.chat
			},
			{
				id: 'console',
				label: 'Console',
				icon: un,
				href: u() || d.console
			},
			{
				id: 'api',
				label: 'API',
				icon: cn,
				href: a() || d.api
			}
		];
	function b(v) {
		var m;
		if (
			(v !== 'discover' &&
				typeof window < 'u' &&
				sessionStorage.setItem('navigatingToMainNav', 'true'),
			Te.setSelectedItem(v),
			v !== 'discover')
		)
			try {
				Promise.resolve()
					.then(() => ur)
					.then(({ selectedSubNavItemTitle: h }) => {
						h.set(''),
							typeof window < 'u' &&
								(sessionStorage.removeItem('subNav-selectedTitle'),
								sessionStorage.removeItem('subNav-selectedId')),
							setTimeout(() => {
								const g = document.getElementById('main-content');
								if (g) {
									const z = g.querySelector('h2[id], h3[id], h4[id], h5[id], h6[id]');
									if (z && z.textContent) {
										const A = z.textContent.trim();
										h.set(A),
											typeof window < 'u' &&
												sessionStorage.setItem('subNav-selectedTitle', JSON.stringify(A));
									}
								}
							}, 200);
					})
					.catch((h) => {
						console.log('[MobileBottomNav] Error clearing header state:', h);
					});
			} catch (h) {
				console.log('[MobileBottomNav] Import failed for clearing state:', h);
			}
		else
			try {
				Promise.resolve()
					.then(() => ur)
					.then(({ selectedSubNavItemTitle: h }) => {
						h.set(''),
							typeof window < 'u' &&
								(sessionStorage.removeItem('subNav-selectedTitle'),
								sessionStorage.removeItem('subNav-selectedId'));
					})
					.catch((h) => {
						console.log('[MobileBottomNav] Error clearing header state for Discover:', h);
					});
			} catch (h) {
				console.log('[MobileBottomNav] Import failed for clearing Discover state:', h);
			}
		(m = o()) == null || m({ item: v, expanded: !1 });
	}
	var p = It(),
		k = Ke(p);
	{
		var S = (v) => {
			var m = ul();
			Ti(
				m,
				21,
				() => c,
				Ta,
				(h, g) => {
					var z = ll();
					let A;
					z.__click = [sl, b, g];
					var Y = C(z),
						M = C(Y);
					const P = /* @__PURE__ */ te(() => i() === w(g).id);
					Oa(
						M,
						() => w(g).icon,
						(N, $) => {
							$(N, {
								get isSelected() {
									return w(P);
								}
							});
						}
					),
						E(Y);
					var X = ie(Y, 2),
						q = C(X, !0);
					E(X),
						E(z),
						we(
							(N, $, V) => {
								Z(z, 'href', w(g).href),
									(A = me(z, 1, N, 'svelte-cugc62', A, $)),
									Z(z, 'part', V),
									Vt(q, w(g).label);
							},
							[
								() => `nav-item ${Bn(w(g).id)}-item`,
								() => ({ active: i() === w(g).id }),
								() => `${Bn(w(g).id)}-item`
							]
						),
						L(h, z);
				}
			),
				E(m),
				L(v, m);
		};
		oe(k, (v) => {
			s() && v(S);
		});
	}
	L(e, p);
	var I = K({
		get onNavItemClick() {
			return o();
		},
		set onNavItemClick(v) {
			o(v), _();
		},
		get showAppIcons() {
			return s();
		},
		set showAppIcons(v = !0) {
			s(v), _();
		},
		get apiUrl() {
			return a();
		},
		set apiUrl(v) {
			a(v), _();
		},
		get chatUrl() {
			return l();
		},
		set chatUrl(v) {
			l(v), _();
		},
		get consoleUrl() {
			return u();
		},
		set consoleUrl(v) {
			u(v), _();
		},
		get discoverUrl() {
			return f();
		},
		set discoverUrl(v) {
			f(v), _();
		}
	});
	return n(), I;
}
Zt(['click']);
G(
	Wi,
	{
		onNavItemClick: {},
		showAppIcons: {},
		apiUrl: {},
		chatUrl: {},
		consoleUrl: {},
		discoverUrl: {}
	},
	[],
	[],
	!0
);
function dl(e, t) {
	J(t, !0);
	let r = y(t, 'onNavItemClick', 7),
		n = y(t, 'showAppIcons', 7, !0),
		i = y(t, 'apiUrl', 7),
		o = y(t, 'chatUrl', 7),
		s = y(t, 'consoleUrl', 7),
		a = y(t, 'discoverUrl', 7);
	return (
		Wi(e, {
			get onNavItemClick() {
				return r();
			},
			get showAppIcons() {
				return n();
			},
			get apiUrl() {
				return i();
			},
			get chatUrl() {
				return o();
			},
			get consoleUrl() {
				return s();
			},
			get discoverUrl() {
				return a();
			}
		}),
		K({
			get onNavItemClick() {
				return r();
			},
			set onNavItemClick(l) {
				r(l), _();
			},
			get showAppIcons() {
				return n();
			},
			set showAppIcons(l = !0) {
				n(l), _();
			},
			get apiUrl() {
				return i();
			},
			set apiUrl(l) {
				i(l), _();
			},
			get chatUrl() {
				return o();
			},
			set chatUrl(l) {
				o(l), _();
			},
			get consoleUrl() {
				return s();
			},
			set consoleUrl(l) {
				s(l), _();
			},
			get discoverUrl() {
				return a();
			},
			set discoverUrl(l) {
				a(l), _();
			}
		})
	);
}
customElements.define(
	'graymatter-mobile-bottom-nav',
	G(
		dl,
		{
			onNavItemClick: {},
			showAppIcons: {},
			apiUrl: {},
			chatUrl: {},
			consoleUrl: {},
			discoverUrl: {}
		},
		[],
		[],
		!0
	)
);
var fl = /* @__PURE__ */ W(
	'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M6 18L18 6M6 6L18 18" stroke="#585C63" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg>'
);
function Gi(e) {
	var t = fl();
	L(e, t);
}
G(Gi, {}, [], [], !0);
var vl = (e, t) => {
		(e.key === 'Enter' || e.key === ' ') && t();
	},
	hl = /* @__PURE__ */ ae(
		'<div class="mobile-overlay" role="button" tabindex="0" aria-label="Close navigation menu (Escape key supported)" part="mobile-overlay"></div>'
	),
	pl = /* @__PURE__ */ ae('<a part="logo-link" class="logo-link svelte-130p86v"><!></a>'),
	bl = /* @__PURE__ */ ae(
		'<div class="subnav-section svelte-130p86v" part="subnav-section"><!></div>'
	),
	gl = /* @__PURE__ */ ae(
		'<div class="no-subnav-message svelte-130p86v" part="no-subnav-message"><p class="svelte-130p86v">Select a section from the bottom navigation</p></div>'
	),
	ml = /* @__PURE__ */ ae(
		'<!> <div part="mobile-side-menu"><div class="menu-header svelte-130p86v" part="menu-header"><div class="logo-container svelte-130p86v" part="logo-container"><!></div> <button class="close-button svelte-130p86v" aria-label="Close navigation menu" part="close-button"><!></button></div> <div class="menu-content svelte-130p86v" part="menu-content"><!></div></div>',
		1
	);
const wl = {
	hash: 'svelte-130p86v',
	code: '.no-subnav-message.svelte-130p86v {padding:var(--ai-size-16);text-align:center;color:var(--ai-color-steel-600);font-size:var(--ai-size-14);}.no-subnav-message.svelte-130p86v p:where(.svelte-130p86v) {margin:0;}.mobile-side-menu.svelte-130p86v {position:fixed;top:0;left:0;bottom:0;width:15rem;max-width:85vw;background:var(--ai-color-white);transform:translateX(-100%);transition:transform 0.3s ease;z-index:var(--ai-layer-50);flex-direction:column;box-shadow:var(--ai-size-2) 0 var(--ai-size-10) #0000001a;}.mobile-side-menu.open.svelte-130p86v {transform:translateX(0);}.menu-header.svelte-130p86v {display:flex;align-items:center;justify-content:space-between;padding:var(--ai-size-16) var(--ai-size-20);border-bottom:0.0625rem solid var(--ai-color-neutral-200);}.mobile-side-menu.svelte-130p86v .logo-container:where(.svelte-130p86v) {display:flex;}.logo-link.svelte-130p86v {display:flex;}.close-button.svelte-130p86v {display:flex;align-items:center;justify-content:center;width:var(--ai-size-32);height:var(--ai-size-32);border:none;background:none;color:var(--ai-color-neutral-700);cursor:pointer;border-radius:var(--ai-size-6);transition:background-color 0.2s ease;}.close-button.svelte-130p86v:hover {background-color:var(--ai-color-neutral-100);}.menu-content.svelte-130p86v {flex:1;overflow-y:auto;padding:0;}.subnav-section.svelte-130p86v {padding:var(--ai-size-16) 0;overflow-y:auto;max-height:calc(100vh - 4rem); /* leave space for header/close */}'
};
function Xi(e, t) {
	J(t, !0), vt(e, wl);
	const [r, n] = Kt(),
		i = () => Ie($e, '$isExpanded', r),
		o = () => Ie(ht, '$selectedItem', r);
	let s,
		a = null,
		l = y(t, 'logoLinkUrl', 7),
		u = y(t, 'logoLinkLabel', 7);
	function f(N) {
		if (((a = N.detail.hash), d(), a)) {
			const V = document.getElementById(a);
			if (V) {
				const se = document.getElementById('main-content-mobile'),
					ce = se ? se.querySelector(`[id="${a}"]`) : null;
				if (ce && se) {
					const le = ce.getBoundingClientRect(),
						Q = se.getBoundingClientRect(),
						ue = se.scrollTop + le.top - Q.top;
					se.scrollTo({ top: ue, behavior: 'auto' }),
						setTimeout(() => {
							var Fe;
							window.dispatchEvent(
								new CustomEvent('sectionVisible', {
									detail: { title: (Fe = ce.textContent) == null ? void 0 : Fe.trim(), id: ce.id }
								})
							);
						}, 100);
				} else {
					let le = V.parentElement;
					for (; le && le !== document.body; ) {
						const Q = window.getComputedStyle(le);
						if (Q.overflowY === 'auto' || Q.overflowY === 'scroll') break;
						le = le.parentElement;
					}
					le && le !== document.body
						? document.getElementById('main-content')
							? (V.scrollIntoView({ behavior: 'auto', block: 'start' }),
								setTimeout(() => {
									var ue;
									window.dispatchEvent(
										new CustomEvent('sectionVisible', {
											detail: { title: (ue = V.textContent) == null ? void 0 : ue.trim(), id: V.id }
										})
									);
								}, 100))
							: V.scrollIntoView({ behavior: 'auto', block: 'start' })
						: V.scrollIntoView({ behavior: 'auto', block: 'start' });
				}
			}
			a = null;
		}
	}
	Jt(() => {
		window.addEventListener('mobileAnchorNavigate', f);
		const N = () => {
			d();
		};
		window.addEventListener('closeMobileMenu', N);
		const $ = (se) => {
			const ce = se,
				{ isMobile: le } = ce.detail;
			le && Te.setExpanded(!1);
		};
		window.addEventListener('layoutTransition', $);
		const V = (se) => {
			const ce = document.activeElement;
			se.key === 'Escape' && i() && !(ce && ce.closest('.profile-context-menu')) && d();
		};
		return (
			window.addEventListener('keydown', V),
			() => {
				window.removeEventListener('mobileAnchorNavigate', f),
					window.removeEventListener('closeMobileMenu', N),
					window.removeEventListener('layoutTransition', $),
					window.removeEventListener('keydown', V);
			}
		);
	});
	function d() {
		Te.setExpanded(!1);
	}
	var c = ml(),
		b = Ke(c);
	{
		var p = (N) => {
			var $ = hl();
			($.__click = d), ($.__keydown = [vl, d]), L(N, $);
		};
		oe(b, (N) => {
			i() && N(p);
		});
	}
	var k = ie(b, 2);
	let S;
	var I = C(k),
		v = C(I),
		m = C(v);
	{
		var h = (N) => {
				var $ = pl(),
					V = C($);
				Rt(V, { width: 75, height: 22 }),
					E($),
					we(() => {
						Z($, 'href', l()), Z($, 'aria-label', u());
					}),
					L(N, $);
			},
			g = (N) => {
				Rt(N, { width: 75, height: 22 });
			};
		oe(m, (N) => {
			l() ? N(h) : N(g, !1);
		});
	}
	E(v);
	var z = ie(v, 2);
	z.__click = d;
	var A = C(z);
	Gi(A), E(z), E(I);
	var Y = ie(I, 2),
		M = C(Y);
	{
		var P = (N) => {
				var $ = bl(),
					V = C($);
				gr(V, t, 'default', {}), E($), L(N, $);
			},
			X = (N) => {
				var $ = gl();
				L(N, $);
			};
		oe(M, (N) => {
			o() && o() !== 'discover' ? N(P) : N(X, !1);
		});
	}
	E(Y),
		E(k),
		it(
			k,
			(N) => (s = N),
			() => s
		),
		we(
			(N) => (S = me(k, 1, 'mobile-side-menu svelte-130p86v', null, S, N)),
			[() => ({ open: i() })]
		),
		L(e, c);
	var q = K({
		get logoLinkUrl() {
			return l();
		},
		set logoLinkUrl(N) {
			l(N), _();
		},
		get logoLinkLabel() {
			return u();
		},
		set logoLinkLabel(N) {
			u(N), _();
		}
	});
	return n(), q;
}
Zt(['click', 'keydown']);
G(Xi, { logoLinkUrl: {}, logoLinkLabel: {} }, ['default'], [], !0);
function _l(e, t) {
	J(t, !0);
	let r = y(t, 'logoLinkUrl', 7),
		n = y(t, 'logoLinkLabel', 7);
	return (
		Xi(e, {
			get logoLinkUrl() {
				return r();
			},
			get logoLinkLabel() {
				return n();
			},
			children: (i, o) => {
				var s = It(),
					a = Ke(s);
				gr(a, t, 'default', {}), L(i, s);
			},
			$$slots: { default: !0 }
		}),
		K({
			get logoLinkUrl() {
				return r();
			},
			set logoLinkUrl(i) {
				r(i), _();
			},
			get logoLinkLabel() {
				return n();
			},
			set logoLinkLabel(i) {
				n(i), _();
			}
		})
	);
}
customElements.define(
	'graymatter-mobile-side-menu',
	G(_l, { logoLinkUrl: {}, logoLinkLabel: {} }, ['default'], [], !0)
);
function yl() {
	Te.toggleExpanded();
}
var kl = /* @__PURE__ */ ae(
	'<nav class="mobile-top-nav svelte-1ndhol0" part="mobile-top-nav"><button aria-label="Open navigation menu" part="menu-button"><!></button> <div class="brand svelte-1ndhol0"><!> <span class="brand-text svelte-1ndhol0"> </span></div> <!></nav>'
);
const xl = {
	hash: 'svelte-1ndhol0',
	code: `.menu-button.svelte-1ndhol0 {display:flex;align-items:center;justify-content:center;border:none;background:none;cursor:pointer;border-radius:var(--ai-size-8);transition:background-color 0.2s ease;}.menu-button.svelte-1ndhol0:disabled,
  .menu-button.disabled.svelte-1ndhol0 {cursor:not-allowed;opacity:0.5;background-color:transparent;}.mobile-top-nav.svelte-1ndhol0 .menu-button:where(.svelte-1ndhol0) {width:var(--ai-size-40);height:var(--ai-size-40);color:var(--ai-color-neutral-700);}.menu-button.svelte-1ndhol0:hover {background-color:var(--ai-color-neutral-100);}

  /* MobileTopNav specific disabled state */.mobile-top-nav.svelte-1ndhol0 .menu-button.disabled:where(.svelte-1ndhol0),
  .mobile-top-nav.svelte-1ndhol0 .menu-button:where(.svelte-1ndhol0):disabled {cursor:not-allowed;opacity:0.5;background-color:transparent;}.brand.svelte-1ndhol0 {display:flex;align-items:center;gap:var(--ai-size-12);flex:1;margin-left:var(--ai-size-8);}.brand-text.svelte-1ndhol0 {color:var(--ai-color-neutral-900);font-size:var(--ai-size-16);font-weight:600;line-height:1;}.menu-button.svelte-1ndhol0 {display:flex;padding:var(--ai-size-4);justify-content:center;align-items:center;background:none;border:none;cursor:pointer;opacity:1;transition:opacity 0.2s ease;will-change:opacity;}.menu-button.disabled.svelte-1ndhol0 {cursor:not-allowed;opacity:0.5;will-change:opacity;}.menu-button.svelte-1ndhol0:focus {outline:none;}`
};
function Qi(e, t) {
	J(t, !0), vt(e, xl);
	const [r, n] = Kt(),
		i = () => Ie(ct, '$selectedSubNavItemTitle', r),
		o = () => Ie(an, '$isMenuButtonDisabled', r);
	let s = y(t, 'profileMenuData', 7);
	Jt(() => {
		const v = (g) => {
				if (window.innerWidth <= 640) return;
				const z = g;
				z.detail &&
					z.detail.title &&
					(ct.set(z.detail.title),
					typeof window < 'u' &&
						sessionStorage.setItem('subNav-selectedTitle', JSON.stringify(z.detail.title)));
			},
			m = () => {
				if (typeof window > 'u') return;
				const g = document.getElementById('main-content');
				if (!g) return;
				const z = g.querySelector('h2[id], h3[id], h4[id], h5[id], h6[id]');
				if (z && z.textContent) {
					const A = z.textContent.trim();
					i() ||
						(ct.set(A),
						typeof window < 'u' &&
							sessionStorage.setItem('subNav-selectedTitle', JSON.stringify(A)));
				}
			};
		window.addEventListener('sectionVisible', v);
		const h = sessionStorage.getItem('subNav-selectedTitle');
		return (
			(!h || h === '""' || h === 'null') &&
				setTimeout(() => {
					m();
				}, 100),
			() => {
				window.removeEventListener('sectionVisible', v);
			}
		);
	});
	var a = kl(),
		l = C(a);
	let u;
	l.__click = [yl];
	var f = C(l);
	dn(f, {}), E(l);
	var d = ie(l, 2),
		c = C(d);
	Rt(c, { width: 75, height: 22 });
	var b = ie(c, 2),
		p = C(b, !0);
	E(b), E(d);
	var k = ie(d, 2);
	{
		var S = (v) => {
			const m = /* @__PURE__ */ te(() => {
					var g;
					return (g = s()) == null ? void 0 : g.userMeta;
				}),
				h = /* @__PURE__ */ te(() => {
					var g;
					return (g = s()) == null ? void 0 : g.menuItems;
				});
			fn(v, {
				get userMeta() {
					return w(m);
				},
				get menuItems() {
					return w(h);
				},
				ariaLabel: 'Open profile menu',
				menuAlign: 'mobile'
			});
		};
		oe(k, (v) => {
			s() && v(S);
		});
	}
	E(a),
		we(
			(v) => {
				(u = me(l, 1, 'menu-button svelte-1ndhol0', null, u, v)),
					(l.disabled = o()),
					Vt(p, i() || '');
			},
			[() => ({ disabled: o() })]
		),
		L(e, a);
	var I = K({
		get profileMenuData() {
			return s();
		},
		set profileMenuData(v) {
			s(v), _();
		}
	});
	return n(), I;
}
Zt(['click']);
G(Qi, { profileMenuData: {} }, [], [], !0);
function El(e, t) {
	J(t, !0);
	let r = y(t, 'profileMenuData', 7);
	return (
		Qi(e, {
			get profileMenuData() {
				return r();
			}
		}),
		K({
			get profileMenuData() {
				return r();
			},
			set profileMenuData(n) {
				r(n), _();
			}
		})
	);
}
customElements.define('graymatter-mobile-top-nav', G(El, { profileMenuData: {} }, [], [], !0));
export {
	ls as Button,
	vs as DesktopHeader,
	el as DesktopSideNav,
	rl as Logo,
	dl as MobileBottomNav,
	_l as MobileSideMenu,
	El as MobileTopNav
};
