<script>
	import Textarea from '$lib/components/common/Textarea.svelte';
	
	let systemPrompt = `You are a helpful AI assistant.

Please follow these guidelines:
- Be concise and clear
- Provide accurate information
- Ask for clarification when needed

Example usage:
User: "What is the weather like?"
Assistant: "I don't have access to real-time weather data. Could you please specify your location and I can help you find weather information?"`;

	let description = `This is a test model for demonstrating textarea functionality.

It supports:
- Multi-line text
- Auto-resize based on content
- Proper line break preservation`;
</script>

<div class="container mx-auto p-8 max-w-4xl">
	<h1 class="text-2xl font-bold mb-6">Textarea Test Page</h1>
	
	<div class="space-y-6">
		<div>
			<h2 class="text-lg font-semibold mb-2">System Prompt (Auto-resize enabled)</h2>
			<Textarea
				className="text-sm w-full bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg px-3 py-2 outline-none resize-none"
				placeholder="Write your model system prompt content here..."
				autoResize={true}
				minHeight={100}
				maxHeight={400}
				bind:value={systemPrompt}
			/>
		</div>
		
		<div>
			<h2 class="text-lg font-semibold mb-2">Description (Auto-resize enabled)</h2>
			<Textarea
				className="text-sm w-full bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg px-3 py-2 outline-none resize-none"
				placeholder="Add a short description..."
				autoResize={true}
				minHeight={60}
				maxHeight={200}
				bind:value={description}
			/>
		</div>
		
		<div>
			<h2 class="text-lg font-semibold mb-2">Regular Textarea (No auto-resize)</h2>
			<Textarea
				className="text-sm w-full bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg px-3 py-2 outline-none resize-none h-32"
				placeholder="This textarea has fixed height..."
				bind:value=""
			/>
		</div>
	</div>
	
	<div class="mt-8 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
		<h3 class="font-semibold mb-2">Current Values:</h3>
		<div class="space-y-2 text-sm">
			<div>
				<strong>System Prompt:</strong>
				<pre class="mt-1 p-2 bg-white dark:bg-gray-900 rounded border text-xs overflow-auto">{systemPrompt}</pre>
			</div>
			<div>
				<strong>Description:</strong>
				<pre class="mt-1 p-2 bg-white dark:bg-gray-900 rounded border text-xs overflow-auto">{description}</pre>
			</div>
		</div>
	</div>
</div>
