.only-icon-button-stroke-svg {
	stroke: var(--ai-color-steel-800);
}

.only-icon-button-fill-svg {
	fill: var(--ai-color-steel-800);
}

.only-icon-button {
	border-radius: var(--ai-radius-small);

	/* Setting border-width prevents the button from "moving" when the state changes. */
	border-width: 2px;
	border-color: transparent;
}

.only-icon-button:hover,
.only-icon-button:active,
.only-icon-button:focus {
	background: var(--ai-color-steel-100);

	.only-icon-button-stroke-svg {
		stroke: var(--ai-color-black);
	}
	.only-icon-button-fill-svg {
		fill: var(--ai-color-black);
	}
}

.only-icon-button:focus {
	border: 2px solid var(--ai-color-blue-600);
}
