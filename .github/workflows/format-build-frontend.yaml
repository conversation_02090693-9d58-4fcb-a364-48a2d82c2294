name: Frontend Build

on:
  push:
    branches:
      - main
      - dev
  pull_request:
    branches:
      - main
      - dev

env:
  PUBLIC_DATADOG_APP_ID: ${{ vars.PUBLIC_DATADOG_APP_ID }},
  PUBLIC_DATADOG_CLIENT_TOKEN: ${{ vars.PUBLIC_DATADOG_CLIENT_TOKEN }},
  PUBLIC_DATADOG_BROWSERLOGS_CLIENT_TOKEN: ${{ vars.PUBLIC_DATADOG_BROWSERLOGS_CLIENT_TOKEN }},
  PUBLIC_DATADOG_SERVICE: ${{ vars.PUBLIC_DATADOG_SERVICE }},
  NODE_OPTIONS: --max-old-space-size=4096

jobs:
  build:
    name: 'Format & Build Frontend'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22' # Or specify any other version you want to use

      - name: Install Dependencies
        run: npm install

      - name: Format Frontend
        run: npm run format

      - name: Run i18next
        run: npm run i18n:parse

      - name: Check for Changes After Format
        run: git diff --exit-code

      - name: Build Frontend
        run: npm run build

  test-frontend:
    name: 'Frontend Unit Tests'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Install Dependencies
        run: npm ci

      - name: Run vitest
        run: npm run test:frontend
